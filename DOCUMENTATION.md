# Documentação do Sistema - MZ MF Shareholders

Este documento serve como índice central para toda a documentação técnica do sistema de acionistas.

## 📋 Páginas Principais

### Autenticação e Acesso

- [**Autenticação**](./src/pages/auth/auth-documentation.md) - Sistema de login e controle de acesso

### Visualizações e Relatórios

- [**Gráficos**](./src/pages/charts/charts-documentation.md) - Visualizações gráficas dos dados
- [**Resumo**](./src/pages/summary/summary-documentation.md) - Página de resumo executivo
- [**Estatísticas Chave**](./src/pages/keyStatistics/key-statistics-documentation.md) - Indicadores principais
- [**Relatórios**](./src/pages/reports/reports-documentation.md) - Geração e visualização de relatórios

### Gestão de Acionistas

- [**Acionistas**](./src/pages/shareholders/shareholders-documentation.md) - Listagem e gestão de acionistas
- [**Base Acionária**](./src/pages/ownership/ownership-documentation.md) - Controle de participações
- [**Agrupamento Inteligente**](./src/pages/smartGrouping/smart-grouping-documentation.md) - Sistema de agrupamento automático

### Histórico e Monitoramento

- [**Histórico**](./src/pages/history/history-documentation.md) - Histórico de transações e alterações
- [**Histórico de Exportações**](./src/pages/export-history/export-history-documentation.md) - Controle de exportações realizadas
- [**Monitoramento**](./src/pages/monitoring/monitoring-documentation.md) - Dashboard de monitoramento do sistema

### Sistema

- [**Logout**](./src/pages/logout/logout-documentation.md) - Processo de saída do sistema

## 📊 ShareholderOverview - Visões Especializadas

Esta seção contém as diferentes implementações da página de visão geral de acionistas:

- [**Visão Simples**](./src/pages/shareholderOverview/shareholder-overview-simple.md) - Interface simplificada para consulta básica
- [**Visão Agrupada**](./src/pages/shareholderOverview/shareholder-overview-grouped.md) - Visualização com agrupamentos avançados
- [**Versão Beta**](./src/pages/shareholderOverview/shareholder-overview-beta.md) - Funcionalidades experimentais em teste

## 🔧 Como Contribuir com a Documentação

### Padrões de Nomenclatura

- Use **kebab-case** para nomes de arquivos: `auth-documentation.md`
- Para páginas com variações, use sufixos específicos: `shareholder-overview-simple.md`

### Localização

- Mantenha a documentação **próxima ao código** que ela documenta
- Coloque os arquivos `.md` no diretório da respectiva página em `src/pages/`

### Estrutura dos Documentos

Cada documento de página deve conter:

1. **Visão Geral** - Propósito e funcionalidade principal
2. **Componentes Técnicos** - Lista dos componentes reais utilizados
3. **Fluxo de Dados** - Como os dados fluem na página
4. **Estados e Interações** - Estados possíveis e interações do usuário
5. **Integrações** - APIs e serviços utilizados

### Atualizações

- Sempre atualize este índice ao **adicionar novas documentações**
- Mantenha os links relativos atualizados
- Revise a documentação quando houver **mudanças significativas** no código

## 🎯 Roadmap

Esta documentação está sendo preparada para futura migração para o **repositório centralizado de documentações do iQ**, onde será integrada com a documentação de outros projetos da organização.

---

_Última atualização: 02/06/2025_
