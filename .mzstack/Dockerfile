FROM node:20.18.3-alpine3.21

WORKDIR /app

# Copy package files first for better Docker layer caching
COPY package*.json ./
COPY .npmrc .

# Install dependencies
RUN --mount=type=secret,id=github-token echo '//npm.pkg.github.com/:_authToken=GITHUB_TOKEN' \
  | cat - .npmrc \
  | sed  -e 's@GITHUB_TOKEN@'"$(cat /run/secrets/github-token)"'@' \
  | tee .npmrc

RUN npm install

# Copy source code
COPY . .

# Expose Vite dev server port
EXPOSE 3005

# Run Vite dev server with hot reload
CMD ["npm", "run", "dev"]
