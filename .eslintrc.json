{"env": {"browser": true, "es2021": true}, "extends": ["airbnb", "prettier", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "react-hooks", "@typescript-eslint", "prettier"], "rules": {"react/no-deprecated": "off", "no-use-before-define": "off", "no-undef": "off", "react/destructuring-assignment": "off", "no-nested-ternary": "off", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react/require-default-props": "off", "react/jsx-props-no-spreading": "off", "prettier/prettier": "error", "react/jsx-filename-extension": [1, {"extensions": [".tsx", ".jsx"]}], "import/extensions": ["error", "ignorePackages", {"ts": "never", "tsx": "never", "js": "never", "jsx": "never"}], "import/prefer-default-export": "off", "import/no-unresolved": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "_"}], "no-shadow": "off", "@typescript-eslint/no-shadow": "error", "import/no-extraneous-dependencies": ["error", {"devDependencies": ["**/*.spec.tsx", "**/vitest*", "**/__tests__/*", "./vite.config.mts"]}]}, "settings": {"import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}, "react": {"version": "16.14"}}, "overrides": [{"files": ["*.js", "*.jsx"], "rules": {"@typescript-eslint/explicit-module-boundary-types": "off"}}], "ignorePatterns": ["*.js", "*.jsx"]}