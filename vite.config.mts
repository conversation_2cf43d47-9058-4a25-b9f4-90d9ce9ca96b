/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import { federation } from '@module-federation/vite'
import { z } from 'zod'
import { createEnv } from '@t3-oss/env-core'
import { dependencies } from './package.json'
// @ts-expect-error -- Allow json import
import * as tsconfig from './tsconfig.app.json'

function loadParsedEnv(mode: string) {
  const viteEnv = loadEnv(mode, process.cwd(), '')

  return createEnv({
    server: {
      MFE_APP_NAME: z.string(),
      MFE_APP_PORT: z.coerce.number().int(),
      MFE_APP_PUBLIC_PATH: z.string().url(),
      MFE_NAVBAR_URL: z.string().url(),
    },
    runtimeEnv: viteEnv,
  })
}

export default defineConfig(({ mode }) => {
  const env = loadParsedEnv(mode)

  return {
    define: {
      'process.env': {},
    },
    server: {
      host: true,
      port: env.MFE_APP_PORT,
      strictPort: true,
    },
    preview: {
      host: true,
      port: env.MFE_APP_PORT,
      strictPort: true,
    },
    base: env.MFE_APP_PUBLIC_PATH,
    plugins: [
      react(),
      tsconfigPaths(),
      federation({
        name: env.MFE_APP_NAME,
        filename: 'remoteEntry-[hash].js',
        manifest: true,
        remotes: {
          '@mz-mfe-navbar': new URL('mf-manifest.json?t=1', env.MFE_NAVBAR_URL).toString(),
        },
        shared: {
          react: {
            requiredVersion: dependencies.react,
            singleton: true,
          },
          'react-dom': {
            requiredVersion: dependencies['react-dom'],
            singleton: true,
          },
          'react-router-dom': {
            requiredVersion: dependencies['react-router-dom'],
            singleton: true,
          },
        },
      }),
    ],
    build: {
      target: 'esnext',
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: undefined,
        },
      },
    },
    optimizeDeps: {
      esbuildOptions: {
        sourcemap: true,
      },
    },
  }
})
