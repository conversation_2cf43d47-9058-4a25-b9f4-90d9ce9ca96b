# Documentação de Troubleshooting

## Erro #1 — LogToProvider deve encapsular o ThemeProvider

| **Item**             | **Detalhe**                                                                              |
| -------------------- | ---------------------------------------------------------------------------------------- |
| **Arquivo(s)**       | `src/contexts/app.tsx`                                                                   |
| **Mensagem de erro** | Erro de tema: o provider do LogTo não encontra os estilos                                |
| **Sintomas**         | Falha na aplicação dos temas, resultando em estilos incorretos ou ausentes               |
| **Causa raiz**       | O `ThemeProvider` estava fora do `LogtoProvider`, causando falhas na aplicação dos temas |
| **Solução aplicada** |

```diff
<ThemeProvider theme={theme}>
  <LogtoProvider config={config}>
    // ... existing code ...
  </LogtoProvider>
</ThemeProvider>

<LogtoProvider config={config}>
  <ThemeProvider theme={theme}>
    // ... existing code ...
  </ThemeProvider>
</LogtoProvider>
```

**Validação** | Verifiquei a aplicação dos temas após a correção
**Boas-práticas / prevenção** | Garantir que os providers estejam na ordem correta

---

## Erro #2 — Versão do VITEST

| **Item**                      | **Detalhe**                                                             |
| ----------------------------- | ----------------------------------------------------------------------- |
| **Arquivo(s)**                | `package.json`                                                          |
| **Mensagem de erro**          | Erro de tipagem devido a versões incompatíveis                          |
| **Sintomas**                  | Falhas nos testes devido a incompatibilidades de versão                 |
| **Causa raiz**                | Versões diferentes do VITEST entre os projetos                          |
| **Solução aplicada**          | Alinhar a versão do VITEST com a do projeto ENGAGEMENT                  |
| **Validação**                 | Executar `npm test` para garantir que os testes passem                  |
| **Boas-práticas / prevenção** | Manter as versões de dependências alinhadas entre projetos relacionados |

---

## Erro #3 — AuthLayout para encapsulamento de rotas

| **Item**                      | **Detalhe**                                                 |
| ----------------------------- | ----------------------------------------------------------- |
| **Arquivo(s)**                | `src/routes/protected-route.tsx`                            |
| **Mensagem de erro**          | Necessidade de encapsulamento de rotas                      |
| **Sintomas**                  | Rotas não protegidas adequadamente                          |
| **Causa raiz**                | Falta de um layout de autenticação para encapsular as rotas |
| **Solução aplicada**          | Criar um `AuthLayout` para encapsular as rotas              |
| **Validação**                 | Verificar se todas as rotas estão protegidas                |
| **Boas-práticas / prevenção** | Utilizar layouts para encapsular funcionalidades comuns     |

---

## Erro #4 — Uso de env de stg para desenvolvimento local

| **Item**                      | **Detalhe**                                                            |
| ----------------------------- | ---------------------------------------------------------------------- |
| **Arquivo(s)**                | `src/contexts/app.tsx`                                                 |
| **Mensagem de erro**          | Uso de variáveis de ambiente incorretas                                |
| **Sintomas**                  | Configurações incorretas durante o desenvolvimento                     |
| **Causa raiz**                | Falta de variáveis de ambiente locais                                  |
| **Solução aplicada**          | Incluir variáveis de ambiente locais no projeto                        |
| **Validação**                 | Testar a aplicação localmente com as novas variáveis                   |
| **Boas-práticas / prevenção** | Manter variáveis de ambiente separadas para desenvolvimento e produção |

---

## Erro #5 — Remoção de referências à pasta application

| **Item**                      | **Detalhe**                                                       |
| ----------------------------- | ----------------------------------------------------------------- |
| **Arquivo(s)**                | Vários arquivos                                                   |
| **Mensagem de erro**          | Caminhos inválidos após remoção da pasta                          |
| **Sintomas**                  | Erros de importação devido a caminhos inexistentes                |
| **Causa raiz**                | Referências a uma pasta removida                                  |
| **Solução aplicada**          | Remover todas as referências à pasta `application`                |
| **Validação**                 | Executar a aplicação para garantir que não há erros de importação |
| **Boas-práticas / prevenção** | Revisar e atualizar caminhos após remoções de pastas              |

---

## Erro #6 — Problema com react-router e react-router-dom

| **Item**                      | **Detalhe**                                                   |
| ----------------------------- | ------------------------------------------------------------- |
| **Arquivo(s)**                | `package.json`                                                |
| **Mensagem de erro**          | Problema de comunicação com o navbar                          |
| **Sintomas**                  | Falhas na navegação                                           |
| **Causa raiz**                | Incompatibilidade entre `react-router` e `react-router-dom`   |
| **Solução aplicada**          | Instalar `react-router-dom` na mesma versão do ENGAGEMENT     |
| **Validação**                 | Testar a navegação para garantir que funciona corretamente    |
| **Boas-práticas / prevenção** | Manter as bibliotecas de roteamento atualizadas e compatíveis |

---

## Erro #7 — Diferenças nos Arquivos tsconfig.json

| **Item**             | **Detalhe**                                                          |
| -------------------- | -------------------------------------------------------------------- |
| **Arquivo(s)**       | `tsconfig.json`, `tsconfig.app.json`, `tsconfig.node.json`           |
| **Mensagem de erro** | Necessidade de ajustes específicos para o contexto do `shareholders` |
| **Sintomas**         | Configurações de compilação e caminho específicas                    |
| **Causa raiz**       | Diferenças nas necessidades de configuração entre os projetos        |
| **Solução aplicada** |

- **tsconfig.app.json**: Adição de caminhos específicos e tipos adicionais para suporte a testes.
- **tsconfig.node.json**: Inclusão de `vite.config.mts` em vez de `vite.config.ts`.
  **Validação** | Verificar se a aplicação compila e executa corretamente com as novas configurações
  **Boas-práticas / prevenção** | Manter as configurações de compilação alinhadas com as necessidades específicas do projeto

---

## Erro #8: Mocks para StoreInformation e NavBar

**Item**:

- `src/__tests__/mocks/storeInformation.ts`
- `src/__tests__/mocks/navbar.tsx`
- `src/__tests__/setup.ts`
- `vitest.config.mts`

**Mensagem de erro**:

- `StoreInformation is not a constructor`
- `Failed to resolve import '@mz-mfe-navbar/app'`

**Sintomas**:

- Testes falhando devido a problemas com Module Federation
- Erro ao tentar instanciar StoreInformation
- Erro ao importar componentes via Module Federation

**Causa raiz**:

- Necessidade de mocks para componentes externos e Module Federation
- O Vite tenta resolver as importações antes dos mocks serem aplicados

**Solução aplicada**:

1. Criar mocks centralizados em `__tests__/mocks`:
   - `storeInformation.ts`: Mock do StoreInformation
   - `navbar.tsx`: Mock do NavBar
2. Configurar mocks no `setup.ts`:
   - Usar `vi.mock` para o StoreInformation
3. Configurar alias no `vitest.config.mts`:
   - Adicionar alias para `@mz-mfe-navbar/app` apontando para o mock
   - Isso resolve o problema no nível do Vite, antes da transformação do código

**Validação**:

- Executar os testes para confirmar que os mocks funcionam corretamente
- Verificar se não há mais erros de importação do Module Federation

**Boas-práticas / prevenção**:

- Manter mocks centralizados e documentados
- Usar `vi.mock` para mocks simples
- Usar alias no Vite para componentes via Module Federation
- Documentar a necessidade de adicionar novos alias quando novos componentes federados forem adicionados

---

Este documento pode ser atualizado conforme necessário para incluir novos erros ou alterações.
