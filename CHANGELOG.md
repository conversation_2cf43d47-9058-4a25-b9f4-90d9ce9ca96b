# Changelog

## [2.0.0] - 2024-03-19

### Adicionado

- Migração para Vite como bundler principal
- Configuração de Module Federation para micro-frontends
- Suporte a TypeScript com configurações otimizadas
- Integração com React Router v6
- Configuração de testes com Vitest
- Suporte a aliases de importação via tsconfig-paths
- Autenticação via logTo, eliminando a necessidade de copiar localStorage

### Modificado

- Estrutura de configuração do projeto
- Sistema de build e desenvolvimento
- Configuração de testes unitários
- Gerenciamento de dependências compartilhadas
- Sistema de rotas e navegação
- Processo de autenticação

### Removido

- Configuração antiga de Webpack
- Dependências desnecessárias
- Configurações redundantes de alias
- Necessidade de copiar localStorage do ambiente de staging
- Configuração e suporte ao Docker
- Arquivos de configuração do Docker (docker-compose.yml, Dockerfile)
- Dependência do container para desenvolvimento local
- Dependência do versionamento do container

### Breaking Changes

- Atualização do React Router para v6
- Atualização do React para 18
- Mudança no sistema de build para Vite
- Alteração na estrutura de testes
- Modificação no sistema de importação de módulos

### Dependências Atualizadas

- React Router v6
- Vite
- Vitest
- TypeScript
- Testing Library
- Module Federation

## [2025-06-12] - Design System Migration

### Added

- Design System Tables em ticker-history, daily-position, monitored-shareholders-search-modal, shareholder-base
- LayerGroupIcon do Design System para agrupamento
- Padrão expanded && em Reports page

### Changed

- Migração ul/li → Table nas páginas principais
- Table.Container → Table (padrão Design System)
- ico_aglutinar.png → LayerGroupIcon

### Fixed

- Export History table layout inconsistency
- Ícones de agrupamento não apareciam

### Removed

- Todos os Table.Container do projeto
- Estilos CSS inline em componentes DS
- textAlign props excessivos

## [2025-06-13] - Group Icon Implementation

### Added

- GroupIcon styled-component para substituir ícones de agrupamento
- Estrutura: `group-icon.tsx` + `group-icon.types.ts` + `index.tsx`
- Props: `size`, `opacity`, `onClick` com tipagem TypeScript
- Efeito hover automático (opacity 0.3 → 1.0)

### Changed

- Página Acionistas: `Icons.OutlineLayers` → `GroupIcon`
- Smart Grouping: `Icons.LayerGroup` → `GroupIcon`
- Ícone agora usa `ico_aglutinar.png` consistentemente

### Removed

- Componente `AglutinarIcon` antigo
- Dependência de CSS background-image para ícones de agrupamento

### Technical

- Styled-components com `attrs` para props estáticas
- Filtro CSS para tema escuro (`brightness(0) invert(1)`)
- Transição suave de opacidade (0.2s ease)
