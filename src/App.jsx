import { datadogRum, DefaultPrivacyLevel } from '@datadog/browser-rum'
import Clarity from '@microsoft/clarity'
import React, { useEffect, useRef } from 'react'
import { AppRoutes } from 'routes'

import { i18n } from 'translate'

import 'assets/styles/_style.scss'

import { AppProvider } from 'contexts'
import { PageWrapper } from 'components/page-wrapper'
import { SideMenu } from 'components/side-menu'

import { lazy, Suspense } from 'react'
import { Loading } from '@mz-codes/design-system'
import GlobalStyles from './styles/global.styles'
import { env } from './env'
import storeInformation from './utils/storeInformation'

if (env.DATADOG_ENABLED) {
  datadogRum.init({
    applicationId: env.DATADOG_APPLICATION_ID,
    clientToken: env.DATADOG_CLIENT_TOKEN,
    service: 'mz-shareholders-sub',
    env: env.DATADOG_ENV,
    sessionSampleRate: env.DATADOG_SAMPLE_RATE,
    trackUserInteractions: true,
    trackResources: true,
    trackLongTasks: true,
    trackErrors: true,
    defaultPrivacyLevel: DefaultPrivacyLevel.MASK_USER_INPUT,
  })

  datadogRum.setUser({
    id: storeInformation.getCore2UserId() || null,
    name: storeInformation.getUserInformation()?.name || null,
    email: storeInformation.getUserEmail() || null,
  })
}

if (env.CLARITY_ENABLED) {
  Clarity.init(env.CLARITY_PROJECT_ID)
  Clarity.identify(
    storeInformation.getUserId(),
    storeInformation.getCompanyId(),
    null,
    storeInformation.getUserInformation()?.name
  )
}

function App() {
  const iframeRef = useRef < HTMLIFrameElement > null

  useEffect(() => {
    if (iframeRef.current != null) {
      iframeRef.current.onload = () => {
        iframeRef.current?.contentWindow?.postMessage({ isSubdomain: true }, '*')
      }
    }
  }, [])

  return (
    <AppProvider>
      <AppRoutes />
      <GlobalStyles />
    </AppProvider>
  )
}

export default App
