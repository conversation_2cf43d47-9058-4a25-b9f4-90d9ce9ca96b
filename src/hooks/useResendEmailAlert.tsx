import { BaseError, ResendEmailAlertError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

type TPatchResendEmailAlert = {
  companyId: string
  tickerId: string
  reportId: string
}

const patchResendEmailAlert = ({ companyId, tickerId, reportId }: TPatchResendEmailAlert) => {
  try {
    const response = api.patch(
      `${MZ_IRM_NEW}/companies/${companyId}/tickers/${tickerId}/reports/${reportId}/email/resend`
    )

    return response
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new ResendEmailAlertError()
  }
}

export { patchResendEmailAlert }
