import { BaseError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { GetMonitoredShareholdersError } from 'pages/monitoring/errors/GetMonitoredShareholdersError'
import { NoMonitoredShareholdersRegister } from 'pages/monitoring/errors/NoMonitoredShareholdersRegisterError'
import { i18n } from 'translate'
import { formatDocument } from 'utils'

export type InterestGroupList = {
  endDate: string
  report: Array<{
    listName: string
    listData: Array<{
      delta: string
      document: string
      finalPosition: string
      name: string
    }>
  }>
  startDate: string
}

export type SerializedInterestGroupList = {
  endDate: string
  report: Array<{
    listName: string
    listData: Array<{
      delta: string
      document: string
      finalPosition: string
      name: string
    }>
  }>
  startDate: string
}

type GetInterestGroupShareholdersListProps = {
  customerId?: string
  tickerId?: string
  referenceDate?: string
}

type ResponseData = {
  success: boolean
  data: InterestGroupList
}

const serialize = (interestGroups: InterestGroupList): SerializedInterestGroupList => {
  const language = i18n.t('globals.currentLanguage') === '0' ? 'en-US' : 'pt-BR'
  const serializedData = {
    ...interestGroups,
    report: interestGroups.report.map((report) => {
      return {
        ...report,
        listData: report.listData.map((shareholder) => {
          return {
            ...shareholder,
            document: formatDocument(shareholder.document),
            finalPosition: Number(shareholder.finalPosition).toLocaleString(language),
            delta: Number(shareholder.delta).toLocaleString(language),
          }
        }),
      }
    }),
  }
  return serializedData
}

const getInterestGroupShareholdersList = async ({
  customerId = '',
  tickerId = '',
  referenceDate = '',
}: GetInterestGroupShareholdersListProps) => {
  try {
    const params = { referenceDate }
    const response = await api.get<ResponseData>(
      `${MZ_IRM_NEW}/company/${customerId}/ticker/${tickerId}/monitored/report`,
      { params }
    )

    const { success, data } = response.data

    if (response.status === 204) throw new NoMonitoredShareholdersRegister()

    if (!success) throw new GetMonitoredShareholdersError()

    return data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new GetMonitoredShareholdersError()
  }
}

const getSerializedInterestGroupShareholdersList = async (params: GetInterestGroupShareholdersListProps) => {
  const shareholders = await getInterestGroupShareholdersList(params)
  return serialize(shareholders)
}

export { getInterestGroupShareholdersList, getSerializedInterestGroupShareholdersList }
