import React from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'

import { useToast, useTours } from '@mz-codes/design-system'

export function hocWrapper(Component: typeof React.Component) {
  function Wrapper(props: object): JSX.Element {
    const navigate = useNavigate()
    const location = useLocation()
    const params = useParams()
    const { createToast } = useToast()
    const { run, startTour, setPageTours } = useTours()

    return (
      <Component
        {...props}
        router={{ location, navigate }}
        createToast={createToast}
        runTour={run}
        startTour={startTour}
        setPageTours={setPageTours}
        params={params}
      />
    )
  }

  return Wrapper
}
