import { BaseError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { i18n } from 'translate'
import { formatDate, dateFromUTC, numberHelper } from 'utils'
import { GetMonitoredShareholdersError } from 'pages/monitoring/errors/GetMonitoredShareholdersError'

type TGetTickerPriceHistoryProps = {
  ticker: string
  startDate: string
  endDate: string
}

export type TPriceHistory = {
  average: string
  close: string
  date: string
  high: string
  low: string
  open: string
  provider: string
  trades: string
  variation: string
  volume: string
}

type TSerializedPriceHistory = {
  average: string
  close: string
  date: string
  high: string
  low: string
  open: string
  provider: string
  trades: string
  variation: string
  volume: string
}

type ResponseData = {
  success: boolean
  data: TPriceHistory[]
}

const serialize = (historyPositions: TPriceHistory[]): TSerializedPriceHistory[] => {
  const language = i18n.t('globals.currentLanguage')
  const serializedData = historyPositions.map((position) => {
    return {
      ...position,
      date: formatDate(dateFromUTC(position.date)),
      open: numberHelper(position.open, language, '', '', 2),
      close: numberHelper(position.close, language, '', '', 2),
      low: numberHelper(position.low, language, '', '', 2),
      high: numberHelper(position.high, language, '', '', 2),
      volume: numberHelper(position.volume, language, '', '', 0),
    }
  })
  return serializedData
}

const getTickerPriceHistory = async ({ ticker, startDate, endDate }: TGetTickerPriceHistoryProps) => {
  const tickerFormatted = ticker.split('.')
  try {
    const response = await api.get<ResponseData>(`${MZ_IRM_NEW}/tickers/${tickerFormatted[0]}`, {
      params: {
        'start-date': startDate,
        'end-date': endDate,
        adjusted: 0,
      },
    })

    const { success, data } = response.data

    if (!success) throw new GetMonitoredShareholdersError()

    return data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new GetMonitoredShareholdersError()
  }
}

const getSerializedTickerPriceHistory = async (params: TGetTickerPriceHistoryProps) => {
  const historyPosition = await getTickerPriceHistory(params)
  if (!historyPosition) return []
  return serialize(historyPosition)
}

export { getTickerPriceHistory, getSerializedTickerPriceHistory }
