import { DeleteShareholderBaseError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

export type TDeleteShareholderBaseParams = {
  companyId: string
  positionBatchId: string
  reason: string
}

export const deleteShareholderBase = async (params: TDeleteShareholderBaseParams) => {
  try {
    const { companyId, positionBatchId, reason } = params

    const uri = `${MZ_IRM_NEW}/position/companies/${companyId}/position-batch/${positionBatchId}`

    await api.delete(uri, { data: { reason } })
  } catch (error: unknown) {
    throw new DeleteShareholderBaseError()
  }
}
