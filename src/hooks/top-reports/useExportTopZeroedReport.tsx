import { ReportStatus } from 'types/shareholders'

import { ExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

type IExportTopZeroedReportRequestDTO = {
  companyId: string
  tickerId: string
  referenceDateStart: string
  referenceDateEnd: string
  shareholderType: string
  viewType: string
  groupedType: string
  language: number
  limit: number
}

type IExportTopZeroedReportResponseDTO = {
  shareholdersReportId: string
  status: ReportStatus
}

export const topZeroedReportExport = async (
  params: IExportTopZeroedReportRequestDTO
): Promise<IExportTopZeroedReportResponseDTO> => {
  const {
    companyId,
    tickerId,
    referenceDateStart,
    referenceDateEnd,
    shareholderType,
    viewType,
    groupedType,
    language,
    limit,
  } = params

  try {
    const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-zeroed`
    const { data: response } = await api.post<IExportTopZeroedReportResponseDTO>(url, {
      companyId,
      tickerId,
      referenceDateStart,
      referenceDateEnd,
      shareholderType,
      viewType,
      groupedType,
      language,
      limit,
    })

    return response
  } catch (err) {
    throw new ExportReportError()
  }
}
