import { ReportStatus } from 'types/shareholders'

import { ExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

type IExportTopHoldersMovementReportRequestDTO = {
  companyId: string
  tickerId: string
  referenceDateStart: string
  referenceDateEnd: string
  shareholderType: string
  viewType: string
  groupedType: string
  language: number
  limit: number
}

type IExportTopHoldersMovementReportResponseDTO = {
  shareholderReportId: string
  status: ReportStatus
}

export const topHoldersMovementReportExport = async (
  params: IExportTopHoldersMovementReportRequestDTO
): Promise<IExportTopHoldersMovementReportResponseDTO> => {
  const {
    companyId,
    tickerId,
    referenceDateStart,
    referenceDateEnd,
    shareholderType,
    viewType,
    groupedType,
    language,
    limit,
  } = params

  try {
    const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-holders-movement`
    const { data: response } = await api.post<IExportTopHoldersMovementReportResponseDTO>(url, {
      tickerId,
      referenceDateStart,
      referenceDateEnd,
      shareholderType,
      viewType,
      groupedType,
      language,
      limit,
    })

    return response
  } catch (err) {
    throw new ExportReportError()
  }
}
