import { ReportStatus } from 'types/shareholders'

import { ExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

interface IExportTopHoldersReportRequestDTO {
  companyId: string
  tickerId: string
  referenceDate: string
  shareholderType: string
  viewType: string
  groupedType: string
  language: number
  limit: number
}

interface IExportTopHoldersReportResponseDTO {
  shareholdersReportId: string
  status: ReportStatus
}

export const topHoldersReportExport = async (
  params: IExportTopHoldersReportRequestDTO
): Promise<IExportTopHoldersReportResponseDTO> => {
  const { companyId, tickerId, referenceDate, shareholderType, viewType, groupedType, language, limit } = params

  try {
    const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-holders`
    const { data: response } = await api.post<IExportTopHoldersReportResponseDTO>(url, {
      companyId,
      tickerId,
      referenceDate,
      shareholderType,
      viewType,
      groupedType,
      language,
      limit,
    })

    return response
  } catch (err) {
    throw new ExportReportError()
  }
}
