import { ReportStatus } from 'types/shareholders'

import { ExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

interface IExportTopNewHoldersReportRequestDTO {
  companyId: string
  tickerId: string
  referenceDateStart: string
  referenceDateEnd: string
  shareholderType: string
  viewType: string
  groupedType: string
  language: number
  limit: number
}

interface IExportTopNewHoldersReportResponseDTO {
  shareholderReportId: string
  status: ReportStatus
}

export const topNewHoldersReportExport = async (
  params: IExportTopNewHoldersReportRequestDTO
): Promise<IExportTopNewHoldersReportResponseDTO> => {
  const {
    companyId,
    tickerId,
    referenceDateStart,
    referenceDateEnd,
    shareholderType,
    viewType,
    groupedType,
    language,
    limit,
  } = params

  try {
    const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-new-holders`
    const { data: response } = await api.post<IExportTopNewHoldersReportResponseDTO>(url, {
      tickerId,
      referenceDateStart,
      referenceDateEnd,
      shareholderType,
      viewType,
      groupedType,
      language,
      limit,
    })

    return response
  } catch (err) {
    throw new ExportReportError()
  }
}
