import { ReportStatus } from 'types/shareholders'

import { ExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

interface IExportTopBuyersReportRequestDTO {
  companyId: string
  tickerId: string
  referenceDateStart: string
  referenceDateEnd: string
  shareholderType: string
  viewType: string
  groupedType: string
  language: number
  limit: number
}

interface IExportTopBuyersReportResponseDTO {
  shareholderReportId: string
  status: ReportStatus
}

export const topBuyersReportExport = async (
  params: IExportTopBuyersReportRequestDTO
): Promise<IExportTopBuyersReportResponseDTO> => {
  const {
    companyId,
    tickerId,
    referenceDateStart,
    referenceDateEnd,
    shareholderType,
    viewType,
    groupedType,
    language,
    limit,
  } = params

  try {
    const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-buyers`
    const { data: response } = await api.post<IExportTopBuyersReportResponseDTO>(url, {
      tickerId,
      referenceDateStart,
      referenceDateEnd,
      shareholderType,
      viewType,
      groupedType,
      language,
      limit,
    })

    return response
  } catch (err) {
    throw new ExportReportError()
  }
}
