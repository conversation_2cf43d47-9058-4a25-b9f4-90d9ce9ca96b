import { ReportStatus } from 'types/shareholders'

import { ExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

interface IExportTopVariationReportRequestDTO {
  companyId: string
  tickerId: string
  referenceDateStart: string
  referenceDateEnd: string
  shareholderType: string
  viewType: string
  groupedType: string
  language: number
  limit: number
}

interface IExportTopVariationReportResponseDTO {
  shareholdersReportId: string
  status: ReportStatus
}

export const topVariationReportExport = async (
  params: IExportTopVariationReportRequestDTO
): Promise<IExportTopVariationReportResponseDTO> => {
  const {
    companyId,
    tickerId,
    referenceDateStart,
    referenceDateEnd,
    shareholderType,
    viewType,
    groupedType,
    language,
    limit,
  } = params

  try {
    const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-variation`
    const { data: response } = await api.post<IExportTopVariationReportResponseDTO>(url, {
      companyId,
      tickerId,
      referenceDateStart,
      referenceDateEnd,
      shareholderType,
      viewType,
      groupedType,
      language,
      limit,
    })

    return response
  } catch (err) {
    throw new ExportReportError()
  }
}
