import { ReportStatus } from 'types/shareholders'

import { ExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

interface IExportReportRequestDTO {
  companyId: string
  tickerId: string
  referenceDate: string
  referenceDateStart: string
  referenceDateEnd: string
  shareholderType: string
  viewType: string
  groupedType: string
  language: number
  limit: number
}

interface IExportReportResponseDTO {
  shareholdersReportId: string
  status: ReportStatus
}

interface IExportReportProps {
  reportType: string
  companyId: string
  tickerId: string
  referenceDate: string
  referenceDateStart: string
  referenceDateEnd: string
  shareholderType: string
  viewType: string
  groupedType: string
  language: number
  limit: number
}

export const topReportsExport = async (params: IExportReportProps): Promise<IExportReportResponseDTO> => {
  const {
    reportType,
    companyId,
    tickerId,
    referenceDate,
    referenceDateStart,
    referenceDateEnd,
    shareholderType,
    viewType,
    groupedType,
    language,
    limit,
  } = params

  try {
    const data: IExportReportRequestDTO = {
      companyId,
      tickerId,
      referenceDate,
      referenceDateStart,
      referenceDateEnd,
      shareholderType,
      viewType,
      groupedType,
      language,
      limit,
    }
    const url = `${MZ_IRM_NEW}/companies/${companyId}/reports/${reportType}`
    const response = await api.post<IExportReportResponseDTO>(url, data)
    return response.data
  } catch (err) {
    throw new ExportReportError()
  }
}
