import { AxiosProgressEvent } from 'axios'
import { BaseError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { GetMonitoredShareholdersError } from 'pages/monitoring/errors/GetMonitoredShareholdersError'

type TPostUploadMonitoredShareholdersProps = {
  file: File
  customerId?: string
  idiom?: number
  onUploadProgress?(progressEvent: AxiosProgressEvent): void
}

type ResponseData = {
  success: boolean
  data: unknown
}

const postUploadMonitoredShareholders = async ({
  file,
  customerId = '',
  idiom = 1,
  onUploadProgress,
}: TPostUploadMonitoredShareholdersProps) => {
  try {
    const payload = new FormData()
    payload.append('monitoredSheet', file, file.name)

    const config = { onUploadProgress }
    const response = await api.post<ResponseData>(
      `${MZ_IRM_NEW}/company/${customerId}/monitored/uploadSheet?idiom=${idiom}`,
      payload,
      config
    )

    const { success, data } = response.data

    if (!success) throw new GetMonitoredShareholdersError()

    return data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new GetMonitoredShareholdersError()
  }
}

export { postUploadMonitoredShareholders }
