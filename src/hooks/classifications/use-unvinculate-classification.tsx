import { api, MZ_IRM_NEW } from 'globals/api'
import { ClassificationsUnvinculateError } from 'errors'

type TUnvinculateClassification = {
  companyId: string
  shareholderId: string
}

const deleteUnvinculateClassification = async ({ companyId, shareholderId }: TUnvinculateClassification) => {
  try {
    const { data } = await api.delete(
      `${MZ_IRM_NEW}/shareholders/companies/${companyId}/shareholders/${shareholderId}/classification/`
    )

    return data
  } catch (err) {
    throw new ClassificationsUnvinculateError()
  }
}

export { deleteUnvinculateClassification }
