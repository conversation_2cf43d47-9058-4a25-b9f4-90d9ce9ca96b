import { api, MZ_IRM_NEW } from 'globals/api'
import { ClassificationsCreateError, ClassificationsCreateAlreadyExistsError } from 'errors'

type TCreateClassification = {
  companyId: string
  description: string
}

type TCreateClassificationResponse = {
  classificationId: string
}

const postCreateClassification = async ({ companyId, description }: TCreateClassification) => {
  try {
    const { data, status } = await api.post<TCreateClassificationResponse>(
      `${MZ_IRM_NEW}/shareholders/companies/${companyId}/classifications`,
      {
        description,
      }
    )

    if (status === 409) throw new ClassificationsCreateAlreadyExistsError()

    if (!data.classificationId || status !== 201) throw new ClassificationsCreateError()

    return data
  } catch (err) {
    throw new ClassificationsCreateError()
  }
}

export { postCreateClassification }
