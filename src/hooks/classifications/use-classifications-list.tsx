import { api, MZ_IRM_NEW } from 'globals/api'
import { ClassificationsListError } from 'errors'

type TClassificationsList = {
  companyId: string
  limit?: number
  offset?: number
}

type TClassificationResponse = {
  classificationId: string
  description: string
}

const getClassificationsList = async ({ companyId, limit, offset }: TClassificationsList) => {
  try {
    const params = { limit, offset }
    const { data, status } = await api.get<TClassificationResponse[]>(
      `${MZ_IRM_NEW}/shareholders/companies/${companyId}/classifications`,
      {
        params: limit && offset ? params : null,
      }
    )

    if (![200, 204].includes(status)) throw new ClassificationsListError()

    return data || []
  } catch (err) {
    throw new ClassificationsListError()
  }
}

export { getClassificationsList }
