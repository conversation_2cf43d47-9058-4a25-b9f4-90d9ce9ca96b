import { api, MZ_IRM_NEW } from 'globals/api'
import { ClassificationsDeleteError } from 'errors'

type TDeleteClassification = {
  companyId: string
  classificationId: string
}

const deleteClassification = async ({ companyId, classificationId }: TDeleteClassification) => {
  try {
    const response = await api.delete(
      `${MZ_IRM_NEW}/shareholders/companies/${companyId}/classifications/${classificationId}`
    )

    if (response.status !== 204) throw new ClassificationsDeleteError()

    return response.status
  } catch (err) {
    throw new ClassificationsDeleteError()
  }
}

export { deleteClassification }
