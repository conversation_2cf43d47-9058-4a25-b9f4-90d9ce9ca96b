import { api, MZ_IRM_NEW } from 'globals/api'
import { ClassificationsVinculateError } from 'errors'

type TVinculateClassification = {
  companyId: string
  shareholderId: string
  classificationId: string
}

const patchVinculateClassification = async ({
  companyId,
  shareholderId,
  classificationId,
}: TVinculateClassification) => {
  try {
    const { data } = await api.patch(
      `${MZ_IRM_NEW}/shareholders/companies/${companyId}/shareholders/${shareholderId}/classification/`,
      {
        classificationId,
      }
    )

    return data
  } catch (err) {
    throw new ClassificationsVinculateError()
  }
}

export { patchVinculateClassification }
