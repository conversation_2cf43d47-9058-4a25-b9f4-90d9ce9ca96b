import { api, MZ_IRM_NEW } from 'globals/api'
import { ClassificationsEditError } from 'errors'

type TEditClassification = {
  companyId: string
  classificationId: string
  description: string
}

const editClassification = async ({ companyId, classificationId, description }: TEditClassification) => {
  try {
    const response = await api.put(
      `${MZ_IRM_NEW}/shareholders/companies/${companyId}/classifications/${classificationId}`,
      {
        description,
      }
    )

    return response
  } catch (err) {
    throw new ClassificationsEditError()
  }
}

export { editClassification }
