import { api, MZ_IRM_NEW } from 'globals/api'
import { GetExportHistoricPositionsError } from 'pages/monitoring/errors/GetExportHistoricPositionsError'

type DailyPositionData = {
  fileUrl: string
  fileName: string
}

type ResponseData = {
  success: boolean
  data: DailyPositionData
}

type SimplePositionsResponse = {
  reportId: string
}

type GetExportHistoricPositionsProps = {
  shareholderId?: string
  tickerId?: string
  companyId?: string
}

interface GetExportHistoricGroupedPositionsProps {
  companyId: string
  tickerId: string
  shareholderGroupId: string
  language?: number
}

const getExportHistoricSimplePositions = async ({
  tickerId = '',
  shareholderId = '',
  companyId = '',
}: GetExportHistoricPositionsProps) => {
  try {
    const response = await api.post<SimplePositionsResponse>(
      `${MZ_IRM_NEW}/tearsheet/companies/${companyId}/position-history/tickers/${tickerId}/shareholders/${shareholderId}/historic`
    )

    if (!response.data) throw GetExportHistoricPositionsError

    return response
  } catch (err) {
    throw GetExportHistoricPositionsError
  }
}

const getExportHistoricGroupedPositions = async ({
  companyId,
  tickerId,
  shareholderGroupId,
  language = 1,
}: GetExportHistoricGroupedPositionsProps) => {
  try {
    const { data, status } = await api.post<ResponseData>(
      `${MZ_IRM_NEW}/tearsheet/companies/${companyId}/position-history/tickers/${tickerId}/group/${shareholderGroupId}/export`,
      {
        language: Number(language),
      }
    )

    if (!data || status >= 400) throw GetExportHistoricPositionsError

    return data
  } catch (err) {
    throw GetExportHistoricPositionsError
  }
}

export { getExportHistoricSimplePositions, getExportHistoricGroupedPositions }
