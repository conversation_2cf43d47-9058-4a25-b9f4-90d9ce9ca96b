import { BaseError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { GetMonitoredShareholdersError } from 'pages/monitoring/errors/GetMonitoredShareholdersError'

type PostInterestGroupExportScreenProps = {
  customerId?: string
  tickerId?: string
  referenceDate?: string
  idiom?: number
}

type ResponseData = {
  success: boolean
  data: unknown
}

const postInterestGroupExportScreen = async ({
  customerId = '',
  tickerId = '',
  referenceDate = '',
  idiom,
}: PostInterestGroupExportScreenProps) => {
  try {
    const params = { idiom, referenceDate }
    const response = await api.post<ResponseData>(
      `${MZ_IRM_NEW}/company/${customerId}/ticker/${tickerId}/monitored/report`,
      params
    )

    const { success, data } = response.data

    if (!success) throw new GetMonitoredShareholdersError()

    return data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new GetMonitoredShareholdersError()
  }
}

export { postInterestGroupExportScreen }
