import { api, MZ_IRM_NEW } from 'globals/api'

export type TRetrieveBaseSummaryParams = {
  companyId: string
  tickerId: string
  referenceDate: string
}

export type TRetrieveBaseSummaryResponse = {
  stockAmount: number
  volume: number
  shareholdersTotal: number
  shareholdersEntry: number
  shareholdersExit: number
  individualsTotal: number
  individualsEntry: number
  individualsExit: number
  fundsTotal: number
  fundsEntry: number
  fundsExit: number
  prevDay: string
  referenceDate: string
}

export const retrieveBaseSummary = async (params: TRetrieveBaseSummaryParams) => {
  const { companyId, referenceDate, tickerId } = params

  const { data, status } = await api.get<TRetrieveBaseSummaryResponse>(
    `${MZ_IRM_NEW}/position/companies/${companyId}/summary-report`,
    {
      params: { tickerId, referenceDate },
    }
  )

  return {
    data,
    status,
  }
}
