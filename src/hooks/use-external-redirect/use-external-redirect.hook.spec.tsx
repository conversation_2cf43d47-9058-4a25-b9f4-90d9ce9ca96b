import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { EXTERNAL_PRODUCT } from 'consts/external-products'
import { useExternalRedirect } from './use-external-redirect.hook'
import * as helpers from './use-external-redirect.helpers'
import { env } from '../../env'

// Mock of env module
vi.mock('../../env', () => ({
  env: {
    PORTAL_URL: 'https://mziq.com',
    PORTAL_STG_URL: 'https://portal-stg.mziq.com',
    IS_DEV: false,
  },
}))

// Mock of helper functions
vi.mock('./use-external-redirect.helpers', () => ({
  isStaging: vi.fn(),
  normalizePath: vi.fn((path) => path),
  getFullSubdomain: vi.fn(),
  buildSubdomainUrl: vi.fn(),
  buildPortalUrl: vi.fn(),
}))

describe('useExternalRedirect', () => {
  // Criar uma variável para guardar a URL para qual será redirecionado
  let redirectUrl = ''

  beforeEach(() => {
    vi.clearAllMocks()
    redirectUrl = ''

    // Em vez de mockar o setter de window.location.href, mockamos o próprio objeto
    // location com um getter/setter personalizado para a propriedade href
    const windowLocationMock = {
      get href() {
        return window.location.href
      },
      set href(url: string) {
        redirectUrl = url
      },
    }

    Object.defineProperty(window, 'location', {
      value: windowLocationMock,
      writable: true,
    })
  })

  describe('current environment', () => {
    it('should correctly detect production environment', () => {
      vi.mocked(helpers.isStaging).mockReturnValue(false)

      const { result } = renderHook(() => useExternalRedirect())

      expect(result.current.isStaging).toBe(false)
      expect(result.current.portalBaseUrl).toBe(env.PORTAL_URL)
      expect(helpers.isStaging).toHaveBeenCalled()
    })

    it('should correctly detect staging environment', () => {
      vi.mocked(helpers.isStaging).mockReturnValue(true)

      const { result } = renderHook(() => useExternalRedirect())

      expect(result.current.isStaging).toBe(true)
      expect(result.current.portalBaseUrl).toBe(env.PORTAL_STG_URL)
      expect(helpers.isStaging).toHaveBeenCalled()
    })
  })

  describe('getProductUrl', () => {
    it('should generate correct URL for product without subdomain (IRM)', () => {
      vi.mocked(helpers.isStaging).mockReturnValue(false)
      vi.mocked(helpers.buildPortalUrl).mockReturnValue('https://mziq.com/irm/dashboard')

      const { result } = renderHook(() => useExternalRedirect())

      const url = result.current.getProductUrl(EXTERNAL_PRODUCT.IRM, '/dashboard')

      expect(url).toBe('https://mziq.com/irm/dashboard')
      expect(helpers.buildPortalUrl).toHaveBeenCalledWith(EXTERNAL_PRODUCT.IRM, '/dashboard', env.PORTAL_URL)
      expect(helpers.buildSubdomainUrl).not.toHaveBeenCalled()
    })

    it('should generate correct URL for product with subdomain (ENGAGEMENT)', () => {
      vi.mocked(helpers.isStaging).mockReturnValue(false)
      vi.mocked(helpers.buildSubdomainUrl).mockReturnValue('https://engagement.mziq.com/dashboard')

      const { result } = renderHook(() => useExternalRedirect())

      const url = result.current.getProductUrl(EXTERNAL_PRODUCT.ENGAGEMENT, '/dashboard')

      expect(url).toBe('https://engagement.mziq.com/dashboard')
      expect(helpers.buildSubdomainUrl).toHaveBeenCalledWith(EXTERNAL_PRODUCT.ENGAGEMENT, '/dashboard', false)
      expect(helpers.buildPortalUrl).not.toHaveBeenCalled()
    })

    it('should use staging base URL when in staging environment', () => {
      vi.mocked(helpers.isStaging).mockReturnValue(true)
      vi.mocked(helpers.buildPortalUrl).mockReturnValue('https://portal-stg.mziq.com/irm/dashboard')

      const { result } = renderHook(() => useExternalRedirect())

      const url = result.current.getProductUrl(EXTERNAL_PRODUCT.IRM, '/dashboard')

      expect(url).toBe('https://portal-stg.mziq.com/irm/dashboard')
      expect(helpers.buildPortalUrl).toHaveBeenCalledWith(EXTERNAL_PRODUCT.IRM, '/dashboard', env.PORTAL_STG_URL)
    })

    it('should use correct subdomain format in staging environment', () => {
      // Test for updated subdomain formatting in staging
      vi.mocked(helpers.isStaging).mockReturnValue(true)

      // Vamos mockar o buildSubdomainUrl para verificar se ele está sendo chamado com os parâmetros corretos
      vi.mocked(helpers.buildSubdomainUrl).mockReturnValue('https://portal-stg-engagement.mziq.com/dashboard')

      const { result } = renderHook(() => useExternalRedirect())

      const url = result.current.getProductUrl(EXTERNAL_PRODUCT.ENGAGEMENT, '/dashboard')

      expect(url).toBe('https://portal-stg-engagement.mziq.com/dashboard')
      expect(helpers.buildSubdomainUrl).toHaveBeenCalledWith(EXTERNAL_PRODUCT.ENGAGEMENT, '/dashboard', true)
      // Ao invés de verificar se getFullSubdomain foi chamado (que não é chamado diretamente),
      // verificamos se a URL gerada está correta e se buildSubdomainUrl recebeu o parâmetro isStaging=true
    })
  })

  describe('navigateTo', () => {
    it('should redirect to external product using generated URL', () => {
      vi.mocked(helpers.isStaging).mockReturnValue(false)
      vi.mocked(helpers.buildPortalUrl).mockReturnValue('https://mziq.com/irm/dashboard')

      const { result } = renderHook(() => useExternalRedirect())

      result.current.navigateTo(EXTERNAL_PRODUCT.IRM, '/dashboard')

      expect(helpers.buildPortalUrl).toHaveBeenCalledWith(EXTERNAL_PRODUCT.IRM, '/dashboard', env.PORTAL_URL)
      expect(redirectUrl).toBe('https://mziq.com/irm/dashboard')
    })

    it('should use default path (/) when no specific path is provided', () => {
      vi.mocked(helpers.isStaging).mockReturnValue(false)
      vi.mocked(helpers.buildPortalUrl).mockReturnValue('https://mziq.com/irm/')

      const { result } = renderHook(() => useExternalRedirect())

      result.current.navigateTo(EXTERNAL_PRODUCT.IRM)

      expect(helpers.buildPortalUrl).toHaveBeenCalledWith(EXTERNAL_PRODUCT.IRM, '/', env.PORTAL_URL)
      expect(redirectUrl).toBe('https://mziq.com/irm/')
    })
  })
})
