import { useMemo, useCallback } from 'react'
import { env } from '../../env'
import { TUseExternalRedirectReturn } from './use-external-redirect.types'
import { isStaging as checkIsStaging, buildSubdomainUrl, buildPortalUrl } from './use-external-redirect.helpers'
import { EXTERNAL_PRODUCTS_CONFIG, TExternalProductKey } from '../../consts/external-products'

/**
 * Hook to manage redirects to external products
 * Automatically detects the environment and builds appropriate URLs
 *
 * @example
 * const { getProductUrl, navigateTo } = useExternalRedirect();
 *
 * // Generate URL for a product
 * const irmUrl = getProductUrl('IRM', '/dashboard');
 *
 * // Navigate to a product
 * const handleClick = () => navigateTo('ENGAGEMENT');
 *
 * @returns Functions and states to manage external redirects
 */
export function useExternalRedirect(): TUseExternalRedirectReturn {
  // Detects the environment once
  const isStaging = useMemo(() => {
    // Quando env.IS_DEV é true, tratamos como staging
    // Em outros casos, usamos a verificação normal de hostname
    if (env.IS_DEV) {
      return true
    }

    return checkIsStaging()
  }, [])

  // Determines the base portal URL (mziq)
  const portalBaseUrl = useMemo(() => {
    return isStaging ? env.PORTAL_STG_URL : env.PORTAL_URL
  }, [isStaging])

  /**
   * Builds the URL for an external product
   */
  const getProductUrl = useCallback(
    (productKey: TExternalProductKey, pagePath = '/'): string => {
      const product = EXTERNAL_PRODUCTS_CONFIG[productKey]

      return product.isSubdomain
        ? buildSubdomainUrl(productKey, pagePath, isStaging)
        : buildPortalUrl(productKey, pagePath, portalBaseUrl)
    },
    [isStaging, portalBaseUrl]
  )

  /**
   * Navigates to an external product
   */
  const navigateTo = useCallback(
    (productKey: TExternalProductKey, pagePath = '/'): void => {
      const finalUrl = getProductUrl(productKey, pagePath)
      console.log('Redirecting to:', finalUrl) // eslint-disable-line no-console
      window.location.href = finalUrl
    },
    [getProductUrl]
  )

  return {
    getProductUrl,
    navigateTo,
    isStaging,
    portalBaseUrl,
  }
}
