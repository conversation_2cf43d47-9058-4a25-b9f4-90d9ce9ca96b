import { TExternalProductKey, EXTERNAL_PRODUCTS_CONFIG, TExternalProduct } from 'consts/external-products'
import { env } from '../../env'

/**
 * Checks if current environment is staging
 */
export const isStaging = (): boolean => {
  // Check if we are in development or staging environment based on hostname or environment
  return window.location.hostname.includes('stg') || window.location.hostname.includes('localhost') || env.IS_DEV
}

/**
 * Formats a path to ensure it starts with "/"
 */
export const normalizePath = (path: string): string => {
  return path.startsWith('/') ? path : `/${path}`
}

/**
 * Generates the full subdomain name based on environment
 */
export const getFullSubdomain = (subdomain: string, inStaging: boolean): string => {
  return inStaging ? `portal-stg-${subdomain}` : subdomain
}

/**
 * Builds URL for a product with its own subdomain
 */
export const buildSubdomainUrl = (productKey: TExternalProductKey, pagePath: string, inStaging: boolean): string => {
  const product = EXTERNAL_PRODUCTS_CONFIG[productKey] as TExternalProduct & { subdomain: string }

  if (!product.subdomain) {
    console.warn(`Product ${productKey} is configured as subdomain but has no subdomain name defined`) // eslint-disable-line no-console
    return ''
  }

  const domain = 'mziq.com'
  const subdomain = getFullSubdomain(product.subdomain, inStaging)
  const normalizedPath = normalizePath(pagePath)

  return `https://${subdomain}.${domain}${normalizedPath}`
}

/**
 * Builds URL for a product without its own subdomain
 */
export const buildPortalUrl = (productKey: TExternalProductKey, pagePath: string, portalBaseUrl: string): string => {
  const product = EXTERNAL_PRODUCTS_CONFIG[productKey]
  const normalizedPath = normalizePath(pagePath)

  return `${portalBaseUrl}${product.path}${normalizedPath}`
}
