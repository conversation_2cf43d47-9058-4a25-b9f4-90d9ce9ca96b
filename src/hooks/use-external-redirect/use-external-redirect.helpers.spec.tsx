import { describe, it, expect, vi } from 'vitest'
import { EXTERNAL_PRODUCT } from 'consts/external-products'
import {
  isStaging,
  normalizePath,
  getFullSubdomain,
  buildSubdomainUrl,
  buildPortalUrl,
} from './use-external-redirect.helpers'

// Mock of window.location object for environment tests
const mockLocation = (hostname: string) => {
  const originalLocation = window.location
  const mockWindowLocation = {
    ...originalLocation,
    hostname,
  }

  // Temporarily replace location object
  Object.defineProperty(window, 'location', {
    configurable: true,
    value: mockWindowLocation,
  })

  return () => {
    // Restore original location object
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: originalLocation,
    })
  }
}

// Mock for external products
vi.mock('consts/external-products', () => ({
  EXTERNAL_PRODUCT: {
    INTELLIGENCE: 'INTELLIGENCE',
    IRM: 'IRM',
    DASHBOARD: 'DASHBOARD',
    RESPONSIBLE: 'RESPONSIBLE',
    ENGAGEMENT: 'ENGAGEMENT',
  },
  EXTERNAL_PRODUCTS_CONFIG: {
    INTELLIGENCE: { path: '/intelligence', isSubdomain: false },
    IRM: { path: '/irm', isSubdomain: false },
    DASHBOARD: { path: '/dashboard', isSubdomain: false },
    RESPONSIBLE: { path: '/responsible', isSubdomain: false },
    ENGAGEMENT: {
      path: '/engagement',
      isSubdomain: true,
      subdomain: 'engagement',
    },
  },
}))

// Mock env.IS_DEV
vi.mock('../../env', () => ({
  env: {
    IS_DEV: false,
  },
}))

describe('useExternalRedirect helpers', () => {
  describe('isStaging', () => {
    it('should correctly detect staging environment', () => {
      const restoreLocation = mockLocation('app-stg.mziq.com')
      expect(isStaging()).toBe(true)
      restoreLocation()
    })

    it('should correctly detect production environment', () => {
      const restoreLocation = mockLocation('app.mziq.com')
      // Ensure env.IS_DEV is false for this test
      vi.mock('../../env', () => ({
        env: {
          IS_DEV: false,
        },
      }))
      expect(isStaging()).toBe(false)
      restoreLocation()
    })
  })

  describe('normalizePath', () => {
    it('should keep path unchanged if it already starts with /', () => {
      expect(normalizePath('/test/path')).toBe('/test/path')
    })

    it('should add / at the beginning of path if it does not exist', () => {
      expect(normalizePath('test/path')).toBe('/test/path')
    })
  })

  describe('getFullSubdomain', () => {
    it('should return unchanged subdomain for production environment', () => {
      expect(getFullSubdomain('engagement', false)).toBe('engagement')
    })

    it('should add portal-stg- prefix for staging environment', () => {
      expect(getFullSubdomain('engagement', true)).toBe('portal-stg-engagement')
    })
  })

  describe('buildSubdomainUrl', () => {
    it('should build correct URL for subdomain in production', () => {
      const url = buildSubdomainUrl(EXTERNAL_PRODUCT.ENGAGEMENT, '/dashboard', false)
      expect(url).toBe('https://engagement.mziq.com/dashboard')
    })

    it('should build correct URL for subdomain in staging', () => {
      const url = buildSubdomainUrl(EXTERNAL_PRODUCT.ENGAGEMENT, '/dashboard', true)
      expect(url).toBe('https://portal-stg-engagement.mziq.com/dashboard')
    })

    it('should normalize path correctly', () => {
      const url = buildSubdomainUrl(EXTERNAL_PRODUCT.ENGAGEMENT, 'dashboard', false)
      expect(url).toBe('https://engagement.mziq.com/dashboard')
    })
  })

  describe('buildPortalUrl', () => {
    it('should build correct URL for portal in production', () => {
      const url = buildPortalUrl(EXTERNAL_PRODUCT.IRM, '/dashboard', 'https://mziq.com')
      expect(url).toBe('https://mziq.com/irm/dashboard')
    })

    it('should build correct URL for portal in staging', () => {
      const url = buildPortalUrl(EXTERNAL_PRODUCT.IRM, '/dashboard', 'https://portal-stg.mziq.com')
      expect(url).toBe('https://portal-stg.mziq.com/irm/dashboard')
    })

    it('should normalize path correctly', () => {
      const url = buildPortalUrl(EXTERNAL_PRODUCT.IRM, 'dashboard', 'https://mziq.com')
      expect(url).toBe('https://mziq.com/irm/dashboard')
    })
  })
})
