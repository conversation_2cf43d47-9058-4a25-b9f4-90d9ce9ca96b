import { TExternalProductKey, EXTERNAL_PRODUCTS_CONFIG } from '../../consts/external-products'

/**
 * Definition of external products with their configurations
 */
export type TExternalProduct = {
  /** Base path of the product */
  path: string
  /** Defines if the product uses its own subdomain */
  isSubdomain: boolean
  /** Subdomain name (without environment prefixes) */
  subdomain?: string
}

/**
 * Return type for useExternalRedirect hook
 */
export type TUseExternalRedirectReturn = {
  /** Indicates if current environment is staging */
  isStaging: boolean
  /** MZ portal base URL for current environment */
  portalBaseUrl: string
  /**
   * Function to generate URL for an external product
   * @param productKey Product key from EXTERNAL_PRODUCT
   * @param pagePath Optional page path within the product
   */
  getProductUrl: (productKey: TExternalProductKey, pagePath?: string) => string
  /**
   * Function to navigate to an external product
   * @param productKey Product key from EXTERNAL_PRODUCT
   * @param pagePath Optional page path within the product
   */
  navigateTo: (productKey: TExternalProductKey, pagePath?: string) => void
}

// For compatibility with existing code - will be temporarily maintained
export const EXTERNAL_PRODUCTS = EXTERNAL_PRODUCTS_CONFIG
