# useExternalRedirect

Um hook poderoso para lidar com navegação entre produtos MZ.

## Visão Geral

O hook `useExternalRedirect` fornece uma maneira padronizada de lidar com redirecionamentos entre diferentes produtos MZ, detectando automaticamente o ambiente atual (desenvolvimento, staging, produção) e gerando as URLs apropriadas.

## Funcionalidades

- **Detecção de ambiente**: Detecta automaticamente se a aplicação está rodando em desenvolvimento, staging ou produção
- **Geração de URL**: Constrói URLs corretas para diferentes produtos com base em sua configuração
- **Suporte a subdomínios**: Lida com produtos que possuem subdomínios personalizados
- **Segurança de tipos**: Completamente tipado com TypeScript

## Instalação

```bash
# Se você estiver usando diretamente no seu projeto
# Basta copiar os arquivos para seu projeto
```

## Uso

### Uso Básico

```tsx
import { useExternalRedirect, EXTERNAL_PRODUCT } from 'hooks/use-external-redirect'

function MeuComponente() {
  const { navigateTo } = useExternalRedirect()

  const handleNavigateToIntelligence = () => {
    navigateTo(EXTERNAL_PRODUCT.INTELLIGENCE)
  }

  return <button onClick={handleNavigateToIntelligence}>Ir para Intelligence</button>
}
```

### Uso com o Componente ExternalLink

```tsx
import { ExternalLink } from 'components'
import { EXTERNAL_PRODUCT } from 'consts/external-products'

function MeuComponente() {
  return (
    <ExternalLink product={EXTERNAL_PRODUCT.IRM} pagePath="/dashboard">
      Ir para o Dashboard do IRM
    </ExternalLink>
  )
}
```

### Obtendo URLs sem Navegação

```tsx
import { useExternalRedirect, EXTERNAL_PRODUCT } from 'hooks/use-external-redirect'

function MeuComponente() {
  const { getProductUrl } = useExternalRedirect()

  // Obter URL para o dashboard do IRM
  const irmUrl = getProductUrl(EXTERNAL_PRODUCT.IRM, '/dashboard')

  return (
    <div>
      <p>URL do IRM: {irmUrl}</p>
    </div>
  )
}
```

## Produtos Disponíveis

O hook suporta os seguintes produtos:

| Chave do Produto | Descrição                               |
| ---------------- | --------------------------------------- |
| `INTELLIGENCE`   | Análise de dados para tomada de decisão |
| `IRM`            | Gestão de Relações com Investidores     |
| `DASHBOARD`      | Visão geral do portal MZ                |
| `SETTINGS`       | Configurações do portal MZ              |
| `ENGAGEMENT`     | Engajamento com stakeholders            |

> **Escalabilidade**: A solução foi projetada para ser facilmente escalável. Para adicionar suporte a novos produtos, basta incluir o produto no arquivo de constantes `external-products.ts`, sem necessidade de modificar o hook em si.

## Tratamento de Ambiente

O hook detecta automaticamente o ambiente atual:

- **Desenvolvimento**: Quando rodando localmente ou em ambiente de desenvolvimento
- **Staging**: Quando o hostname inclui 'stg' ou rodando em ambiente de staging
- **Produção**: Quando rodando em produção

As URLs são geradas de acordo com o ambiente:

- Para produtos sem subdomínios:
  - Produção: `https://mziq.com/{caminho-do-produto}/{caminho-da-página}`
  - Staging: `https://portal-stg.mziq.com/{caminho-do-produto}/{caminho-da-página}`

- Para produtos com subdomínios:
  - Produção: `https://{subdominio}.mziq.com/{caminho-da-página}`
  - Staging: `https://portal-stg-{subdominio}.mziq.com/{caminho-da-página}`

## Referência da API

### `useExternalRedirect()`

Retorna um objeto com as seguintes propriedades:

| Propriedade     | Tipo                                                             | Descrição                                 |
| --------------- | ---------------------------------------------------------------- | ----------------------------------------- |
| `getProductUrl` | `(productKey: TExternalProductKey, pagePath?: string) => string` | Gera uma URL para o produto especificado  |
| `navigateTo`    | `(productKey: TExternalProductKey, pagePath?: string) => void`   | Navega para o produto especificado        |
| `isStaging`     | `boolean`                                                        | Indica se o ambiente atual é staging      |
| `portalBaseUrl` | `string`                                                         | A URL base do portal MZ no ambiente atual |

### `<ExternalLink>`

Props do componente:

| Prop       | Tipo                                      | Obrigatório | Descrição                                 |
| ---------- | ----------------------------------------- | ----------- | ----------------------------------------- |
| `product`  | `TExternalProductKey`                     | Sim         | O produto para o qual linkar              |
| `pagePath` | `string`                                  | Não         | O caminho da página dentro do produto     |
| `...props` | `AnchorHTMLAttributes<HTMLAnchorElement>` | Não         | Quaisquer outras props do elemento âncora |

## Adicionando Novos Produtos

Para adicionar um novo produto, basta atualizar o arquivo `external-products.ts`:

```typescript
// 1. Adicione a chave do produto no enum EXTERNAL_PRODUCT
export const EXTERNAL_PRODUCT = {
  // Produtos existentes...
  NOVO_PRODUTO: 'NOVO_PRODUTO',
} as const

// 2. Adicione a configuração no objeto EXTERNAL_PRODUCTS_CONFIG
export const EXTERNAL_PRODUCTS_CONFIG = {
  // Configurações existentes...
  [EXTERNAL_PRODUCT.NOVO_PRODUTO]: {
    path: '/novo-produto',
    isSubdomain: false, // ou true se usar um subdomínio
    // Se usar um subdomínio, adicione:
    // subdomain: 'novo-produto'
  },
} as const
```

E pronto! O hook `useExternalRedirect` e o componente `ExternalLink` passarão a suportar automaticamente o novo produto.

## Configuração de Ambiente

O hook depende das seguintes variáveis de ambiente:

```typescript
// No seu arquivo env.ts
VITE_PORTAL_URL: 'https://mziq.com',
VITE_PORTAL_STG_URL: 'https://portal-stg.mziq.com',
```
