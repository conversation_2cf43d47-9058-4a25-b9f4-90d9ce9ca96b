import { GroupShareholderAlreadyExistsError, GroupShareholderError } from 'errors'
import { DocumentTypes } from 'types/shareholders'
import { api, MZ_IRM_NEW } from 'globals/api'

type TPostGroupShareholder = {
  companyId: string | number
  shareholderGroupId: string
  shareholderDocument: string
  shareholderDocumentType: DocumentTypes
}

const postGroupShareholder = async ({
  companyId,
  shareholderGroupId,
  shareholderDocument,
  shareholderDocumentType,
}: TPostGroupShareholder) => {
  try {
    const { data, status } = await api.post(
      `${MZ_IRM_NEW}/company/${companyId}/shareholdergroup/${shareholderGroupId}/groupShareholder`,
      {
        shareholderDocument,
        shareholderDocumentType,
      }
    )

    if (status === 409) throw new GroupShareholderAlreadyExistsError()

    if (status !== 200) throw new GroupShareholderError()

    return data
  } catch (err) {
    throw new GroupShareholderError()
  }
}

export { postGroupShareholder }
