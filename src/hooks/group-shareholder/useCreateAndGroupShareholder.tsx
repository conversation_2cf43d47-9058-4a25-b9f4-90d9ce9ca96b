import { GroupShareholderAlreadyExistsError, GroupShareholderError } from 'errors'
import { DocumentTypes } from 'types/shareholders'
import { api, MZ_IRM_NEW } from 'globals/api'

type TPostCreateAndGroupShareholder = {
  companyId: string | number
  groupName: string
  shareholderDocument: string
  shareholderDocumentType: DocumentTypes
}

const postCreateGroupAndGroupShareholder = async ({
  companyId,
  groupName,
  shareholderDocument,
  shareholderDocumentType,
}: TPostCreateAndGroupShareholder) => {
  try {
    const { data, status } = await api.post(`${MZ_IRM_NEW}/company/${companyId}/createGroupAndGroupShareholder`, {
      groupName,
      shareholderDocument,
      shareholderDocumentType,
    })

    if (status === 409) throw new GroupShareholderAlreadyExistsError()

    if (status !== 200) throw new GroupShareholderError()

    return data
  } catch (err) {
    throw new GroupShareholderError()
  }
}

export { postCreateGroupAndGroupShareholder }
