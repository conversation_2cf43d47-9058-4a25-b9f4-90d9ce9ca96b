import { api, MZ_IRM_NEW } from 'globals/api'

type HistoryReprocessBase = {
  companyId: string | number
  positionBatchId: string
}

type ResponseData = {
  data: {
    positionBatchId: string
  }
  success: boolean
}

const historyReprocessBase = async ({ companyId, positionBatchId }: HistoryReprocessBase) => {
  const response = await api.post<ResponseData>(
    `${MZ_IRM_NEW}/positionBatch/company/${companyId}/reprocessPositionBatch/${positionBatchId}`
  )
  return response.data
}

export { historyReprocessBase }
