import { ExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { ReportStatus } from 'types/shareholders'

interface IExportReportProps {
  companyId: string
  tickerId: string
  shareholderGroupId: string
  referenceDateStart: string
  referenceDateEnd: string
  language: 0 | 1
}

interface IExportReportResponseDTO {
  shareholdersReportId: string
  status: ReportStatus
}

interface IExportReportRequestDTO {
  referenceDateStart: string
  referenceDateEnd: string
  language: 0 | 1
}

export const vinculatedShareholdersExport = async (params: IExportReportProps): Promise<IExportReportResponseDTO> => {
  try {
    const { companyId, tickerId, shareholderGroupId, referenceDateStart, referenceDateEnd, language } = params
    const data: IExportReportRequestDTO = {
      referenceDateStart,
      referenceDateEnd,
      language,
    }
    const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/ticker/${tickerId}/group/${shareholderGroupId}/info/report`

    const response = await api.post<IExportReportResponseDTO>(uri, data)
    return response.data
  } catch (error: unknown) {
    throw new ExportReportError()
  }
}
