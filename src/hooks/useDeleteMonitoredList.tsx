import { DeleteMonitoredListError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

export type TDeleteMonitoredList = {
  companyName: string
  companyId: string
  customerId: string
  tickerId: string
  referenceDate: string
  language: number
}

type TDeleteMonitoredListDTO = {
  companyName: string
  customerId: string
  tickerId: string
  referenceDate: string
  language: number
}

export const postDeleteMonitoredList = async (params: TDeleteMonitoredList) => {
  try {
    const { companyName, companyId, customerId, tickerId, referenceDate, language } = params

    const data: TDeleteMonitoredListDTO = {
      companyName,
      customerId,
      tickerId,
      referenceDate,
      language,
    }
    const uri = `${MZ_IRM_NEW}/companies/${companyId}/monitored/backup`

    const response = await api.post(uri, data)
    return response.data
  } catch (error: unknown) {
    throw new DeleteMonitoredListError()
  }
}
