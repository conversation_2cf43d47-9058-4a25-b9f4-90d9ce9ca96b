import { api, MZ_IRM_NEW } from 'globals/api'
import { i18n } from 'translate'
import { utils, formatDate, dateFromUTC } from 'utils'
import { GetMonitoredShareholdersError } from 'pages/monitoring/errors/GetMonitoredShareholdersError'
import { BaseError } from 'errors'

export type DailyPositionData = {
  closingPrice?: string
  document?: string
  label?: string
  name?: string
  operation: string
  referenceDate: string
  stockAmountEdited?: string
  value?: string
  variation?: string
}

export type SerializedDailyPositionData = {
  closingPrice?: string
  document?: string
  label?: string
  name?: string
  operation: string
  referenceDate: string
  stockAmountEdited?: string
  value?: string
  variation?: string
}

type ResponseData = {
  success: boolean
  data: DailyPositionData[]
}

type GetDailyPositionProps = {
  companyId?: string
  tickerId?: string
  referenceDateStart?: string
  referenceDateEnd?: string
  classification?: string
  contactId?: string
}

const handleOperationTranslations = (operation: string) => {
  const translation: { [index: string]: string } = {
    BUY: i18n.t('monitoring.operations.buy'),
    SELL: i18n.t('monitoring.operations.sell'),
  }
  return translation[operation] as string
}

const serialize = (positions: DailyPositionData[]): SerializedDailyPositionData[] => {
  const language = i18n.t('globals.currentLanguage') === '0' ? 'en-US' : 'pt-BR'
  const formatter = new Intl.NumberFormat(language, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  const serializedData = positions.map((position) => {
    return {
      ...position,
      operation: handleOperationTranslations(position.operation),
      referenceDate: formatDate(dateFromUTC(position.referenceDate)),
      stockAmountEdited: utils.formatLocale(position.stockAmountEdited),
      closingPrice: `R$ ${formatter.format(parseFloat(position?.closingPrice || '0'))}`,
      value: `R$  ${formatter.format(parseFloat(position?.value || '0'))}`,
      variation: utils.formatLocale(position.variation),
    }
  })
  return serializedData
}

const getDailyPosition = async ({
  companyId = '',
  tickerId = '',
  referenceDateStart = '',
  referenceDateEnd = '',
  classification = '',
  contactId = '',
}: GetDailyPositionProps) => {
  try {
    const params = {
      referenceDateStart,
      referenceDateEnd,
      classification,
      contactId,
    }
    const response = await api.get<ResponseData>(
      `${MZ_IRM_NEW}/companies/${companyId}/tickers/${tickerId}/shareholders/monitored/positions`,
      { params }
    )

    const { success, data } = response.data

    if (!success) throw new GetMonitoredShareholdersError()

    return data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new GetMonitoredShareholdersError()
  }
}

const getSerializedDailyPosition = async (params: GetDailyPositionProps) => {
  const positions = await getDailyPosition(params)
  return serialize(positions)
}

export { getDailyPosition, getSerializedDailyPosition }
