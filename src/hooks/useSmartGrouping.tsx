import { api, MZ_IRM_NEW } from 'globals/api'

export type TGetSmartGrouping = {
  companyId: string
}

export type TGetSmartGroupingGroup = {
  shareholderGroupId: string
} & TGetSmartGrouping

export type TPutSmartGrouping = {
  shareholderGroupId?: string
  shareholdersIds: string[]
} & TGetSmartGrouping

export type TPostCreateGroup = {
  shareholderGroupName: string
} & TGetSmartGrouping

export type TGetManualSuggestionsPaginatedFundsList = {
  limit: number
  page: number
  archived?: boolean
} & TGetSmartGrouping

export type TGetManualSuggestionsPaginatedFundsListResponseData = {
  filter: { page: string; limit: string; archived: boolean }

  shareholders: {
    shareholderId: string
    shareholderName: string
  }[]
  totalItems: number
}

export type TSmartGroupingHighlightsResponseData = {
  totalManual: string
  totalSuggested: string
  totalUngrouped: string
}

export type TAlbertSuggestionsResponseData = {
  albertShareholderGroupId: string
  albertShareholderGroupName: string
  totalSuggestions: string
}[]

export type TAlbertSuggestionsShareholderListResponseData = {
  document: string
  shareholderId: string
  shareholderName: string
}[]

export type TGetGroupsResponseData = {
  data: {
    name: string
    shareholderGroupId: string
  }[]
}

export type TGetMaxPage = {
  limit: number
  archived: boolean
} & TGetSmartGrouping

const getGroups = async (companyId: string) => {
  return api.get<TGetGroupsResponseData>(`${MZ_IRM_NEW}/company/${companyId}/shareholdergroup`)
}

const getManualSuggestionMaxPage = async (props: TGetMaxPage) => {
  const { companyId, limit, archived } = props
  return api.get<{ maxPage: number }>(
    `${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/manual-suggestions/maxpage?limit=${limit}&archived=${archived}`
  )
}

const getSmartGroupingSummary = async (props: TGetSmartGrouping) => {
  const { companyId } = props
  return api.get(`${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/summary`)
}

const getSmartGroupingHighlights = async (props: TGetSmartGrouping) => {
  const { companyId } = props
  return api.get<TSmartGroupingHighlightsResponseData>(
    `${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/highlights`
  )
}

const getAlbertSuggestions = async (props: TGetSmartGrouping) => {
  const { companyId } = props
  return api.get<TAlbertSuggestionsResponseData>(
    `${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/albert-suggestions`
  )
}

const getAlbertSuggestionsData = async (props: TGetSmartGroupingGroup) => {
  const { companyId, shareholderGroupId } = props
  return api.get<TAlbertSuggestionsShareholderListResponseData>(
    `${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/albert-suggestions/${shareholderGroupId}`
  )
}

const getManualSuggestionsPaginatedFundsList = async (props: TGetManualSuggestionsPaginatedFundsList) => {
  const { companyId, limit, page, archived = false } = props
  return api.get<TGetManualSuggestionsPaginatedFundsListResponseData>(
    `${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/manual-suggestions?limit=${limit}&page=${page}&archived=${archived}`
  )
}

const putAlbertSuggestionsAcceptGrouping = async (props: TPutSmartGrouping) => {
  const { companyId, shareholderGroupId, shareholdersIds } = props
  return api.put(
    `${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/albert-suggestions/${shareholderGroupId}/accept-grouping`,
    { shareholderIds: shareholdersIds }
  )
}

const postGroupAllAlbertSuggestions = async (props: TGetSmartGrouping) => {
  const { companyId } = props
  return api.post(`${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/albert-suggestions/group-all`)
}

const putManualSuggestionsManualGrouping = async (props: TPutSmartGrouping) => {
  const { companyId, shareholderGroupId, shareholdersIds } = props
  return api.put(
    `${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/manual-suggestions/${shareholderGroupId}/group`,
    { shareholderIds: shareholdersIds }
  )
}

const putManualSuggestionsGroupLater = async (props: TPutSmartGrouping) => {
  const { companyId, shareholdersIds } = props
  return api.put(`${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/manual-suggestions/archive`, {
    shareholderIds: shareholdersIds,
  })
}

const postCreateGroup = async (props: TPostCreateGroup) => {
  const { companyId, shareholderGroupName } = props
  return api.post(`${MZ_IRM_NEW}/shareholders/companies/${companyId}/smart-grouping/group`, {
    shareholderGroupName,
  })
}

export {
  getAlbertSuggestions,
  getAlbertSuggestionsData,
  getGroups,
  getManualSuggestionMaxPage,
  getManualSuggestionsPaginatedFundsList,
  getSmartGroupingHighlights,
  getSmartGroupingSummary,
  postCreateGroup,
  postGroupAllAlbertSuggestions,
  putAlbertSuggestionsAcceptGrouping,
  putManualSuggestionsGroupLater,
  putManualSuggestionsManualGrouping,
}
