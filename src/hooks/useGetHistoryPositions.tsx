import { api, MZ_IRM_NEW } from 'globals/api'
import { TPositionType } from 'pages/history/components/shareholder-base'

type GetHistoryPositionsArgs = {
  tickerId: string | number
  limit: number
  orderBy: string
  direction: string
  status: string | number
  startDate: string
  endDate: string
}

type ResponseData = {
  data: Array<TPositionType>
  success: boolean
}

const getHistoryPositions = async ({
  tickerId,
  limit,
  orderBy,
  direction,
  status,
  startDate,
  endDate,
}: GetHistoryPositionsArgs) => {
  const params = { limit, orderBy, direction, status, startDate, endDate }
  const response = await api.get<ResponseData>(`${MZ_IRM_NEW}/tickers/${tickerId}/position-batches`, { params })
  return response.data
}

export { getHistoryPositions }
