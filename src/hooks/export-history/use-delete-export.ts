import { api, MZ_IRM_NEW } from 'globals/api'
import { ExportHistoryDeleteReport } from 'errors'

type TDeleteExport = {
  companyId: string
  reportId: string
  reason: string
}

const deleteExportReport = async ({ companyId, reportId, reason }: TDeleteExport) => {
  try {
    const response = await api.delete(`${MZ_IRM_NEW}/management/companies/${companyId}/reports/${reportId}`, {
      data: { reason },
    })

    if (response.status >= 400) throw new ExportHistoryDeleteReport()
  } catch (err) {
    throw new ExportHistoryDeleteReport()
  }
}

export { deleteExportReport }
