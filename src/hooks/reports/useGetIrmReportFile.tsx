import { AxiosResponse } from 'axios'
import { api, MZ_IRM_NEW } from 'globals/api'

export type IGetIrmReportFileRequest = {
  reportId: string
  companyId: string
}

export type IGetIrmReportFileResponse = AxiosResponse<Buffer>

export const getIrmReportFile = async (params: IGetIrmReportFileRequest): Promise<IGetIrmReportFileResponse> => {
  const { reportId, companyId } = params
  const response = await api.get(`${MZ_IRM_NEW}/management/companies/${companyId}/irm-reports/${reportId}/download`, {
    responseType: 'arraybuffer',
  })
  return response
}
