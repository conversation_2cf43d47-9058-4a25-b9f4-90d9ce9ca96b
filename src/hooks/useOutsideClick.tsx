import { useEffect, useCallback } from 'react'

type TUseOutsideClick = {
  ref: React.RefObject<HTMLDivElement>
  callback(): void
}

export const useOutsideClick = ({ ref, callback }: TUseOutsideClick) => {
  const handleClick = useCallback(
    (event: Event) => {
      const e = event as MouseEvent
      if (ref.current && !ref.current.contains(e.target as Node)) {
        callback()
      }
    },
    [ref, callback]
  )

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      handleClick(event)
    }

    document.addEventListener('click', handleClickOutside)

    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [ref, callback, handleClick])
}
