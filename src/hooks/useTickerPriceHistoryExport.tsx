import { BaseError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { GetMonitoredShareholdersError } from 'pages/monitoring/errors/GetMonitoredShareholdersError'

export type TPostTickerPriceHistoryExport = {
  companyId: string
  companyName: string
  ticker: string
  tickerId: string
  stockType: string
  startDate: string
  endDate: string
  language: string
}

type ResponseData = {
  success: boolean
  data: unknown
}

const postTickerPriceHistoryExport = async ({
  companyId,
  companyName,
  ticker,
  tickerId,
  stockType,
  startDate,
  endDate,
  language,
}: TPostTickerPriceHistoryExport) => {
  try {
    const response = await api.post<ResponseData>(
      `${MZ_IRM_NEW}/management/companies/${companyId}/tickers/${tickerId}/historical-price`,
      {
        tickerCode: ticker,
        startDate,
        endDate,
        stockType,
        companyName,
        language,
      }
    )

    const { status } = response

    if (status >= 400) throw new GetMonitoredShareholdersError()
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new GetMonitoredShareholdersError()
  }
}

export { postTickerPriceHistoryExport }
