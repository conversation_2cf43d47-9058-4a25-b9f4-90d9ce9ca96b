import { AxiosProgressEvent } from 'axios'
import { BaseError, SpreadsheetGroupingError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

type TPostSpreadsheetGrouping = {
  file: File
  companyId: string
  tickerId: string
  referenceDate: string
  language: string
  importType?: 'replace' | 'merge'
  onUploadProgress?(progressEvent: AxiosProgressEvent): void
}

type ResponseData = {
  data: unknown
}

const postSpreadsheetGrouping = async ({
  file,
  companyId = '',
  tickerId = '',
  referenceDate,
  language,
  importType = 'replace',
  onUploadProgress,
}: TPostSpreadsheetGrouping) => {
  try {
    const payload = new FormData()
    payload.append('groupingBase', file, file.name)
    payload.append('notify', 'true')
    payload.append('tickerId', tickerId)
    payload.append('referenceDate', referenceDate)
    payload.append('language', language)
    payload.append('importType', importType)

    const config = { onUploadProgress }
    const response = await api.post<ResponseData>(
      `${MZ_IRM_NEW}/company/${companyId}/group/groupingBase`,
      payload,
      config
    )

    const { data } = response.data

    return data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new SpreadsheetGroupingError()
  }
}

export { postSpreadsheetGrouping }
