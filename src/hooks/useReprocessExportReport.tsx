import { BaseError, ReprocessExportReportError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

type TPostReprocessExportReport = {
  companyId: string
  shareholderReportId: string
}

const postReprocessExportReport = async ({ companyId, shareholderReportId }: TPostReprocessExportReport) => {
  try {
    const response = await api.post(
      `${MZ_IRM_NEW}/management/companies/${companyId}/reports/${shareholderReportId}/reprocess`
    )

    return response
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new ReprocessExportReportError()
  }
}

export { postReprocessExportReport }
