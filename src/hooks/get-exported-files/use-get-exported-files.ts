import { GetExportedFilesError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { TGetExportedFilesResponse, TGetExportedFilesProps } from './use-get-exported-files.types'

export const getExportedFiles = async (props: TGetExportedFilesProps) => {
  try {
    const { companyId, params } = props

    const uri = `${MZ_IRM_NEW}/management/companies/${companyId}/reports`
    const { data } = await api.get<TGetExportedFilesResponse>(uri, {
      params,
    })

    return data
  } catch (error: unknown) {
    throw new GetExportedFilesError()
  }
}
