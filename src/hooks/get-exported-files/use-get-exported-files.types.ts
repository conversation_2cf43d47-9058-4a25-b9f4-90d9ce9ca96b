export type TExportHistoryStatusCode = 'requested' | 'processing' | 'uploading' | 'finished' | 'failed'

export type TExportHistoryActionButtonTypes = 'excel' | 'email'

export type TExportedFile = {
  reportId: string
  ticker: string
  label: string
  tickerId: string
  referenceDate: string
  reportType: string
  createdAt: string
  status: TExportHistoryStatusCode
  meta: {
    companyId?: string
    tickerId?: string
    shareholderGroupId?: string
  }
  reportModel: TExportHistoryActionButtonTypes
  reportFileKey: string | null
  origin: string
  fileName: string
}

export type TGetExportedFilesResponse = TExportedFile[]

export type TGetExportedFilesProps = {
  companyId: string
  params: TFilterParams
}

type TFilterParams = {
  tickerId: string | undefined
  referenceDate: string | undefined
  reportType: string | undefined
  reportModel: string | undefined
  status: string | undefined
}
