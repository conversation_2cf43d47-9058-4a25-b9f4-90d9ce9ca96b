import { GetReportFileError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { TGetReportFile } from './use-get-report-file.types'

export const getReportFile = async (props: TGetReportFile) => {
  try {
    const { reportId } = props

    const uri = `${MZ_IRM_NEW}/reports/${reportId}/file`
    const response = await api.get(uri, {
      responseType: 'arraybuffer',
    })

    return response
  } catch (error: unknown) {
    throw new GetReportFileError()
  }
}
