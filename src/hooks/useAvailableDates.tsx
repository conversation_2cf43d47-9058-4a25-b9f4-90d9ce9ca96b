import { api, MZ_IRM_NEW } from 'globals/api'
import { GetMonitoredShareholdersError } from 'pages/monitoring/errors/GetMonitoredShareholdersError'

type PostAvailableDatesProps = {
  companyId?: string
  tickerId?: string
}

type ResponseData = {
  success: boolean
  data: string[]
}

const getAvailableDates = async ({ companyId = '', tickerId = '' }: PostAvailableDatesProps) => {
  try {
    const response = await api.get<ResponseData>(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/positionDates`)

    const { success, data } = response.data

    if (!success) throw new GetMonitoredShareholdersError()

    return data
  } catch (err) {
    throw new GetMonitoredShareholdersError()
  }
}

export { getAvailableDates }
