import { BaseError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'
import { GetMonitoredShareholdersError } from 'pages/monitoring/errors/GetMonitoredShareholdersError'

import { DocumentType } from 'types/shareholders'

import { formatDocument, types, getTranslatedDocumentType } from 'utils'

export type MonitoredShareholder = {
  contactId: string
  name: string
  document: string
  documentType: DocumentType
}

export type SerializedMonitoredShareholder = {
  contactId: string
  name: string
  document: string
  documentType: DocumentType
  translatedShareholderType: string
  shareholderImage: string
}

type ResponseData = {
  success: boolean
  data: MonitoredShareholder[]
}

type GetMonitoredShareholdersProps = {
  search?: string | number
  classification?: string
  limit?: number
  offset?: number
}

const serialize = (shareholders: MonitoredShareholder[]): SerializedMonitoredShareholder[] => {
  const serializedData = shareholders.map((shareholder) => {
    return {
      ...shareholder,
      document: formatDocument(shareholder.document),
      translatedShareholderType: getTranslatedDocumentType(shareholder.documentType),
      shareholderImage: types.SHAREHOLDER.documentTypesImages[shareholder.documentType],
    }
  })
  return serializedData
}

const getMonitoredShareholders = async ({
  search = '',
  classification = '',
  limit = 10,
  offset = 0,
}: GetMonitoredShareholdersProps) => {
  try {
    const params = { search, classification, limit, offset }
    const response = await api.get<ResponseData>(`${MZ_IRM_NEW}/shareholders/monitored`, { params })

    const { success, data } = response.data

    if (!success) throw new GetMonitoredShareholdersError()

    return data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new GetMonitoredShareholdersError()
  }
}

const getSerializedMonitoredShareholders = async (params: GetMonitoredShareholdersProps) => {
  const shareholders = await getMonitoredShareholders(params)
  return serialize(shareholders)
}

export { getMonitoredShareholders, getSerializedMonitoredShareholders }
