import { BaseError, DeleteSpreadsheetGroupingError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

export type TDeleteSpreadsheetGrouping = {
  companyId: string
  referenceDate: string
  language: string
}

export const deleteSpreadsheetGrouping = async (params: TDeleteSpreadsheetGrouping) => {
  const { companyId, referenceDate, language } = params
  try {
    const uri = `${MZ_IRM_NEW}/companies/${companyId}/spreadsheet-grouping/backup`

    const response = await api.post(uri, { referenceDate, language })
    return response.data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new DeleteSpreadsheetGroupingError()
  }
}
