import { api, MZ_IRM_NEW } from 'globals/api'

type HistoryReprocessBase = {
  tickerId: string | number
  positionBatchId: string
}

type ResponseData = ArrayBuffer

const getHistoryPositionFile = async ({ tickerId, positionBatchId }: HistoryReprocessBase) => {
  const response = await api.get<ResponseData>(
    `${MZ_IRM_NEW}/tickers/${tickerId}/position-batches/${positionBatchId}/file`,
    {
      responseType: 'arraybuffer',
    }
  )
  return response
}

export { getHistoryPositionFile }
