import { TOptions } from 'i18next'
import en from './translations-en.json'
import pt from './translations-pt.json'

const translations = { ...en, ...pt }

export type TTranslations = typeof translations

export type TKeyOf<T> = {
  [K in keyof T & (string | number)]: T[K] extends object ? `${K}.${TKeyOf<T[K]>}` : `${K}`
}[keyof T & (string | number)]

export type TTranslationsKeys = TKeyOf<TTranslations>

export type TParams<T> = TOptions & T
