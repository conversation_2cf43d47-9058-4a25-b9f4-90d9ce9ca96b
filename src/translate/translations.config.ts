import i18next from 'i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

import storeInformation from 'utils/storeInformation'
import { TTranslationsKeys, TParams } from './translations.types'

import enUS from './translations-en.json'
import ptBR from './translations-pt.json'

const resources = { 'pt-BR': { translations: { ...ptBR } }, 'en-US': { translations: { ...enUS } } }

const i18nInstance = i18next.createInstance()

function normalizeLanguage(language: string | null) {
  if (!language) return undefined
  return language.toLowerCase().startsWith('pt') ? 'pt-BR' : 'en-US'
}

i18nInstance.use(LanguageDetector).init({
  lng: normalizeLanguage(storeInformation.getLanguage()),
  resources,
  preload: ['pt-BR', 'en-US'],
  supportedLngs: ['pt-BR', 'en-US'],
  fallbackLng: 'en-US',
  interpolation: {
    escapeValue: false,
  },
  defaultNS: 'translations',
})

export const i18n = {
  ...i18nInstance,
  language: i18nInstance.language as 'pt-BR' | 'en-US',
  t: <T>(key: TTranslationsKeys, options?: TParams<T>) => i18nInstance.t(key, options),
}
