{"globals": {"help": "Help", "currentLanguage": "0", "actions": "Actions", "DatePickerDateFormat": "MM/dd/yyyy", "dateFormatAmPm": "MM/dd/yyyy hh:mm a", "errors": {"selectedTickerNotFound": {"title": "Selected ticker not found", "message": "Try again later"}, "requestFail": {"title": "Something went wrong", "message": "Please try again in a few minutes."}, "noPortfolioData": {"title": "Portfolio not found", "message": "It's necessary to register a Portfolio in the Intelligence product to load the data on this screen."}, "createGroup": {"title": "Something went wrong.", "message": "Unable to create group. Please check if a group with that name exists or try again in a few minutes."}, "vinculateGroup": {"title": "Something went wrong.", "message": "Unable to vinculate shareholder to the group. Please check if the shareholder is already vinculated to a group or try again in a few minutes."}, "deleteMonitoredList": {"title": "Something went wrong", "message": "It was not possible to delete the monitored list. Please try again in a few minutes."}, "deleteGrouping": {"title": "Something went wrong", "message": "The current grouping could not be deleted. Please try again later."}, "uploadError": {"title": "Something went wrong", "message": "Unable to upload file. Please try again later."}, "classifications": {"list": {"title": "Something went wrong", "message": "Unable to load the list of classifications. Please try again later."}, "create": {"title": "Something went wrong", "message": {"unknown": "Unable to create classification. Please try again later.", "alreadyExists": "Classification already exists. Please try another name."}}, "delete": {"title": "Something went wrong", "message": {"unknown": "Unable to delete classification. Please try again later."}}, "edit": {"title": "Something went wrong", "message": {"unknown": "Unable to edit classification. Please try again later."}}, "vinculate": {"title": "Something went wrong", "message": "The shareholder could not be vinculated on the selected classification. Check if the shareholder already has vinculated classification or try again later."}, "unvinculate": {"title": "Something went wrong", "message": "It was not possible to unvinculate the shareholder in the selected classification. Try again later."}}, "exportHistory": {"list": {"title": "Something went wrong", "message": "Unable to load the export list. Please try again later."}, "file": {"title": "Something went wrong", "message": "Unable to load the export file. Please try again later."}, "delete": {"title": "Something went wrong", "message": "Unable to delete the export. Please try again later."}}, "shareholderBaseHistory": {"delete": {"title": "Something went wrong", "message": "Unable to delete the shareholder base. Please try again later."}}, "toastError": {"title": "Something went wrong", "message": "useToast must be used inside a ToastProvider"}}, "backup": {"success": {"title": "Success", "message": "A backup is being processed and will soon be available on the Exports page, then the deletion will be carried out automatically."}}, "uploadToastfy": {"success": {"title": "Success", "message": "Upload completed successfully. You can access the previous grouping backup from the Exports page."}}, "ungroupedToastfy": {"success": {"title": "Concluded", "message": "The shareholder was successfully ungrouped from the group."}, "errors": {"title": "Something went wrong", "message": "Unable to ungroup shareholder at this time. Please try again later"}}, "groupedShareholderActionsToastfy": {"success": {"title": "Shareholder successfully grouped!", "message": "To see more details select one of the options below:", "groupDetails": "Click to see group details:"}, "errors": {"createGroup": {"title": "Something went wrong.", "message": "Unable to create group. Please check if a group with that name exists or try again in a few minutes."}, "vinculateGroup": {"title": "Something went wrong.", "message": "Unable to vinculate shareholder to the group. Please check if the shareholder is already vinculated to a group or try again in a few minutes."}, "alreadyGrouped": {"title": "Something went wrong", "description": "The shareholder already belongs to a group"}}}, "toasts": {"changeShareholderName": {"success": {"title": "Success!", "message": {"individual": "The shareholder name was successfully changed.", "group": "The group name was successfully changed."}}, "error": {"title": "Something went wrong", "message": {"individual": "Unable to change shareholder name. Please check if the new name is the same as your current one or try again later.", "group": "Unable to change group name. Please check if the new name is the same as your current one or try again later."}}}, "newShareholder": {"success": {"title": "Success!", "description": "The shareholder was created successfully, click here to access the details:"}, "error": {"title": "Something went wrong", "description": "Unable to create shareholder, please try again later."}}, "vinculateShareholderOnGroup": {"success": {"title": "Success!", "description": "The shareholder was successfully vinculated on the group"}, "error": {"title": "Something went wrong", "description": "It was not possible to vinculate the shareholder on the group. Try again later"}}, "deleteShareholdersBase": {"success": {"title": "Success!", "description": "The shareholder base was successfully deleted."}, "error": {"title": "Something went wrong", "description": "Unable to delete the shareholder base. Please try again later."}}}, "infos": {"noBaseListFound": {"title": "Attention", "message": "There is no base list available."}}, "export": {"sent": {"title": "Information", "message": "Your request has been sent"}, "success": {"title": "Success", "message": "Your file is being processed and will soon be available in your export history."}, "error": {"title": "Something went wrong", "message": "Unable to export the requested report. Please try again in a few minutes."}, "file": {"title": "File export", "message": "Check your browser's downloads folder"}, "delete": {"title": "Success", "message": "The export was successfully deleted."}}, "reprocess": {"success": {"title": "Success", "message": "Your report is being reprocessed and will soon be available for download."}, "error": {"title": "Something went wrong", "message": "We were unable to reprocess your report, please try again later."}}, "goToHistory": "Go to History", "downloadFile": "Download File", "headerContent": {"date": "Date", "stockType": "Stock type", "quantity": "Quantity"}, "imported": "Imported", "overwritten": "Overwritten", "awaitingDeletion": "Awaiting Deletion", "loading": "Loading...", "referenceDate": "Reference date", "status": "Status", "uploadedAt": "Uploaded at", "startDate": "Start date", "endDate": "End date", "dataError": "Data error", "cancel": "Cancel", "addFund": "Add fund", "noInformationFound": "No information found.", "percentage": "Percentage", "expand": "Expand", "retract": "Retract", "retractAll": "Retract all", "orderBy": "Order by", "expandAll": "Expand all", "descending": "Descending", "ascending": "Ascending", "grouped": "Grouped", "ungrouped": "Ungrouped", "all": "All", "allShareholders": "All shareholders", "uploaded": "Uploaded", "processing": "Processing", "replaced": "Replaced", "failure": "Failure"}, "statusError": {"errorCountries": "<PERSON><PERSON><PERSON> (Countries)", "errorPositionInfo": "Error (Position Info)", "errorCustodianBank": "Error (Custodian Bank)", "errorDownloadingFiles": "Error (Downloading Files)", "errorExtractingDates": "Error (Extracting Dates)", "errorReadingFiles": "Error (Reading Files)", "errorStockAmount": "Error (Stock Amount)", "errorDiffDates": "Error (Diff Dates)", "errorLoadingHolidays": "Error (Loading Holidays)", "invalidDocuments": "Invalid document(s)", "failToUpload": "Fail to upload"}, "components": {"uploadModal": {"Title": "Groups Update", "InfoText": "Manage the groups of your shareholders base by importing the updated version of your template worksheet here.", "InfoText2": "If you haven't done so before, download the template worksheet.", "downloading": "Downloading...", "DownloadWorkSheet": "Download template worksheet", "InfoText3": "Each new import promotes alteration/inclusion in the shareholder groups.", "acceptedFiles": "Accepted files", "Dropzone": {"DragOrClick": "Drag or click here"}, "monitoredShareholders": {"title": "Update list", "infoText": "<p>This feature allows updating the list of supervised people. Download the spreadsheet template and import the update version.</p><p>Each upload promotes a change or inclusion of people in the list, but never deletes people who are not in the spreadsheet. This allows the control of the supervised shareholders to be made using more than one spreadsheet source.</p>"}}, "actionsDropdownOptions": {"actions": "Actions", "deleteList": "Delete list", "deleteGrouping": "Delete grouping", "import": "Import", "newGroup": "New group", "update": "Update", "export": "Export", "exportScreen": "Export screen", "exportList": "Export list", "exportGrouping": "Export grouping"}, "confirmModal": {"confirmButton": "Confirm", "cancelButton": "Cancel"}, "groupModal": {"searchGroup": "Search group", "newGroup": "New group", "groupModalButton": "Group"}, "newGroupModal": {"placeholder": "Enter the group name...", "createGroup": {"success": {"title": "Success!", "message": "Group created successfully."}}}, "deleteModal": {"deleteButton": "Yes, I'm sure", "cancelButton": "Cancel"}, "reasonModal": {"warningLength": "Min. {{minLength}} and max. {{maxLength}} characters."}, "tour": {"back": "Back", "close": "Close", "last": "Finish", "next": "Next", "skip": "<PERSON><PERSON>"}}, "tickerPriceHistory": {"Date": "Date", "OpeningPrice": "Opening price", "ClosingPrice": "Closing price", "LowPrice": "Low price", "HighPrice": "High price", "Volume": "Volume", "Ticker": "Stock type", "StartDateLabel": "Start date", "EndDateLabel": "End date", "Export": "Export", "DataNotFound": "Data not found", "success": "Success", "exportMessage": "Your file is being processed and will soon be available in your export history."}, "shareholderOverview": {"date": "Date", "initialPosition": "Initial Position", "variation": "Variation", "finalPosition": "Final Position", "chartPosition": "Position", "patrimony": "Patrimony", "averagePrice": "Average Price", "closingPrice": "Closing Price", "lowPrice": "Low Price", "highPrice": "High Price", "minMovement": "Min. Movement", "maxMovement": "Max. Movement", "bestCase": "Best Case: ", "bestCaseDisclaimer": "scenario that simulates purchase(s) at the minimum price and sale(s) at the maximum price in the selected period.", "worstCase": "Worst Case: ", "worstCaseDisclaimer": "scenario that simulates purchase(s) at maximum price and sale(s) at minimum price in the selected period.", "closingPriceDisclaimer": "The Average Price shown is the VWAP (Volume Weighted Average Price) or average day trade price. When the change in position is negative, the average price shown will be the closing price at the beginning of the selected period.", "classificationDropdownPlaceholder": "None", "newClassification": "New classification", "unvinculate": "Unlink", "unvinculateModalTitle": "Unlink classification", "unvinculateModalMessage": "Are you sure you want to proceed with unlinking this shareholder's classification?", "classificationToasts": {"vinculate": {"success": {"title": "Success!", "description": "The classification was linked successfully."}, "error": {"title": "Something went wrong", "description": "Unable to link category, please try again later."}}, "unvinculate": {"success": {"title": "Success!", "description": "The classification was successfully unlinked."}, "error": {"title": "Something went wrong", "description": "Unable to unlink the category, please try again later."}}, "createAndVinculate": {"title": "Success!", "description": "The classification was created and linked successfully."}}}, "ownership": {"add": "Add", "agroup": "Group", "city": "City", "classification": "Classification", "close": "Close", "closingPrice": "Closing price", "configure": "Configure", "country": "Country", "date": "Date", "editedBy": "Edited by:", "export": "Export", "finalVolume": "Final volume", "finalPriceVolume": "Final price/volume", "fund": "Fund", "fundGroupedSuccessfully": "Fund grouped successfully!", "group": "Group", "groupDetails": "Group details", "groupFund": "Group fund", "grouping": "Grouping", "individual": "Individual", "name": "Name", "noResultsFoundOnYourBase": "No results found on your base", "nowEnterTheDesiredAction": "Now, enter the desired action:", "peers": "Peers ($)", "position": "Position", "priceVolume": "Price/Volume", "quantity": "Quantity", "save": "Save", "search": "Search...", "shareholderDetails": "Shareholder details", "shareholderGroup": "Shareholder group", "shareholderType": "Shareholder type", "stockType": "Stock type", "thisShareholderAlreadyBelongsToAnotherGroup": "This shareholder already belongs to another group", "thisShareholderAlreadyBelongsToAGroup": "This shareholder already belongs to a group", "style": "Style", "$MM": "$MM", "$MMTooltip": "AUM or market value", "type": "Type", "value": "Value", "valueMM": "Value (R$ MM)", "view": "View", "volume": "Volume", "warning": "Warning", "_dateFormat": {"date": "MM/DD/YYYY", "dateSimple": "m/d/Y", "dateTime": "MM/DD/YYYY hh:mm", "dateTimeAmPm": "MM/DD/YYYY hh:mm A", "flatpickrTime": "h:i K", "is24Hours": false, "onlyTime": "hh:mm A"}, "_dropdownOptions": {"grouped": [{"value": "all", "label": "All"}, {"value": "grouped", "label": "Grouped"}, {"value": "notGrouped", "label": "Ungrouped"}], "shareholderTypes": [{"value": "0", "label": "All shareholders"}, {"value": "2", "label": "Individual"}, {"value": "1", "label": "Fund"}], "view": [{"value": "0", "label": "Simple"}, {"value": "1", "label": "Grouped"}, {"value": "2", "label": "Beta"}, {"value": "3", "label": "Beta 2"}]}}, "exportHistory": {"Status": {"Processing": "Processing", "InProcessing": "Processing", "Exported": "Exported", "ProcessingError": "Processing error", "Requested": "Requested file", "Creating": "Creating file", "Uploading": "Exporting file", "Finished": "Exported file", "Failed": "Processing error", "Preprocessing": "Preprocessing", "New": "New", "Partialsuccess": "Partialsuccess", "Canceled": "Canceled"}, "filters": {"ticker": "Ticker", "quantity": "Quantity", "reportType": "Type", "status": "Status"}, "fileName": "File name", "noHistoryFound": "No history found", "actions": {"download": "Download", "reprocess": "Reprocess", "sendAlert": "Send alert", "delete": "Delete"}, "reprocess": {"confirmTitle": "Confirmation", "confirmMessage": "Are you sure you want to <strong>reprocess</strong> this file?"}, "sendAlert": {"confirmTitle": "Confirmation", "confirmMessage": "Are you sure you want to resend the ", "toastTitle": "<PERSON><PERSON>", "toastMessage": "The alerts have been sent to the registered emails."}, "deleteModal": {"title": "Delete Export History", "label": "Type a reason for deleting this export."}, "tooltipMessageError": "\nError: File with invalid CPF/CNPJ.\nYour shareholder base has not been updated.\nPlease download the file from the custodian bank again and upload it to MZiQ.", "type": {"baseSummary": "Base summary", "closePriceExcel": "Ticker history", "contactTearsheetPrivate": "Private contact", "dailyPositions": "Daily position", "firmTearsheetPrivate": "Company", "Full": "Full report", "increaseAndDecreaseAlertEmail": "Increase and decrease alert", "Members": "Group members", "monitoredContactsBackup": "Monitored list backup", "monitoredDailyPositionExcel": "Supervised daily position", "monitoredMovementReportExcel": "Supervised report", "monitoredReportEmail": "Supervised shareholders alert", "monitoredReportExcel": "Supervised list", "NewShareholders": "New holders", "ninetyDayPositionsExcel": "Daily position 90 days", "ownershipBeta2": "Shareholder base (Beta 2)", "scraperImportFailureEmail": "Automatic import failure alert", "shareholderBase": "Shareholder base", "shareholderBaseAlbert": "Shareholder base albert", "shareholderPositionOverviewReport": "Shareholder position history (period)", "shareholderDailyPositionHistoricReport": "Shareholder position history", "shareholderGroupPositionReport": "Group position history (period)", "shareholderHistoricPositionGrouped": "Group position history", "shareholdersCharts": "Charts - table export", "shareholders": "Shareholders", "shareholdersGrouping": "Shareholders grouping", "shareholdersGroups": "Shareholders groups list", "spreadsheetGroupingBackup": "Spreadsheet grouping (backup)", "summaryReportEmail": "Shareholder base summary alert", "topBuyers": "Top buyers", "topHolders": "Top holders", "topHoldersMovement": "Top holders movement", "topNewHolders": "New holders", "topSellers": "Top sellers", "topVariation": "Shareholder activity", "topZeroedPositions": "Zeroed positions", "vinculatedShareholders": "Vinculated shareholders"}}, "shareholderBaseHistory": {"deleteModal": {"title": "Delete Shareholder Base", "label": "Type a reason for deleting this base."}}, "reports": {"zeroedTooltip": "Shareholders with zero positions will not be shown under any circumstances", "zeroedLabel": "Don't show zeroed positions", "visualizationLabel": "Visualization", "value": "Value", "startValue": "Start value", "finalValue": "Final Value", "shares": "Shares", "monetary": "Monetary", "tooltips": {"loadButton": "When all supervised shareholders is selected, the report will be downloaded."}}, "shareholders": {"classificationName": "Classification name", "uploadBaseWarning": "Your file is being processed. Your share base will be updated shortly.", "uploadBaseError": "There was a problem importing the last group. Your stock base is out of date.", "exportSuccessTitle": "Shareholders export", "exportGroupsSuccessTitle": "Groups export", "exportSuccessMessage": "Your file is being processed and will soon be available in your export history.", "exportFailTitle": "Something went wrong", "exportFailMessage": "Please try again in a few minutes.", "deleteGroupingTitle": "Delete Grouping", "deleteGroupingMessage": "Are you sure you want to delete grouping? When deleting, the backup will be available in the Historicals > Exports menu.", "editClassificationTitle": "Edit Classification", "editClassificationSuccess": "Classification successfully updated", "deleteClassificationTitle": "Delete classification", "deleteClassificationMessage": "Are you sure you want to delete this classification? Confirming this action will leave the shareholders assosciated with it without assignment", "deleteClassificationSuccess": "Classification successfully deleted", "mergeDataLabel": "Merge data with current base", "toasts": {"success": {"title": "Completed", "description": "Your file is being processed and will soon be available in your export history."}, "info": {"title": "Information", "description": "Your request has been sent."}, "error": {"title": "Something went wrong", "description": "Please try again in a few minutes."}}}, "monitoring": {"load": "Load", "export": "Export", "classification": "Classification", "steps": {"stepOne": "Use the filters above to configure your daily position report", "stepTwo": "Upload a shareholder's report on screen or export the report however you prefer", "stepThree": "When exporting, you will receive an email notification once the file is available in the Export History menu"}, "dailyPosition": {"positionDate": "Position Date", "operation": "Operation", "stockQuantity": "Stock Amount", "closingPrice": "Closing Price", "totalValue": "Total Value"}, "interestGroup": {"pageContentPlaceholder": "Click on load to show the report", "emailMonitoredAlert": "When the alert is on, a report will be sent by email whenever a new shareholder base is imported.", "table": {"name": "Name", "document": "Document", "sharesChange": "Shares Change", "finalVolume": "Final Volume", "noNegotiation": "There was no negotiation of the supervised shareholders classified as"}, "upload": {"buttonLabel": "Upload", "success": {"title": "Success", "message": "Your monitored list has been updated successfully."}}, "deleteList": {"title": "Confirmation", "message": "Are you sure you want to delete the monitored list? When deleting a file, the backup will be available in the Historicals > Exports menu.", "success": {"title": "Success!", "message": "The monitored list was successfully deleted. You can view the backup in the Export History.", "goToExportHistory": "Go to History"}}}, "exportFail": "Something went wrong", "exportFailMessage": "Please try again in a few minutes.", "tooltipText": "Choose a period of maximum 35 days", "selectedShareholderTooltip": "When selecting all monitored, it is only possible to export the report. If you want to see data on screen, select a monitored one.", "exportDailyPosition": "Daily position export", "exportListMessage": "Your file is being processed and will soon be available in your export history.", "invalidDatesTitle": "Failed", "invalidDatesMessage": "You've selected more than 35 days. Select a smaller period.", "monitoredShareholdersSearchModal": {"name": "Name", "document": "Document", "type": "Type", "title": "Select supervised shareholder", "message": "Note:  When one supervised shareholder is selected, is possible to load the report on the screen. When <strong>all supervised shareholders is selected</strong>, the report will be downloaded."}, "errors": {"getMonitoredShareholders": {"title": "Something went wrong", "message": "Please try again in a few minutes."}, "noMonitoredShareholdersRegistred": {"title": "Attention", "message": "There is no registered shareholder for supervising. Go to the Monitoring > Interest group menu and download the template spreadsheet by clicking on the \"Export list\" button and upload the updated spreadsheet with the list of supervised people through the \"Update list\" button."}}, "monitored": "Monitored", "loadTooltip": "To load the report it is necessary to select a monitored one in the screen controls.", "monitoredExportListMessage": "After requesting the export, you can track the file being processed in the Export history section.", "success": "Success", "operations": {"buy": "BUY", "sell": "SELL"}}, "sideMenu": {"summary": "Summary", "keyStatistics": "Key statistics", "ownership": "Ownership", "reports": "Reports", "shareholders": "Shareholders", "charts": "Charts", "monitoring": "Monitoring", "interestGroup": "Interest Group", "dailyPosition": "Daily Position", "history": "Historicals", "ticker": "Ticker", "exports": "Exports", "uploads": "Shareholder bases", "ninetyDayPositions": "Position 90 days"}, "_dropdownOptions": {"grouped": [{"value": "all", "label": "All"}, {"value": "grouped", "label": "Grouped"}, {"value": "notGrouped", "label": "Ungrouped"}], "shareholderTypes": [{"value": "0", "label": "All shareholders"}, {"value": "2", "label": "Individual"}, {"value": "1", "label": "Fund"}], "view": [{"value": "0", "label": "Simple"}, {"value": "1", "label": "Grouped"}, {"value": "2", "label": "Beta"}]}, "_dateFormat": {"date": "MM/DD/YYYY", "dateSimple": "m/d/Y", "dateTime": "MM/DD/YYYY hh:mm", "dateTimeAmPm": "MM/DD/YYYY hh:mm A", "flatpickrTime": "h:i K", "is24Hours": false, "onlyTime": "hh:mm A"}, "pagePrivateTearSheet": {"titleConfirm": "Confirm", "removeTaskConfirmation": "Are you sure to proceed with this operation?", "errors": {"getExportHistoricPosition": {"title": "Something went wrong", "message": "Please try again in a few minutes."}}, "tearsheetModal": {"export": "Export", "selectOneOption": "Select one of the options below:", "selectedPeriod": "Selected Period", "selectedPeriodDescription": "All data will be exported in table format for the period selected on the screen.", "positionHistory": "Position History", "positionHistoryDescription": "Exports for daily historic shareholding positions will start from the initial position."}}, "pageGroupedTearSheet": {"addNote": "New note", "addTask": "New task", "newContact": "New contact", "cancel": "Cancel", "chartPlaceholder": "Click on LOAD button to see the chart", "chartPlaceholderNoResults": "No results", "date": "Date", "delete": "Delete", "deleteGroup": "Delete group?", "editNote": "Edit note", "endDate": "End date", "executors": "Executors", "export": "Export", "exportInfoModal": {"infoTitle": "Export in progress", "infoDescription": "Your file is being processed and will be available shortly on the “export history” page. You will also receive an email when the process has been completed."}, "tours": {"goToNewVersion": {"button": "Go to new version", "content": "There is a new version of this page. Click the link above to find out"}}, "filterShareholder": "Filter shareholders", "fundAssociationEmptyMessage": "This group is not linked with any institution yet.", "groups": "Groups", "marketValue": "Market Value ($MM)", "noInformationFound": "No information found.", "noPositoinHistoryFound": "No position history found", "noSectorFound": "No sector found", "noShareHolder": "No shareholder found", "noTopCountriesFound": "No countries found", "noTopPeersFound": "No peers found", "participants": "Participants", "percentageOfPort": "% of port", "position": "Position history", "publicInformation": "Public information", "removeTaskConfirmation": "Are you sure you want to proceed with unlinking the fund from this shareholder?", "save": "Save", "sectorAnalysis": "Sector analysis", "shareholderTypeIndividual": "Individual", "sharesChange": "Shares change", "showingPublicInformation": "Showing public information for", "startDate": "Start date", "subType": "Subtype", "tasks": "Tasks", "title": "Title", "titleConfirm": "Confirm", "to": "to", "top5Countries": "Top 5 countries", "topPeers": "Top 10 peers", "type": "Type", "typeShareholderNameOrDocument": "Enter shareholder name or document", "valueMM": "Value ($MM)", "viewType": "View type", "vinculatedShareholders": "Vinculated shareholders", "vinculateShareholder": "Vinculate shareholder", "seeIntelligenceDetails": "See details: Intelligence (Institution)", "bind": "Link", "unbind": "Unlink"}, "pageKeyStatistics": {"accessIntelligence": "Access Intelligence", "basicSharesM": "Basic shares (M)", "contactUs": "Contact us", "currency": "<PERSON><PERSON><PERSON><PERSON>", "deniedIntelligenceAccess": "To view this page, you must have permission to access the Intelligence application and have this company registered on IRM. If you are interested in knowing more about the product, ", "description": "Description", "errorRetrievingData": "Error retrieving data. Please, try again!", "sector": "Sector", "marketCap": "Market cap", "marketCapRule": "The market cap is calculated by multiplying the <i>total number of shares</i> by the <i>stock price</i> in <i>Dollar</i>.", "million": "Million", "orderOfMagnitude": "Order of magnitude", "quantity": "Quantity", "shares": "Shares", "shareholders": "Shareholders", "stockPrice": "Stock price", "stockPriceInDollar": "Stock price in Dollar", "styleAnalysis": "Style analysis", "totalShares": "Total of shares", "totalSharesRule": "Total number of shares of the company.", "turnoverAnalysis": "Turnover analysis"}, "ninetyDayPositions": {"load": "Load", "tooltipText": "Choose a period of up to 95 days", "export": {"success": {"title": "Success", "message": "Your file is being processed and will soon be available in your export history."}}, "errors": {"period": {"title": "Invalid period", "message": "You've selected a period superior of 95 days."}, "export": {"title": "Something went wrong", "message": "Please try again in a few minutes."}}, "steps": {"stepOne": "Utilize the filters above to configure your 90-day daily position report.", "stepTwo": "Click on the Load button to send the list of documents (Maximum limit of 5000 documents per report).", "stepThree": "The file will be available on the History menu, Exports option."}, "uploadFileModal": {"confirmButton": "Load", "title": "Upload list of documents", "infoText": "<p>To upload data, download the template spreadsheet.</p><p>After uploading the file, click on the {{confirmButtonLabel}} button.</p>", "templateLabel": "Download template spreadsheet"}}, "pageSmartGrouping": {"noSuggestion": "No suggestion", "grouped": "grouped", "suggestedGroup": "Suggested group", "suggestedGrouping": "Suggested grouping", "groupingSuggestion": "Grouping Suggestion", "manualGrouping": "Manual grouping", "acceptSuggestedGroup": "Accept suggested group", "acceptAllGroupingSuggestions": "Accept all suggestions", "newGrouping": "New grouping", "newGroup": "New group", "finishGroupingModalTitle": "Group", "confirmSuggestedGroupingModalTitle": "Confirm grouping suggestion", "confirmSuggestedGroupingModalSubtitle": "{{count}} fund will be grouped into \"{{groupName}}\".\nDo you wish to confirm?", "confirmSuggestedGroupingModalSubtitle_plural": "{{count}} funds will be grouped under \"{{groupName}}\".\nDo you wish to confirm?", "confirmAllGroupingSuggestionsSubtitle": "Are you sure you want to accept all {{count}} grouping suggestions?", "group": "Group", "groupModalButton": "Group", "searchGroup": "Search group", "groupedShareholders": "Grouped shareholders", "ungroupedShareholders": "Ungrouped shareholders", "manageYourGroupings": "Manage your groupings", "fundOrShareholder": "Fund/Shareholder", "funds": "Funds", "groupLater": "Group later", "noFundOrShareholderToGroup": "No Fund/Shareholder to group", "confirm": "Confirm", "cancel": "Cancel"}, "addButton": "Add", "percentageOfPort": "% of port", "changeName": "You are changing", "to": "to", "confirmChanges": "Do you wish to save?", "vinculatedFunds": "Vinculated funds", "startDate": "Start date", "endDate": "End date", "fundName": "Search shareholders", "shareholderTypeFund": "Fund", "managedBy": "Managed by", "errorRetrievingData": "Error retrieving data. Please, try again!", "address": "Address", "city": "City", "noResultsFound": "No results available / found", "toGroup": "Group", "details": "Details", "shareholder": "Shareholder", "positionHistory": "Position history", "firmContacts": "Firm contacts", "tearsheetPeriod": "Period", "tearsheetMaximum": "Maximum", "tearsheetLastSale": "Last sale", "tearsheetMinimum": "Minimum", "tearsheetVariation": "Variation", "tearsheetHistory": "History", "tearsheetFirstPurchase": "First purchase", "tearsheetShareholderOverview": "Shareholder Overview", "tearsheetPositionMovement": "Position Movement", "tearsheetBestCase": "Best Case", "tearsheetWorstCase": "Worst Case", "tearsheetProfit": "[Profit]", "tearsheetLoss": "[Loss]", "tearsheetTotalPurchase": "Total Purchase", "tearsheetTotalSales": "Total Sales", "days": "Days", "dataNotFound": "Data not found", "noFund": "No fund found.", "contacts": "Contacts", "chartPosition": "Position", "closePrice": "Close price", "price": "Price", "positionLegend": "Position", "representativeness": "Representativeness", "tickerTotalStock": "Total ticker stock", "priceLegend": "Price", "activities": "Tasks", "patrimony": "Patrimony", "finalVolume": "Final volume", "notesTitle": "Title", "noteModalNote": "Note", "noteModalPrivate": "Private Note", "noteModalSave": "Save", "btnExport": "Export", "reportChartDownload": "Report - Shareholder", "topHolders": "Top holders", "topBuyers": "Top buyers", "topSellers": "Top sellers", "zeroedPositions": "Zeroed Positions", "newHolders": "New Holders", "shareholderBase": "Shareholder base", "baseSummary": "Base summary", "monitoredReport": "Supervised Report", "download": "Download", "document": "Document", "step": "Step", "uploadIntro": "You haven't added any data to your shareholder base yet", "fileName": "File name", "change": "Shares change", "stockType": "Stock type", "viewType": "View", "date": "Date", "startVolume": "Start volume", "filter": "Filter", "processedAt": "Processed at", "totalShareholders": "Shareholders", "totalShareholdersUnidentified": "Unidentified shareholders", "dateFormat": "MM/DD/YYYY", "invalidDate": "Invalid date", "displayDateFormat": {"date": "MM/DD/YYYY", "dateTime": "MM/DD/YYYY hh:mm", "dateTimeAmPm": "MM/DD/YYYY hh:mm A"}, "noResults": "No results found on your base", "name": "Name", "email": "E-mail", "volume": "Volume", "priceVolume": "Price/Volume", "type": "Shareholder type", "groupedLabel": "Grouping", "country": "Country", "lastVolume": "Final volume", "variation": "Variation", "btnAddBase": "Add", "save": "Save", "select": "Select one...", "quantity": "Quantity", "quantityPurchased": "Purchased", "quantitySold": "Sold", "shareholderType": "Shareholder type", "search": "Search...", "new": "New", "groupInto": "Group into ...", "groupIntoANewGroup": "Group into a new group", "groupIntoExistingGroup": "Group into an existing group", "reportType": "Report", "btnLoad": "Load", "chart": "Chart", "table": "Table", "value": "Value", "loadChart": "Click on load to show chart data", "holders": "Holders", "holdersByCountry": "Holders by country", "shares": "Shares", "sharesByCountry": "Shares by country", "sharesAmount": "Shares amount", "sharesAmountByType": "Shares amount by type", "shareholderQuantityByType": "Shareholders percentage by type", "shareholderQuantity": "Shareholder percentage", "representativity": "%", "load": "Load", "totalAmount": "Total stocks", "newShareholder": "New shareholder", "newGroup": "New group", "groupName": "Group name", "cpf": "CPF", "cnpj": "CNPJ", "close": "Cancel", "fund": "Fund", "private": "Individual", "simple": "Simple", "grouped": "Grouped", "classification": "Classification", "edit": "Edit", "delete": "Delete", "loadReport": "Click on load to show report", "all": "All shareholders", "individual": "Individuals", "volumeMin": "Minimum", "volumeMax": "Maximum", "volumeAvg": "Average", "success": "Success", "finish": "Close", "fundSuccessfullyGrouped": "Fund successfully grouped!", "warning": "Warning", "groupedErrorMessage": "This shareholder already belongs to a group.", "successGroupedMessage": "Now, enter the desired action:", "groupDetail": "Group detail", "shareholderDetail": "Shareholder detail", "shareholderGroupTitle": "Shareholder group", "groupType": "Group", "stocksByNationality": "Shares by nationality", "shareholderByType": "Shareholders by type", "national": "National", "unknown": "Unknown", "foreign": "Foreign", "shareholderBaseIn": "Shareholder Base on", "variationsPositions": "Changes related to positions on", "stockQuantity": "Number of shares:", "shareholdersQuantity": "Number of shareholders (new; zeroed):", "calculatedVolume": "Volume traded (sum of the changes):", "exported": "Exported", "numberShareholders": "Number of shareholders", "numberShares": "Number of shares", "topShareholdersPerCountry": "Top holders by country", "topShareholdersPerCountryYAxis": "% of total shares", "topBuyersPerCountry": "Top buyers by country", "topBuyersPerCountryYAxis": "% of total purchased", "topBuyersPerCountrySeriesName": "Purchased shares:", "topSellersPerCountry": "Top sellers by country", "topSellersPerCountryYAxis": "% of total sold", "topSellersPerCountrySeriesName": "Shares sold:", "topHoldersMovement": "Top holders movement", "summaryDay": "Day", "summaryWeek": "Week", "summaryMonth": "30 Day", "summaryYear": "12 Month", "summaryShares": "Shares", "generatedDate": "Generated date", "ticker": "Ticker", "exportReportType": "Report type", "exportReport": "Export Report", "keepEditing": "Continue on the page", "history": "Go to the History page", "exportedModalSummary": "Your file is being processed and will be available briefly in the history section", "monitoredShareholders": "Supervised shareholders alert:", "executor": "Executor", "summary": "Summary", "action": "Action", "actions": "Actions", "emailMonitoredAlert": "When the alert is on, a report will be sent by email whenever a new shareholder base is imported.", "exportList": "Export list", "monitoredMov": "Shares Change", "monitoredExportListMessage": "After requesting the export, you can track the file being processed in the Export history section.", "emailAlert": "When the alert is on, a summary of the data will be sent by email whenever a new shareholder base is imported.", "overview": "View more", "groupLabel": "Group", "hasGroup": "This group already exists", "and": "and", "access": "Access", "uploadShareholderBase": "Upload your shareholder base", "ungroup": "Ungroup", "upload": "Upload", "portfolio": "Portfolio", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "bindConfirmationModal": {"title": "Attention", "messageFirst": "This shareholder group do not have public information.", "messageSecond": "Do you want to add it?", "ignore": "No, ignore", "confirm": "Yes, I want", "seeAgain": "I do not want to see this again"}}