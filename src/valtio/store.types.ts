export interface ShareholderGroup {
  id: string | null
  name: string | null
}

export interface ContactModalState {
  isOpen: boolean
  edit: unknown | null
  reload: boolean
  data: Record<string, unknown>
  search: string
  blockRedirect: boolean
  lastCreated: Record<string, unknown>
  tab: string | null
  shareholderGroup: ShareholderGroup
  allowSearch: boolean
}

export interface ActivitiesModalState {
  isOpen: boolean
  handleTypesUpdate: boolean
  taskId: string | null
  contactInfo: unknown | null
  tab: string | null
  emitReload: boolean
  shouldReload: boolean
  shareholderGroup: ShareholderGroup
  institutionId: string | null
}

export interface Store {
  ContactModal: ContactModalState
  ActivitiesModalVisibility: ActivitiesModalState
}
