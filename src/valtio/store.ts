import { proxy, ref } from 'valtio/vanilla'
import { Store } from './store.types'

export const store = proxy<Store>({
  ContactModal: {
    isOpen: false,
    edit: null,
    reload: false,
    data: ref({}),
    search: '',
    blockRedirect: false,
    lastCreated: ref({}),
    tab: null,
    shareholderGroup: ref({
      id: null,
      name: null,
    }),
    allowSearch: true,
  },
  ActivitiesModalVisibility: {
    isOpen: false,
    handleTypesUpdate: false,
    taskId: null,
    contactInfo: null,
    tab: null,
    emitReload: false,
    shouldReload: true,
    shareholderGroup: ref({
      id: null,
      name: null,
    }),
    institutionId: null,
  },
})
