import { ref } from 'valtio/vanilla'
import { store } from './store'
import { ShareholderGroup } from './store.types'

export const openContactModal = (shareholderGroup: ShareholderGroup, allowSearch = false) => {
  store.ContactModal.shareholderGroup = ref(shareholderGroup)
  store.ContactModal.allowSearch = allowSearch
  store.ContactModal.isOpen = true
}

export const closeContactModal = () => {
  store.ContactModal.isOpen = false
  store.ContactModal.edit = null
  store.ContactModal.reload = false
  store.ContactModal.data = ref({})
  store.ContactModal.search = ''
  store.ContactModal.tab = null
}

export const openActivitiesModal = (shareholderGroup: ShareholderGroup, taskId?: string | null) => {
  store.ActivitiesModalVisibility = {
    ...store.ActivitiesModalVisibility,
    isOpen: true,
    taskId: taskId || null,
    shareholderGroup: ref(shareholderGroup),
  }
}

export const closeActivitiesModal = () => {
  store.ActivitiesModalVisibility = {
    ...store.ActivitiesModalVisibility,
    isOpen: false,
    handleTypesUpdate: false,
    taskId: null,
  }
}
