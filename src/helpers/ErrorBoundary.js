import { Component } from 'react'
import PropTypes from 'prop-types'
// Error boundaries atualmente tem que ser classes.
export default class ErrorBoundary extends Component {
  constructor(props) {
    super(props)
    this.state = {
      hasError: false,
    }
  }

  static getDerivedStateFromError(error) {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error, errorInfo) {
    const { onFallback } = this.props
    if (onFallback) {
      onFallback(error, errorInfo)
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}

ErrorBoundary.propTypes = {
  fallback: PropTypes.node,
  onFallback: PropTypes.func,
  children: PropTypes.node,
}

ErrorBoundary.defaultProps = {
  fallback: null,
}
