import { IMAGES } from 'assets'
import { i18n } from 'translate'

export const NAME = i18n.t('sideMenu.shareholders')
export const PATH = '/shareholders'
export const SCOPE = '5e5c0a7a413fd0d8313d8143'
export const ICON = IMAGES.SHAREHOLDERS_ICON

export const prefix = {
  MZ_ANALYTICS_API: '/mzanalytics',
  MZ_CORE_V1: '/v1/core',
}

export const REPORT_STATUS = {
  NEW: 1,
  PREPROCESSING: 2,
  PROCESSING: 3,
  SUCCESS: 4,
  PARTIALSUCCESS: 5,
  ERROR: 6,
  CANCELED: 7,
}

// TYPES
// & COMMON
const topQuantity = [
  { value: 5, label: '5' },
  { value: 10, label: '10' },
  { value: 15, label: '15' },
  { value: 20, label: '20' },
  { value: 25, label: '25' },
  { value: 30, label: '30' },
  { value: 50, label: '50' },
  { value: 100, label: '100' },
  { value: 500, label: '500' },
  { value: 1000, label: '1000' },
]

const noFilterOption = { label: i18n.t('globals.all'), value: '' }
const topQuantitySummary = topQuantity.slice(0, 8)

export const COMMON = { topQuantity, topQuantitySummary, noFilterOption }

export const SHAREHOLDER = {
  // & SHAREHOLDER-ACTIONS
  ACTION_SHAREHOLDER_BASE: 'ACTION_SHAREHOLDER_BASE',
  ACTION_SHAREHOLDER_BASE_LOADING: 'ACTION_SHAREHOLDER_BASE_LOADING',
}

// MAP
export const APPLICATIONS_ENUM = {
  IRM: 'irm',
  IRM_NEW: 'irm-new',
  INTELLIGENCE: 'intelligence',
  MAILER: 'mailer',
  WEBCAST: 'webcast',
  FILEMANAGER: 'filemanager',
  INTEGRATIONS: 'integrations',
  BOT: 'bot',
  COMPANY_ADMIN: 'companyAdmin',
  RESTRICTED_AREA: 'restrictedarea',
  FUNDSMANAGER: 'fundsmanager',
}

export const APPLICATIONS = {
  [APPLICATIONS_ENUM.IRM]: '5e67bb6360d42669cc3b3b73',
  [APPLICATIONS_ENUM.IRM_NEW]: '5e5c0a7a413fd0d8313d8143',
  [APPLICATIONS_ENUM.INTELLIGENCE]: '5e5c0a845af7f7b51a5ebdee',
  [APPLICATIONS_ENUM.MAILER]: '5e5c0a8ad0b4fdbb56a3607a',
  [APPLICATIONS_ENUM.WEBCAST]: '5e59869fd3518402037d5a6d',
  [APPLICATIONS_ENUM.FILEMANAGER]: '5e5c0a983d03be33f0e30f52',
  [APPLICATIONS_ENUM.INTEGRATIONS]: '5e5c0a9fd9d80ec853a19673',
  [APPLICATIONS_ENUM.BOT]: '5e5c0aa5b6e28fdd1b748ffa',
  [APPLICATIONS_ENUM.COMPANY_ADMIN]: '5e5c0aadaf0fb9f1a8c5fa53',
  [APPLICATIONS_ENUM.RESTRICTED_AREA]: '5e5c0ac21b0ccbd7e876e212',
  [APPLICATIONS_ENUM.FUNDSMANAGER]: '5fae93ce3a382101c6252253',
}

// USED IN INTEGRATIONS MODAL FOR ENTITY MAPPING (IRM x SHAREHOLDERS x INTELLIGENCE)
export const ENTITY_SEARCH_CONTEXT = {
  INSTITUTION_SELECTION: 3,
  FIRM_SELECTION: 4,
}

// USED IN INTEGRATIONS MODAL FOR ENTITY MAPPING (IRM x SHAREHOLDERS x INTELLIGENCE)
export const APPLICATION_CONTEXT = {
  SHAREHOLDERS: 'SHAREHOLDERS',
}

export const ContentViewType = [
  { label: i18n.t('chart'), value: '1' },
  { label: i18n.t('table'), value: '2' },
]
