/**
 * Constants for MZ system external products
 * Used to configure redirections between products
 */

/**
 * Object with external product keys
 * Use these constants to reference products in components
 *
 * @example
 * <ExternalLink product={EXTERNAL_PRODUCT.IRM} path="/dashboard">
 *   Access IRM
 * </ExternalLink>
 */
export const EXTERNAL_PRODUCT = {
  INTELLIGENCE: 'INTELLIGENCE',
  IRM: 'IRM',
  DASHBOARD: 'DASHBOARD',
  SETTINGS: 'SETTINGS',
  ENGAGEMENT: 'ENGAGEMENT',
} as const

/**
 * Definition of external products with their configurations
 */
export type TExternalProduct = {
  /** Product base path */
  path: string
  /** Defines if the product uses its own subdomain */
  isSubdomain: boolean
  /** Subdomain name (without environment prefixes) */
  subdomain?: string
}

/**
 * Configuration for each external product
 * Contains information such as base path and subdomain configuration
 */
export const EXTERNAL_PRODUCTS_CONFIG = {
  [EXTERNAL_PRODUCT.INTELLIGENCE]: {
    path: '/intelligence',
    isSubdomain: false,
  },
  [EXTERNAL_PRODUCT.IRM]: {
    path: '/irm',
    isSubdomain: false,
  },
  [EXTERNAL_PRODUCT.DASHBOARD]: {
    path: '/dashboard',
    isSubdomain: false,
  },
  [EXTERNAL_PRODUCT.SETTINGS]: {
    path: '/settings',
    isSubdomain: false,
  },
  [EXTERNAL_PRODUCT.ENGAGEMENT]: {
    path: '/engagement',
    isSubdomain: true,
    subdomain: 'engagement',
  },
} as const

/** Type representing the keys of EXTERNAL_PRODUCT */
export type TExternalProductKey = (typeof EXTERNAL_PRODUCT)[keyof typeof EXTERNAL_PRODUCT]
