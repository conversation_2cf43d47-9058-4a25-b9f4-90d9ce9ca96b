import { api } from 'globals/api'

import { endpoints } from './endpoints'

class CoreAPI {
  private endpoints = endpoints

  async authenticateFrom<PERSON>ogto(accessToken: string) {
    const response = await api.post(
      this.endpoints.authenticateFromLogto(),
      {},
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    )

    return response.data
  }

  async logout() {
    await api.delete(this.endpoints.logout())
  }
}

export const coreAPI = new CoreAPI()
