body {
  margin: 0;
  padding: 0;
}

#loading {
  margin: 0;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #191c24;
}

.loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.loader-icon {
  width: 88px;
  height: 88px;
  background-color: #535f7b;
  -webkit-mask-image: url('../assets/svg/loading-screen-shareholders-icon.svg');
  mask-image: url('../assets/svg/loading-screen-shareholders-icon.svg');
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-position: center;
  mask-position: center;

  animation: loadingPulse 1.6s ease-in-out infinite;
}

.loader-spinner {
  width: 12px;
  height: 12px;
  border: 3px solid #33a0ff;
  border-top: 3px solid transparent;
  border-radius: 50%;
  animation: loadingSpinner 1s linear infinite;
}

@keyframes loadingPulse {
  0% {
    background-color: #33a0ff;
    opacity: 1;
  }
  50% {
    background-color: #33a0ff;
    opacity: 0.4;
  }
  100% {
    background-color: #33a0ff;
    opacity: 1;
  }
}

@keyframes loadingSpinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
