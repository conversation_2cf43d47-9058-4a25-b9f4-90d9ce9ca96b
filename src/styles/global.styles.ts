import { createGlobalStyle } from 'styled-components'

const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-decoration: none;
    font-family: 'Lato', sans-serif;
  }

  *:focus {
    outline: none;
  }

  body {
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    font-family: 'Lato', sans-serif;
    font-size: 14px;
    background-color: ${({ theme }) => theme.colors.neutral.grey[800]};
  }
`

export default GlobalStyles
