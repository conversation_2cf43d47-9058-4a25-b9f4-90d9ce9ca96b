import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { PageContainer } from './page-container.template'

describe('Page Container Template', () => {
  it('should render the page container correctly', () => {
    const { getByTestId } = customRender(<PageContainer data-testid="page-container" />)

    expect(getByTestId('page-container')).toBeInTheDocument()
  })
})
