import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { Page } from './page.template'

describe('Page Template', () => {
  it('should render the page correctly', () => {
    const { getByTestId, getByText } = customRender(
      <Page>
        <div>Children</div>
      </Page>
    )

    expect(getByTestId('page')).toBeInTheDocument()
    expect(getByText('Children')).toBeInTheDocument()
  })
})
