import styled from 'styled-components'

import { TDataNotFound } from './data-not-found.types'

export const DataNotFound = styled.div<TDataNotFound>`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  text-align: center;
  color: white;
  ${({ $height }) => $height && `height: ${$height};`}
  ${({ $noBorderBottom }) => ($noBorderBottom ? 'border-bottom: none;' : `border-bottom: 1px solid #344b69;`)}
  ${({ $fontSize }) => ($fontSize ? `font-size: ${$fontSize};` : `font-size: 13px;`)}
`
