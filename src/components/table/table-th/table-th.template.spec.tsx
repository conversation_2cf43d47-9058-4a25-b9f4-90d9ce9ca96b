import { customRender } from 'test'
import { describe, expect, it } from 'vitest'

import { TableTH } from './table-th.template'

describe('Table TH', () => {
  it('should be able to render the TH correctly', () => {
    const { getByTestId } = customRender(<TableTH data-testid="th" />)

    const th = getByTestId('th')

    expect(th).toBeInTheDocument()
    expect(th).toHaveStyleRule('text-align', 'left')
  })

  it('should be able to render the TH correctly when textAlign is used', () => {
    const { getByTestId } = customRender(<TableTH data-testid="th" $textAlign="right" />)

    const th = getByTestId('th')

    expect(th).toHaveStyleRule('text-align', 'right')
  })
})
