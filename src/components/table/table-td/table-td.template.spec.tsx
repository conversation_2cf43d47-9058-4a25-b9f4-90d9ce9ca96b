import { customRender } from 'test'
import { describe, expect, it } from 'vitest'

import { TableTD } from './table-td.template'

describe('Table TD', () => {
  it('should be able to render the TD correctly', () => {
    const { getByTestId } = customRender(<TableTD data-testid="td" />)

    const td = getByTestId('td')

    expect(td).toBeInTheDocument()
    expect(td).toHaveStyleRule('text-align', 'left')
  })

  it('should be able to render the TD correctly when textAlign is used', () => {
    const { getByTestId } = customRender(<TableTD data-testid="td" $textAlign="right" />)

    const td = getByTestId('td')

    expect(td).toBeInTheDocument()
    expect(td).toHaveStyleRule('text-align', 'right')
  })
})
