import { theme } from '@mz-codes/design-system'
import styled from 'styled-components'

import { TTableTD } from './table-td.types'

export const TableTD = styled.td<TTableTD>`
  position: relative;
  min-height: 45px;
  font-weight: 500;
  font-size: 14px;
  text-align: ${({ $textAlign }) => $textAlign || 'left'};
  padding: 20px;
  color: ${theme.legacy.colors.grayScale.texts};
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
`
