import { customRender } from 'test'
import { describe, expect, it } from 'vitest'

import { Table } from './index'

describe('Table Template', () => {
  it('should be able to render the Table Template correctly', () => {
    const { getByTestId, getByText } = customRender(
      <Table.Container data-testid="container">
        <Table.THead data-testid="thead">
          <Table.TR data-testid="thead-tr">
            <Table.TH>Head 01</Table.TH>
            <Table.TH>Head 02</Table.TH>
          </Table.TR>
        </Table.THead>
        <Table.TBody data-testid="tbody">
          <Table.TR data-testid="tbody-tr-01">
            <Table.TD>TR 01 - TD 01</Table.TD>
            <Table.TD>TR 01 - TD 02</Table.TD>
          </Table.TR>
          <Table.TR data-testid="tbody-tr-02">
            <Table.TD>TR 02 - TD 01</Table.TD>
            <Table.TD>TR 02 - TD 02</Table.TD>
          </Table.TR>
        </Table.TBody>
      </Table.Container>
    )

    const container = getByTestId('container')
    const thead = getByTestId('thead')
    const theadTr = getByTestId('thead-tr')
    const head01 = getByText('Head 01')
    const head02 = getByText('Head 02')
    const tbody = getByTestId('tbody')
    const tbodyTr01 = getByTestId('tbody-tr-01')
    const tbodyTr02 = getByTestId('tbody-tr-02')
    const cell01 = getByText('TR 01 - TD 01')
    const cell02 = getByText('TR 01 - TD 02')
    const cell03 = getByText('TR 02 - TD 01')
    const cell04 = getByText('TR 02 - TD 02')

    expect(container).toBeInTheDocument()
    expect(thead).toBeInTheDocument()
    expect(theadTr).toBeInTheDocument()
    expect(head01).toBeInTheDocument()
    expect(head02).toBeInTheDocument()
    expect(tbody).toBeInTheDocument()
    expect(tbodyTr01).toBeInTheDocument()
    expect(tbodyTr02).toBeInTheDocument()
    expect(cell01).toBeInTheDocument()
    expect(cell02).toBeInTheDocument()
    expect(cell03).toBeInTheDocument()
    expect(cell04).toBeInTheDocument()
  })
})
