import styled from 'styled-components'

export const TableTHead = styled.thead`
  display: block;
  position: relative;
  background-color: ${({ theme }) => theme.legacy.colors.neutral.contentBackground};
  width: 100%;
  top: 0;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: ${({ theme }) => theme.legacy.colors.neutral.contentBackground};
  }

  &::after {
    background: radial-gradient(ellipse at 50% 15px, rgba(0, 0, 0, 0.57) 0%, rgba(0, 0, 0, 0) 76%);
    background-color: rgba(0, 0, 0, 0.57);
    background-color: transparent;
    top: 20px;
    content: '';
    height: 60px;
    left: 0;
    position: absolute;
    right: 0;
    z-index: -1;
  }
`
