import { customRender } from 'test'
import { describe, expect, it } from 'vitest'

import { TableContainer } from './table-container.template'

describe('Table Container', () => {
  it('should be able to render the container correctly', () => {
    const { getByTestId } = customRender(<TableContainer data-testid="container" />)

    const container = getByTestId('container')

    expect(container).toBeInTheDocument()
  })
})
