import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { PageWrapperContainer } from './page-wrapper-container.template'

describe('Page Container Template', () => {
  it('should render the page wrapper container correctly', () => {
    const { getByTestId } = customRender(<PageWrapperContainer data-testid="page-wrapper-container" />)

    expect(getByTestId('page-wrapper-container')).toBeInTheDocument()
  })
})
