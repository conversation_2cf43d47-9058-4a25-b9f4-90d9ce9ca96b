import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import userEvent from '@testing-library/user-event'
import { TabItem } from './tab-item'
import { TabsTemplate } from './tabs.template'
import { TabContent } from './tab-content'

describe('Tabs Template', () => {
  it('should be able to render tabs and content', async () => {
    let activeTabState = 0

    const handleActiveTab = (tab: number) => {
      activeTabState = tab
    }

    const { getByText } = customRender(
      <TabsTemplate activeTab={activeTabState} handleActiveTab={handleActiveTab}>
        <TabItem label="Tab 1">
          <TabContent>Content 1</TabContent>
        </TabItem>
        <TabItem label="Tab 2">
          <TabContent>Content 2</TabContent>
        </TabItem>
      </TabsTemplate>
    )

    expect(getByText('Tab 1')).toBeInTheDocument()
    expect(getByText('Tab 2')).toBeInTheDocument()

    expect(getByText('Content 1')).toBeInTheDocument()

    await userEvent.click(getByText('Tab 2'))

    expect(activeTabState).toBe(1)
  })
})
