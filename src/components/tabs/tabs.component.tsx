import { useState } from 'react'
import { TTabs } from './tabs.types'
import { TabsTemplate } from './tabs.template'
import { TabItem } from './tab-item'

export function Tabs(props: TTabs) {
  const { children } = props

  const [activeTab, setActiveTab] = useState(0)

  const handleActiveTab = (tab: number) => {
    if (tab === activeTab) return
    setActiveTab(tab)
  }

  return (
    <Tabs.Template activeTab={activeTab} handleActiveTab={handleActiveTab}>
      {children}
    </Tabs.Template>
  )
}

Tabs.Template = TabsTemplate
Tabs.Item = TabItem
