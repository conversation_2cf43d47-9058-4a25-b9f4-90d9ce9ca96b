import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TabItem } from './tab-item.template'

describe('Tab Item Template', () => {
  it('should be able to render tab item', () => {
    const { getByText } = customRender(
      <TabItem label="Tab-1">
        <div>Child 1</div>
        <span>Child 2</span>
      </TabItem>
    )

    expect(getByText('Child 1')).toBeInTheDocument()
    expect(getByText('Child 2')).toBeInTheDocument()
  })
})
