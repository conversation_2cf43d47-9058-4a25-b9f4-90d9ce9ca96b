import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TabsContainer } from './tabs-container.template'

describe('Tab Container Template', () => {
  it('should be able to render tab container', () => {
    const { getByText } = customRender(
      <TabsContainer>
        <span>Child 1</span>
        <span>Child 2</span>
      </TabsContainer>
    )

    expect(getByText('Child 1')).toBeInTheDocument()
    expect(getByText('Child 2')).toBeInTheDocument()
  })
})
