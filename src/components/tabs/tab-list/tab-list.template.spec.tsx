import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TabList } from './tab-list.template'

describe('Tab List Template', () => {
  it('should be able to render tab list', () => {
    const { getByText } = customRender(
      <TabList>
        <span>Child 1</span>
        <span>Child 2</span>
      </TabList>
    )

    expect(getByText('Child 1')).toBeInTheDocument()
    expect(getByText('Child 2')).toBeInTheDocument()
  })
})
