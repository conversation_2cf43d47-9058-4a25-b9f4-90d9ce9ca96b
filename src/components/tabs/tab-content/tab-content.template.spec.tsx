import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TabContent } from './tab-content.template'

describe('Tab Content Template', () => {
  it('should be able to render styled tab content', () => {
    const initialChild = <div>Render me</div>

    const { getByTestId } = customRender(<TabContent data-testid="tab-content">{initialChild}</TabContent>)

    const tabContent = getByTestId('tab-content')

    expect(tabContent).toBeInTheDocument()
  })
})
