import React, { forwardRef } from 'react'
import { generateUniqueId } from 'utils'
import { TTab, TTabsTemplate } from './tabs.types'
import { TabList } from './tab-list'
import { TabContent } from './tab-content'
import { TabsContainer } from './tabs-container'
import { TabHandler } from './tab-handler'

export function BaseTabsTemplate(props: TTabsTemplate, ref: React.Ref<HTMLDivElement>) {
  const { children, activeTab, handleActiveTab } = props

  const tabs = React.Children.toArray(children) as React.ReactElement<TTab>[]

  return (
    <BaseTabsTemplate.Container ref={ref}>
      <BaseTabsTemplate.List>
        {React.Children.map(children, (child, index) => {
          return (
            <BaseTabsTemplate.Handler
              key={generateUniqueId()}
              onClick={() => handleActiveTab(index)}
              $active={!!(index === activeTab)}
            >
              {child.props.label}
            </BaseTabsTemplate.Handler>
          )
        })}
      </BaseTabsTemplate.List>

      <BaseTabsTemplate.Content>{tabs.length > 0 && tabs[activeTab]?.props.children}</BaseTabsTemplate.Content>
    </BaseTabsTemplate.Container>
  )
}

BaseTabsTemplate.List = TabList
BaseTabsTemplate.Container = TabsContainer
BaseTabsTemplate.Content = TabContent
BaseTabsTemplate.Handler = TabHandler

export const TabsTemplate = forwardRef(BaseTabsTemplate)
