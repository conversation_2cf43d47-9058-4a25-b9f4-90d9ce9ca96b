import { describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import userEvent from '@testing-library/user-event'
import { theme } from '@mz-codes/design-system'
import { TabHandler } from './tab-handler.template'

describe('Tab Handler Template', () => {
  it('should be able to render styled tab handler', () => {
    const clickHandler = vi.fn()
    const initialText = 'Render me'

    const { getByTestId } = customRender(
      <TabHandler $active={false} onClick={clickHandler} data-testid="tab-handler">
        {initialText}
      </TabHandler>
    )

    const tabHandler = getByTestId('tab-handler')

    expect(tabHandler).toBeInTheDocument()
  })

  it('should be able to click tab handler', async () => {
    const clickHandler = vi.fn()
    const initialText = 'Render me'

    const { getByTestId } = customRender(
      <TabHandler $active={false} onClick={clickHandler} data-testid="tab-handler">
        {initialText}
      </TabHandler>
    )

    const tabHandler = getByTestId('tab-handler')

    await userEvent.click(tabHandler)
    expect(tabHandler).toBeInTheDocument()
    expect(clickHandler).toHaveBeenCalledOnce()
  })

  it('should be able to render active styled tab handler', () => {
    const clickHandler = vi.fn()
    const initialText = 'Render me'

    const { getByTestId } = customRender(
      <TabHandler $active onClick={clickHandler} data-testid="tab-handler">
        {initialText}
      </TabHandler>
    )

    const tabHandler = getByTestId('tab-handler')

    expect(tabHandler).toBeInTheDocument()
    expect(tabHandler).toHaveStyleRule('color', theme.legacy.colors.primary.primary)
  })
})
