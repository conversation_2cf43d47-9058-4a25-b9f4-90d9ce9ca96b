import styled, { css } from 'styled-components'
import { theme } from '@mz-codes/design-system'
import { TTabHandler } from '../tabs.types'

export const TabHandler = styled.button<TTabHandler>(({ $active }) => {
  return css`
    position: relative;
    font-weight: 700;
    background: transparent;
    border: 0;
    padding: 21.5px 0;
    color: white;
    font-size: 14px;
    transition: all 0.3s;
    cursor: pointer;
    ${$active && `color: ${theme.legacy.colors.primary.primary};`}
    &::before {
      content: '';
      background-color: ${theme.legacy.colors.primary.primary};
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 2px;
      transition: all 0.3s;
      ${$active && `width: 50%;`}
    }
  `
})
