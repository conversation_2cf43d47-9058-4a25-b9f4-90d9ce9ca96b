import { ReactElement, ReactNode, RefObject } from 'react'

export type TTab = {
  label: string
  children: ReactNode
}

export type TTabContent = {
  children: ReactNode
}

export type TTabs = {
  children: ReactElement<TTab>[]
}

export type TTabsTemplate = TTabs & {
  activeTab: number
  handleActiveTab(tabIndex: number): void
  ref: RefObject<HTMLElement>
}

export type TTabItem = {
  label: string
  children: ReactElement<TTab | TTabContent> | ReactElement<TTab | TTabContent>[]
}

export type TTabHandler = {
  $active: boolean
}
