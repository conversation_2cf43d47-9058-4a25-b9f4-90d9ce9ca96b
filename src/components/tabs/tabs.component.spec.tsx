import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import userEvent from '@testing-library/user-event'
import { Tabs } from './tabs.component'

describe('Tabs Component', () => {
  it('should be able to render tabs and content', async () => {
    const { getByText } = customRender(
      <Tabs>
        <Tabs.Item label="Tab 1">
          <div>Content 1</div>
        </Tabs.Item>
        <Tabs.Item label="Tab 2">
          <div>Content 2</div>
        </Tabs.Item>
      </Tabs>
    )

    expect(getByText('Tab 1')).toBeInTheDocument()
    expect(getByText('Tab 2')).toBeInTheDocument()

    expect(getByText('Content 1')).toBeInTheDocument()
  })

  it('should be able to change tabs and render correct content', async () => {
    const { getByText } = customRender(
      <Tabs>
        <Tabs.Item label="Tab 1">
          <div>Content 1</div>
        </Tabs.Item>
        <Tabs.Item label="Tab 2">
          <div>Content 2</div>
        </Tabs.Item>
      </Tabs>
    )

    expect(getByText('Tab 1')).toBeInTheDocument()
    expect(getByText('Tab 2')).toBeInTheDocument()

    expect(getByText('Content 1')).toBeInTheDocument()

    await userEvent.click(getByText('Tab 2'))

    expect(getByText('Content 2')).toBeInTheDocument()
  })
})
