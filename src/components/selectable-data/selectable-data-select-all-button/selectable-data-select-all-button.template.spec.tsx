import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SelectableDataSelectAllButton } from './selectable-data-select-all-button.template'

describe('Selectable Data Select All Button', () => {
  it('should be able to render a Select All Button correctly', () => {
    const { getByTestId } = customRender(<SelectableDataSelectAllButton data-testid="select-all-button" />)

    const selectAllButton = getByTestId('select-all-button')

    expect(selectAllButton).toBeInTheDocument()
  })
})
