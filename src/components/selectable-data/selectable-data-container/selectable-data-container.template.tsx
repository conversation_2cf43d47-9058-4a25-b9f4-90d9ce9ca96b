import styled, { css } from 'styled-components'
import { theme, ScrollBar } from '@mz-codes/design-system'
import { TContainer } from './selectable-data-container.types'

export const SelectableDataContainer = styled.div<TContainer>`
  background-color: ${theme.legacy.colors.neutral.contentBackground};
  width: 100%;
  margin: 0 0 20px;
  border-radius: 8px;
  flex: 1 1 auto;
  height: -webkit-fill-available;
  overflow: auto;
  ${({ $isLoading, $noData }) =>
    ($isLoading || $noData) &&
    css`
      display: flex;
      align-items: center;
      justify-content: center;
    `};
  ${ScrollBar};
`
