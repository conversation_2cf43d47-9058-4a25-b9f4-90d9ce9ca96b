import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SelectableDataContainer } from './selectable-data-container.template'

describe('Selectable Data Container', () => {
  it('should be able to render a Container correctly when isLoading is true', () => {
    const { getByTestId } = customRender(<SelectableDataContainer data-testid="container" $isLoading $noData={false} />)

    const container = getByTestId('container')

    expect(container).toHaveStyleRule('display', 'flex')
    expect(container).toHaveStyleRule('align-items', 'center')
    expect(container).toHaveStyleRule('justify-content', 'center')
  })

  it('should be able to render a Container correctly when noData is true', () => {
    const { getByTestId } = customRender(<SelectableDataContainer data-testid="container" $isLoading={false} $noData />)

    const container = getByTestId('container')

    expect(container).toHaveStyleRule('display', 'flex')
    expect(container).toHaveStyleRule('align-items', 'center')
    expect(container).toHaveStyleRule('justify-content', 'center')
  })

  it('should be able to render a Container correctly when noData and isLoading is false', () => {
    const { getByTestId } = customRender(
      <SelectableDataContainer data-testid="container" $isLoading={false} $noData={false} />
    )

    const container = getByTestId('container')

    expect(container).not.toHaveStyleRule('display', 'flex')
    expect(container).not.toHaveStyleRule('align-items', 'center')
    expect(container).not.toHaveStyleRule('justify-content', 'center')
  })
})
