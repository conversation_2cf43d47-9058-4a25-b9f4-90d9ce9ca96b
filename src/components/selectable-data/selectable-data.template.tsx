import { Loading, Inputs, theme } from '@mz-codes/design-system'

import { translations } from 'pages/smartGrouping/smart-grouping.translations'
import { NoDataLabel } from 'pages/smartGrouping/components/NoDataLabel'

import SelectAllIcon from 'assets/svg/selectAll-icon'
import { TSelectableData } from './types'
import { SelectableDataContainer } from './selectable-data-container'
import { SelectableDataHeader } from './selectable-data-header'
import { SelectableDataHeaderText } from './selectable-data-header-text'
import { SelectableDataCheckboxContainer } from './selectable-data-checkbox-container'
import { SelectableDataSelectAllButton } from './selectable-data-select-all-button'
import { SelectableDataListContainer } from './selectable-data-list-container'

export function SelectableData(props: TSelectableData) {
  const { loading, data, noDataLabel, handleRows, handleAllRows } = props

  return (
    <SelectableDataContainer
      data-testid="selectable-data-container"
      $isLoading={loading}
      $noData={!loading && !data.length}
    >
      {loading && <Loading data-testid="loading" />}

      {!loading && !data.length && <NoDataLabel>{noDataLabel}</NoDataLabel>}

      {!loading && !!data.length && (
        <>
          <SelectableDataHeader data-testid="header">
            <SelectableDataSelectAllButton type="button" onClick={handleAllRows}>
              <SelectAllIcon />
            </SelectableDataSelectAllButton>
            <SelectableDataHeaderText>{translations.fundOrShareholder}</SelectableDataHeaderText>
          </SelectableDataHeader>

          <SelectableDataListContainer data-testid="list-container">
            {data.map((item) => (
              <SelectableDataCheckboxContainer key={item.value}>
                <Inputs.Label key={item.value} $horizontalAlignment="center" style={{ padding: '16px 20px' }}>
                  <Inputs.Checkbox
                    checked={item.isSelected}
                    handleChange={handleRows}
                    key={item.value}
                    value={item.value}
                    name={item.label}
                  />
                  <Inputs.Text style={{ color: theme.legacy.colors.grayScale.texts }}>{item.label}</Inputs.Text>
                </Inputs.Label>
              </SelectableDataCheckboxContainer>
            ))}
          </SelectableDataListContainer>
        </>
      )}
    </SelectableDataContainer>
  )
}
