import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SelectableDataHeader } from './selectable-data-header.template'

describe('Selectable Data Header', () => {
  it('should be able to render a Header correctly', () => {
    const { getByTestId } = customRender(<SelectableDataHeader data-testid="header" />)

    const header = getByTestId('header')

    expect(header).toBeInTheDocument()
  })
})
