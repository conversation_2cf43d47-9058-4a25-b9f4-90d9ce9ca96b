import { SelectableDataCheckboxContainer } from './selectable-data-checkbox-container'
import { SelectableDataContainer } from './selectable-data-container'
import { SelectableDataHeader } from './selectable-data-header'
import { SelectableDataHeaderText } from './selectable-data-header-text'
import { SelectableDataListContainer } from './selectable-data-list-container'
import { SelectableDataSelectAllButton } from './selectable-data-select-all-button'

export { SelectableData } from './selectable-data.template'

export const SelectableDataComposite = {
  Container: SelectableDataContainer,
  Header: SelectableDataHeader,
  HeaderText: SelectableDataHeaderText,
  CheckboxContainer: SelectableDataCheckboxContainer,
  SelectAllButton: SelectableDataSelectAllButton,
  ListContainer: SelectableDataListContainer,
}
