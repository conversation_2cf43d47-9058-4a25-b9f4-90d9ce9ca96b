import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SelectableDataListContainer } from './selectable-data-list-container.template'

describe('Selectable Data List Container', () => {
  it('should be able to render a List Container correctly', () => {
    const { getByTestId } = customRender(<SelectableDataListContainer data-testid="list-container" />)

    const listContainer = getByTestId('list-container')

    expect(listContainer).toBeInTheDocument()
  })
})
