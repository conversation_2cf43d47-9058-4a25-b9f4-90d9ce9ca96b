import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SelectableDataHeaderText } from './selectable-data-header-text.template'

describe('Selectable Data Header', () => {
  it('should be able to render a Header correctly', () => {
    const { getByTestId } = customRender(<SelectableDataHeaderText data-testid="header-text" />)

    const headerText = getByTestId('header-text')

    expect(headerText).toBeInTheDocument()
  })
})
