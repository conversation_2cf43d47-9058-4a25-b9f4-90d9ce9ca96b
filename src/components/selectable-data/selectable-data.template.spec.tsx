import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import userEvent from '@testing-library/user-event'

import SelectAllIcon from 'assets/svg/selectAll-icon'

import { SelectableData } from './selectable-data.template'

function generateMockData() {
  const handleAllRows = vi.fn()
  const handleRows = vi.fn()

  const mockProps = {
    data: [
      {
        label: 'test 01',
        value: '01',
        isSelected: true,
      },
      {
        label: 'test 02',
        value: '02',
        isSelected: false,
      },
    ],
    handleAllRows,
    handleRows,
    hasData: true,
    icon: SelectAllIcon as unknown as JSX.Element,
    loading: false,
    noData: false,
    noDataLabel: 'No data found',
  }

  return { mockProps }
}

describe('Selectable Data Template', () => {
  it('should be able to render a Selectable Data Template correctly', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<SelectableData {...mockProps} />)

    const container = getByTestId('selectable-data-container')

    expect(container).toBeInTheDocument()
  })

  it('should be able to render a Loading correctly when loading is true', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<SelectableData {...mockProps} loading />)

    const loading = getByTestId('loading')

    expect(loading).toBeInTheDocument()
  })

  it('should be able to render a No Data Label correctly when data is empty', () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(<SelectableData {...mockProps} data={[]} />)

    const noDataText = getByText('No data found')

    expect(noDataText).toBeInTheDocument()
  })

  it('should be able to render the content correctly when it has data', () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByRole, getByText } = customRender(<SelectableData {...mockProps} />)

    const header = getByTestId('header')
    const button = getByRole('button')
    const text = getByText('Fund/Shareholder')
    const listContainer = getByTestId('list-container')
    const test01 = getByText('test 01')
    const test02 = getByText('test 02')

    expect(header).toBeInTheDocument()
    expect(button).toBeInTheDocument()
    expect(text).toBeInTheDocument()
    expect(listContainer).toBeInTheDocument()
    expect(test01).toBeInTheDocument()
    expect(test02).toBeInTheDocument()
  })

  it('should calls handleAllRows correctly when select all button is clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByRole } = customRender(<SelectableData {...mockProps} />)

    const button = getByRole('button')
    await userEvent.click(button)

    expect(mockProps.handleAllRows).toHaveBeenCalled()
  })

  it('should calls handleRow correctly when checkbox row is clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByRole } = customRender(<SelectableData {...mockProps} />)

    const row = getByRole('checkbox', {
      name: /test 01/i,
    })
    await userEvent.click(row)

    expect(mockProps.handleRows).toHaveBeenCalled()
  })
})
