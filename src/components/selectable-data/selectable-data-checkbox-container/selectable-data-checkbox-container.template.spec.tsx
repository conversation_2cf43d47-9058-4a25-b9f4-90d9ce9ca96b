import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SelectableDataCheckboxContainer } from './selectable-data-checkbox-container.template'

describe('Selectable Data Checkbox Container', () => {
  it('should be able to render a Checkbox Container correctly', () => {
    const { getByTestId } = customRender(<SelectableDataCheckboxContainer data-testid="checkbox-container" />)

    const checkboxContainer = getByTestId('checkbox-container')

    expect(checkboxContainer).toBeInTheDocument()
  })
})
