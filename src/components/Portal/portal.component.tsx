import React from 'react'
import ReactDOM from 'react-dom'
import { TPortal } from './portal.types'

export const testid = 'portal-test'

export const Portal: React.FC<TPortal> = ({ children, parent, className }) => {
  const el = React.useMemo(() => document.createElement('div'), [])
  React.useEffect(() => {
    const target = parent?.appendChild ? parent : document.body
    const classList = ['portal-container']
    if (className) className.split(' ').forEach((item) => classList.push(item))
    classList.forEach((item) => el.classList.add(item))
    el.setAttribute('data-testid', testid)
    target.appendChild(el)
    return () => {
      target.removeChild(el)
    }
  }, [el, parent, className])
  return ReactDOM.createPortal(children, el)
}
