import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import userEvent from '@testing-library/user-event'
import { MemoryRouter, Routes, Route } from 'react-router-dom'
import { LinkButton } from './link-button.component'

describe('Link Button Component', () => {
  it('should render a Link Button with correct text and href', () => {
    const initialText = 'Testing Link Button'
    const mockedPath = '/shareholders/history/exports'

    const { getByText } = customRender(
      <MemoryRouter>
        <LinkButton link={mockedPath}>{initialText}</LinkButton>
      </MemoryRouter>
    )

    const linkElement = getByText(initialText)
    expect(linkElement).toBeInTheDocument()
    expect(linkElement).toHaveAttribute('href', mockedPath)
  })

  it('should be able to click <PERSON> Button', async () => {
    const initialText = 'Testing Link Button'
    const mockedPath = '/shareholders/history/exports'
    const currentPath = '/'

    const { getByText } = customRender(
      <MemoryRouter initialEntries={[currentPath]}>
        <Routes>
          <Route path={mockedPath} element={<div>Target Page</div>} />
          <Route path="*" element={<LinkButton link={mockedPath}>{initialText}</LinkButton>} />
        </Routes>
      </MemoryRouter>
    )

    const linkElement = getByText(initialText)
    await userEvent.click(linkElement)
    expect(getByText('Target Page')).toBeInTheDocument()
  })
})
