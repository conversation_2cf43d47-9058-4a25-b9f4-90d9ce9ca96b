import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { theme } from '@mz-codes/design-system'
import { MemoryRouter } from 'react-router-dom'
import { LinkButtonContent } from './link-button-content.template'

describe('Link Button Content Template', () => {
  it('should be able to render Link Button Content', () => {
    const mockedPath = '#'
    const { getByTestId } = customRender(
      <MemoryRouter>
        <LinkButtonContent to={mockedPath} data-testid="link-button-content" />
      </MemoryRouter>
    )

    const iconTextButtonText = getByTestId('link-button-content')

    expect(iconTextButtonText).toBeInTheDocument()
    expect(iconTextButtonText).toHaveStyleRule('color', theme.legacy.colors.primary.primary)
    expect(iconTextButtonText).toHaveStyleRule('color', theme.legacy.colors.primary.highlight, {
      modifier: ':hover',
    })
    expect(iconTextButtonText).toHaveStyleRule('color', theme.legacy.colors.primary.primary, {
      modifier: ':focus',
    })
  })
})
