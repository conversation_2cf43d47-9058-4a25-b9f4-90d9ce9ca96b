import { describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import { theme } from '@mz-codes/design-system'
import userEvent from '@testing-library/user-event'
import { RoundedBorderButton } from './rounded-border-button.component'

describe('Rounded Border Button Component', () => {
  it('should be able to render enabled Rounded Border Button', () => {
    const handleClick = () => {}
    const initialText = 'Testing Rounded Border Button'

    const { getByRole } = customRender(
      <RoundedBorderButton onClick={handleClick} disabled={false}>
        {initialText}
      </RoundedBorderButton>
    )

    const roundedBorderButton = getByRole('button')

    expect(roundedBorderButton).toBeInTheDocument()
    expect(roundedBorderButton).toBeEnabled()
    expect(roundedBorderButton).toHaveStyleRule('border-radius', '4px')
    expect(roundedBorderButton).toHaveStyleRule('color', theme.legacy.colors.grayScale.texts)
  })

  it('should be able to render disabled Rounded Border Button', () => {
    const handleClick = () => {}
    const initialText = 'Testing Rounded Border Button'

    const { getByRole } = customRender(
      <RoundedBorderButton onClick={handleClick} disabled>
        {initialText}
      </RoundedBorderButton>
    )

    const roundedBorderButton = getByRole('button')

    expect(roundedBorderButton).toBeInTheDocument()
    expect(roundedBorderButton).toBeDisabled()
  })

  it('should be able to click Rounded Border Button', async () => {
    const handleClick = vi.fn()
    const initialText = 'Testing Rounded Border Button'

    const { getByRole } = customRender(<RoundedBorderButton onClick={handleClick}>{initialText}</RoundedBorderButton>)

    const roundedBorderButton = getByRole('button')

    expect(roundedBorderButton).toBeInTheDocument()

    await userEvent.click(roundedBorderButton)

    expect(handleClick).toHaveBeenCalledOnce()
  })
})
