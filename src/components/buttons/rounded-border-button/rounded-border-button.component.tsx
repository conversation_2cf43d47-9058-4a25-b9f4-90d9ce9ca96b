import { theme, Buttons } from '@mz-codes/design-system'
import { TRoundedBorderButton } from './rounded-border-button.types'

export function RoundedBorderButton(props: TRoundedBorderButton) {
  const { onClick, children, disabled = false, $active, ...rest } = props

  return (
    <Buttons.Base
      onClick={onClick}
      $bgColor="transparent"
      $hoverBgColor="#285F8F"
      $color={theme.legacy.colors.grayScale.texts}
      $padding="6px 13px"
      $height="auto"
      $borderColor={theme.legacy.colors.neutral.borderColor}
      $borderRadius="4px"
      disabled={disabled}
      $disabledOpacity="0.2"
      $active={$active}
      {...rest}
    >
      {children}
    </Buttons.Base>
  )
}
