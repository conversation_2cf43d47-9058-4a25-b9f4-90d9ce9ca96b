import { beforeEach, describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import userEvent from '@testing-library/user-event'
import { DownloadFileButton } from './download-file-button.component'

describe('Download File Button Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn((key: string) => key),
      },
    }))
  })

  it('should be able to render Download File Button', () => {
    const { getByText } = customRender(<DownloadFileButton shareholderReportId="testingReportId" />)

    const downloadFileButton = getByText('globals.downloadFile')
    expect(downloadFileButton).toBeInTheDocument()
  })

  it('should be able to click Download File Button', async () => {
    const mockResponse = vi.hoisted(() => {
      return Buffer.from('testing')
    })

    const mockGetExportedFile = vi.hoisted(() => {
      return vi.fn(() => Promise.resolve(mockResponse))
    })

    const mockDoDownload = vi.hoisted(() => {
      return vi.fn()
    })

    vi.mock('client', () => ({
      getExportedFile: mockGetExportedFile,
    }))

    vi.mock('utils', () => ({
      doDownload: mockDoDownload,
    }))

    const { getByText } = customRender(<DownloadFileButton shareholderReportId="testingReportId" />)

    const downloadFileButton = getByText('globals.downloadFile')
    expect(downloadFileButton).toBeInTheDocument()

    await userEvent.click(downloadFileButton)

    expect(mockGetExportedFile).toBeCalledWith('testingReportId')
    expect(mockGetExportedFile).toHaveBeenCalledOnce()

    expect(mockDoDownload).toBeCalledWith(mockResponse, '')
    expect(mockDoDownload).toHaveBeenCalledOnce()
  })
})
