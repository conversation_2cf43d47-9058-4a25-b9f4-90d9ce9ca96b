import { i18n } from 'translate'
import { getExportedFile } from 'client'
import { doDownload } from 'utils'
import { Buttons } from '@mz-codes/design-system'
import { TDownloadFileButtonProps } from './download-file-button.types'

export function DownloadFileButton(props: TDownloadFileButtonProps) {
  const { shareholderReportId } = props

  const handleClick = async () => {
    const response = await getExportedFile(shareholderReportId)
    doDownload(response, '')
  }

  return <Buttons.Primary onClick={handleClick}>{i18n.t('globals.downloadFile')}</Buttons.Primary>
}
