import { MemoryRouter } from 'react-router-dom'
import { describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import { GoToHistoryButton } from './go-to-history-button.component'

describe('Go To History Button Component', () => {
  it('should render a Link to history with correct text', () => {
    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn((key: string) => key),
      },
    }))

    const { getByText } = customRender(
      <MemoryRouter>
        <GoToHistoryButton />
      </MemoryRouter>
    )

    const linkElement = getByText('globals.goToHistory')
    expect(linkElement).toBeInTheDocument()

    const link = linkElement.closest('a')
    expect(link).toHaveAttribute('href', '/shareholders/history/exports')
  })
})
