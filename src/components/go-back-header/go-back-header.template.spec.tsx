import { describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import userEvent from '@testing-library/user-event'

import { TGoBackHeader } from './go-back-header.types'
import { GoBackHeader } from './go-back-header.template'

describe('Close Button Component', () => {
  function generateMockData() {
    const handleNavigate = vi.fn()

    const mockProps: TGoBackHeader = {
      handleNavigate,
      title: 'Title',
    }

    return { mockProps }
  }

  it('should render correctly', () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByText } = customRender(<GoBackHeader {...mockProps} />)

    const container = getByTestId('go-back-header-container')
    const title = getByText('Title')

    expect(container).toBeInTheDocument()
    expect(title).toBeInTheDocument()
  })

  it('should call handleNavigate if icon is pressed', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<GoBackHeader {...mockProps} />)

    const btn = getByTestId('go-back-header-icon')
    await userEvent.click(btn)

    expect(mockProps.handleNavigate).toHaveBeenCalledTimes(1)
  })
})
