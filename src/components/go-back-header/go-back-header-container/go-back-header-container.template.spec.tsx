import { describe, expect, it } from 'vitest'
import { customRender } from 'test'

import { GoBackHeaderContainer } from './go-back-header-container.template'

describe('Go Back Header Icon', () => {
  it('should render correctly', () => {
    const { getByTestId } = customRender(<GoBackHeaderContainer data-testid="container" />)

    const container = getByTestId('container')

    expect(container).toBeInTheDocument()
  })
})
