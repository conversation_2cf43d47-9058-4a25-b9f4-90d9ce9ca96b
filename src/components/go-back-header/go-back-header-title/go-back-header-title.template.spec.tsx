import { describe, expect, it } from 'vitest'
import { customRender } from 'test'

import { GoBackHeaderTitle } from './go-back-header-title.template'

describe('Go Back Header Title', () => {
  it('should render correctly', () => {
    const { getByTestId } = customRender(<GoBackHeaderTitle data-testid="title">Title test</GoBackHeaderTitle>)

    const title = getByTestId('title')

    expect(title).toBeInTheDocument()
  })
})
