import { TGoBackHeader } from './go-back-header.types'

import { GoBackHeaderContainer } from './go-back-header-container'
import { GoBackHeaderTitle } from './go-back-header-title'
import { GoBackHeaderIcon } from './go-back-header-icon'

export function GoBackHeader(props: TGoBackHeader) {
  const { title, handleNavigate } = props

  return (
    <GoBackHeader.Container data-testid="go-back-header-container">
      <GoBackHeader.Icon data-testid="go-back-header-icon" onClick={handleNavigate} />
      <GoBackHeader.Title>{title}</GoBackHeader.Title>
    </GoBackHeader.Container>
  )
}

GoBackHeader.Container = GoBackHeaderContainer
GoBackHeader.Title = GoBackHeaderTitle
GoBackHeader.Icon = GoBackHeaderIcon
