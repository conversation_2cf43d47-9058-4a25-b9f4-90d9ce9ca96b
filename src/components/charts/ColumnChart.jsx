import React, { Component } from 'react'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { theme } from '@mz-codes/design-system'
import { utils } from 'utils'

class ColumnChart extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    Highcharts.setOptions({
      lang: {
        decimalPoint: ',',
        thousandsSep: '.',
      },
    })

    const optionsGraph = {
      chart: {
        type: 'column',
        backgroundColor: theme.legacy.colors.neutral.contentBackground,
        borderRadius: utils.convertPxToNumber(theme.legacy.units.md),
      },
      title: {
        text: this.props.title,
        style: {
          color: '#ffffff',
          fontSize: '20px',
        },
      },
      xAxis: {
        categories: this.props.categories,
        crosshair: true,
      },
      yAxis: {
        min: 0,
        title: {
          text: this.props.yAxisText,
          style: {
            color: '#ffffff',
          },
        },
      },
      legend: {
        itemStyle: {
          color: '#ffffff',
          fontWeight: 'bold',
        },
      },
      credits: {
        enabled: false,
      },
      tooltip: {
        headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
        pointFormat: `<tr><td style="color:{series.color};padding:0">{series.name}: </td><td style="padding:0"><b>{point.y:,.0f} ${this.props.yAxisText}</b></td></tr>`,
        footerFormat: '</table>',
        shared: true,
        useHTML: true,
      },
      plotOptions: {
        column: {
          pointPadding: 0.2,
          borderWidth: 0,
        },
      },
      series: [
        {
          name: this.props.seriesName,
          data: this.props.data,
        },
      ],
    }

    return <HighchartsReact highcharts={Highcharts} options={optionsGraph} />
  }
}

export default ColumnChart
