import React, { Component } from 'react'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { theme } from '@mz-codes/design-system'
import { utils } from 'utils'

class <PERSON><PERSON><PERSON> extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    Highcharts.setOptions({
      lang: {
        decimalPoint: ',',
        thousandsSep: '.',
      },
    })

    const optionsGraph = {
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        type: 'pie',
        backgroundColor: theme.legacy.colors.neutral.contentBackground,
        borderRadius: utils.convertPxToNumber(theme.legacy.units.md),
      },
      title: {
        text: this.props.title,
        style: {
          color: '#ffffff',
        },
      },
      tooltip: {
        pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>',
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            format: '<b>{point.name}</b>: {point.percentage:.1f} %',
            style: {
              color: '#ffffff',
              fontWeight: 'bold',
            },
          },
          showInLegend: true,
        },
      },
      legend: {
        itemStyle: {
          color: '#ffffff',
          fontWeight: 'bold',
        },
      },
      credits: {
        enabled: false,
      },
      series: [
        {
          name: this.props.name,
          colorByPoint: true,
          data: this.props.data,
        },
      ],
    }

    return <HighchartsReact highcharts={Highcharts} options={optionsGraph} />
  }
}

export default PieChart
