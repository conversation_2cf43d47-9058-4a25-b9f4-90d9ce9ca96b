import React, { Component } from 'react'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { theme } from '@mz-codes/design-system'
import { utils } from 'utils'

class <PERSON><PERSON>hart extends Component {
  constructor(props) {
    super(props)
    this.state = {
      options: {},
      loading: true,
    }
  }

  componentDidMount() {
    this.renderGraph()
  }

  componentDidUpdate(prevProps) {
    if (prevProps.series !== this.props.series) {
      this.setState({ options: {}, loading: true }, () => this.renderGraph())
    }
  }

  renderGraph = () => {
    const { modalType, openModal } = this.props
    let { decimalPlaces } = this.props
    const color = this.props.barColor != null ? this.props.barColor : '#339fff'
    decimalPlaces = decimalPlaces ? parseInt(decimalPlaces, 10) : 2
    const formaterFunction =
      this.props.idiom === 0
        ? function () {
            return `<small>${this.x}</small>
                <table>
                    <tr>
                        <td style="color: ${this.series.color}">${this.series.name}: </td>
                        <td style="text-align: right">
                            <b>${Highcharts.numberFormat(this.y, decimalPlaces, '.', ',')} ${
                              this.series.tooltipOptions.valueSuffix
                            }</b>
                        </td>
                    </tr>
                </table>`
          }
        : function () {
            return `<small>${this.x}</small>
                        <table>
                            <tr>
                                <td style="color: ${this.series.color}">${this.series.name}: </td>
                                <td style="text-align: right">
                                    <b>${Highcharts.numberFormat(this.y, decimalPlaces, ',', '.')} ${
                                      this.series.tooltipOptions.valueSuffix
                                    }</b>
                                </td>
                            </tr>
                        </table>`
          }

    // { valueSuffix: this.props.valueSufix };
    const optionsGraph = {
      chart: {
        type: 'bar',
        backgroundColor: theme.legacy.colors.neutral.contentBackground,
        borderRadius: utils.convertPxToNumber(theme.legacy.units.md),
        height: this.props.height,
      },
      title: {
        text: this.props.title,
        style: {
          color: '#339fff',
          fontSize: '20px',
        },
      },
      xAxis: {
        categories: this.props.categories,
        title: {
          text: null,
        },
      },
      yAxis: [
        {
          title: {
            text: this.props.textTitle,
          },
          labels: {
            format: this.props.valueSufix ? `{value} ${this.props.valueSufix}` : null,
          },
        },
        {
          opposite: this.props.opposite,
          max: 100,
          title: {
            text: this.props.oppositeTitle,
          },
          labels: {
            format: '{value} %',
            style: {
              color: Highcharts.getOptions().colors[2],
            },
          },
        },
      ],
      tooltip: {
        valueSuffix: this.props.valueSufix,
        useHTML: true,
        formatter: formaterFunction,
      },
      // tooltip: {
      //   valueSuffix: this.props.valueSufix,
      // },
      plotOptions: {
        bar: {
          dataLabels: {
            enabled: this.props.enableDataLabels,
          },
          className: openModal != null ? 'pointer' : '',
        },
        column: {
          pointPadding: 0.2,
          borderWidth: 0,
        },
        series: {
          stacking: 'normal',
          negativeColor: 'red',
          // data: {
          //   colors: ['green']
          // }
          events: {
            click(e) {
              if (openModal != null) {
                openModal(modalType, e.point.category)
              }
            },
          },
        },
      },
      legend: {
        enabled: this.props.enableLegend,
        itemStyle: {
          color: '#ffffff',
          fontWeight: 'bold',
        },
      },
      credits: {
        enabled: false,
      },
      colors: [color],
      series: this.props.series,
    }

    this.setState({ options: optionsGraph, loading: false })
  }

  render() {
    const { loading, options } = this.state
    let highChart = <div />
    if (!loading) {
      highChart = <HighchartsReact highcharts={Highcharts} options={options} />
    }

    return highChart
  }
}

BarChart.defaultProps = {
  enableLegend: true,
  opposite: false,
  valueSufix: '',
  enableDataLabels: true,
  formatToolTip: false,
  toolTipDividerValue: 1,
  idiom: 0,
}

export default BarChart
