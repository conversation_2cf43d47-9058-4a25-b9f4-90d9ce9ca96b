import { useExternalRedirect } from 'hooks/use-external-redirect'
import { TExternalLink } from './external-link.types'
import { ExternalLinkContent } from './external-link-content'

/**
 * ExternalLink Component
 *
 * This component is used to create links to external products.
 * It generates absolute URLs based on the environment and product type.
 */
export function ExternalLink({
  product,
  pagePath = '/',
  children,
  className = '',
  style,
  target = '_blank',
  ...rest
}: TExternalLink) {
  const { getProductUrl } = useExternalRedirect()

  return (
    <ExternalLinkContent
      href={getProductUrl(product, pagePath)}
      target={target}
      rel="noopener noreferrer"
      className={className}
      style={style}
      {...rest}
    >
      {children}
    </ExternalLinkContent>
  )
}
