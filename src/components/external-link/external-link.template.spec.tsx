import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import * as ExternalRedirectHook from 'hooks/use-external-redirect'
import { ExternalLink } from './external-link.template'
import { ExternalLinkContent } from './external-link-content'

// Mock for useExternalRedirect hook
vi.mock('hooks/use-external-redirect', () => {
  return {
    useExternalRedirect: vi.fn(),
    EXTERNAL_PRODUCTS: {
      INTELLIGENCE: { path: '/intelligence', isSubdomain: false },
      IRM: { path: '/irm', isSubdomain: false },
      DASHBOARD: { path: '/dashboard', isSubdomain: false },
      RESPONSIBLE: { path: '/responsible', isSubdomain: false },
      ENGAGEMENT: { path: '/engagement', isSubdomain: true, subdomain: 'engagement' },
    },
  }
})

// Mock for ExternalLinkContent component
vi.mock('./external-link-content', () => ({
  ExternalLinkContent: vi.fn(({ children, ...props }) => (
    <a data-testid="styled-link" {...props}>
      {children}
    </a>
  )),
}))

describe('ExternalLink', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render correctly with default props', () => {
    const mockGetProductUrl = vi.fn().mockImplementation((product) => `https://mocked-url.com/${product}`)

    // Setup hook mock configuration
    vi.mocked(ExternalRedirectHook.useExternalRedirect).mockReturnValue({
      getProductUrl: mockGetProductUrl,
      navigateTo: vi.fn(),
      isStaging: false,
      portalBaseUrl: 'https://mocked-portal.com',
    })

    render(<ExternalLink product="IRM">Test Link</ExternalLink>)

    // Verify component rendering
    const link = screen.getByTestId('styled-link')
    expect(link).toBeInTheDocument()
    expect(link).toHaveTextContent('Test Link')

    // Verify ExternalLinkContent was called with correct props
    expect(ExternalLinkContent).toHaveBeenCalledWith(
      expect.objectContaining({
        href: 'https://mocked-url.com/IRM',
        target: '_blank',
        rel: 'noopener noreferrer',
        className: '',
        children: 'Test Link',
      }),
      expect.anything()
    )

    // Verify getProductUrl was called correctly
    expect(mockGetProductUrl).toHaveBeenCalledWith('IRM', '/')
  })

  it('should handle pagePath correctly', () => {
    const mockGetProductUrl = vi
      .fn()
      .mockImplementation((product, pagePath) => `https://mocked-url.com/${product}${pagePath}`)

    // Setup hook mock configuration
    vi.mocked(ExternalRedirectHook.useExternalRedirect).mockReturnValue({
      getProductUrl: mockGetProductUrl,
      navigateTo: vi.fn(),
      isStaging: false,
      portalBaseUrl: 'https://mocked-portal.com',
    })

    render(
      <ExternalLink product="ENGAGEMENT" pagePath="/dashboard">
        Test Link
      </ExternalLink>
    )

    // Verify getProductUrl was called correctly
    expect(mockGetProductUrl).toHaveBeenCalledWith('ENGAGEMENT', '/dashboard')

    // Verify ExternalLinkContent was called with correct URL
    expect(ExternalLinkContent).toHaveBeenCalledWith(
      expect.objectContaining({
        href: 'https://mocked-url.com/ENGAGEMENT/dashboard',
      }),
      expect.anything()
    )
  })

  it('should apply custom props and pass them to ExternalLinkContent', () => {
    const mockGetProductUrl = vi
      .fn()
      .mockImplementation((product, pagePath) => `https://mocked-url.com/${product}${pagePath}`)

    // Setup hook mock configuration
    vi.mocked(ExternalRedirectHook.useExternalRedirect).mockReturnValue({
      getProductUrl: mockGetProductUrl,
      navigateTo: vi.fn(),
      isStaging: false,
      portalBaseUrl: 'https://mocked-portal.com',
    })

    const customProps = {
      product: 'INTELLIGENCE' as const,
      pagePath: '/reports',
      className: 'custom-class',
      style: { color: 'red' },
      target: '_self',
      'data-testid': 'custom-test-id',
      'aria-label': 'External link',
    }

    render(<ExternalLink {...customProps}>Custom Link</ExternalLink>)

    // Verify ExternalLinkContent was called with all custom props
    expect(ExternalLinkContent).toHaveBeenCalledWith(
      expect.objectContaining({
        href: 'https://mocked-url.com/INTELLIGENCE/reports',
        className: 'custom-class',
        style: { color: 'red' },
        target: '_self',
        'data-testid': 'custom-test-id',
        'aria-label': 'External link',
      }),
      expect.anything()
    )
  })
})
