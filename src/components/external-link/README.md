# ExternalLink

Um componente React para navegação entre produtos MZ com detecção de ambiente.

## Visão Geral

O componente `ExternalLink` fornece uma maneira simples de criar links para outros produtos MZ, tratando automaticamente a geração de URL com base no ambiente atual (desenvolvimento, staging, produção).

## Funcionalidades

- **Detecção de ambiente**: Gera automaticamente a URL correta para o ambiente atual
- **Segurança de tipos**: Completamente tipado com TypeScript
- **API simples**: Basta especificar o produto e o caminho
- **Navegação padronizada**: Garante geração de URLs consistente em toda a aplicação

## Instalação

```bash
# Se você estiver usando diretamente no seu projeto
# Basta copiar os arquivos para seu projeto
```

## Uso

### Uso Básico

```tsx
import { ExternalLink } from 'components'
import { EXTERNAL_PRODUCT } from 'consts/external-products'

function MeuComponente() {
  return (
    <ExternalLink product={EXTERNAL_PRODUCT.IRM} pagePath="/dashboard">
      Ir para o Dashboard do IRM
    </ExternalLink>
  )
}
```

### Com Atributos Adicionais

```tsx
import { ExternalLink } from 'components'
import { EXTERNAL_PRODUCT } from 'consts/external-products'

function MeuComponente() {
  return (
    <ExternalLink
      product={EXTERNAL_PRODUCT.INTELLIGENCE}
      pagePath="/reports"
      className="external-link"
      target="_blank"
      rel="noopener noreferrer"
      data-testid="intelligence-link"
    >
      <span>Ver Relatórios do Intelligence</span>
      <Icon name="external-link" />
    </ExternalLink>
  )
}
```

## Produtos Disponíveis

O componente suporta os seguintes produtos:

| Chave do Produto | Descrição                               |
| ---------------- | --------------------------------------- |
| `INTELLIGENCE`   | Análise de dados para tomada de decisão |
| `IRM`            | Gestão de Relações com Investidores     |
| `DASHBOARD`      | Visão geral do portal MZ                |
| `SETTINGS`       | Configurações do portal MZ              |
| `ENGAGEMENT`     | Engajamento com stakeholders            |

> **Escalabilidade**: A solução foi projetada para ser facilmente escalável. Para adicionar suporte a novos produtos, basta incluir o produto no arquivo de constantes `external-products.ts`, sem necessidade de modificar o componente em si.

## Props

| Prop       | Tipo                                      | Obrigatório | Descrição                                           |
| ---------- | ----------------------------------------- | ----------- | --------------------------------------------------- |
| `product`  | `TExternalProductKey`                     | Sim         | O produto para o qual linkar                        |
| `pagePath` | `string`                                  | Não         | O caminho da página dentro do produto (padrão: '/') |
| `children` | `ReactNode`                               | Sim         | Conteúdo a ser renderizado dentro do link           |
| `...props` | `AnchorHTMLAttributes<HTMLAnchorElement>` | Não         | Quaisquer outras props do elemento âncora           |

## Tratamento de Ambiente

O componente detecta automaticamente o ambiente atual e gera a URL apropriada:

- **Desenvolvimento/Staging**: Links para ambientes de staging
- **Produção**: Links para ambientes de produção

As URLs são geradas de acordo com o ambiente:

- Para produtos sem subdomínios:
  - Produção: `https://mziq.com/{caminho-do-produto}/{caminho-da-página}`
  - Staging: `https://portal-stg.mziq.com/{caminho-do-produto}/{caminho-da-página}`

- Para produtos com subdomínios:
  - Produção: `https://{subdominio}.mziq.com/{caminho-da-página}`
  - Staging: `https://portal-stg-{subdominio}.mziq.com/{caminho-da-página}`

## Detalhes de Implementação

O componente `ExternalLink` é construído sobre o hook `useExternalRedirect`, que lida com toda a lógica de detecção de ambiente e geração de URL. O componente é essencialmente um wrapper fino em torno desse hook que renderiza um elemento âncora com a URL gerada.

## Testes

Ao testar componentes que usam `ExternalLink`, você precisará mockar o hook `useExternalRedirect`:

```tsx
import { vi } from 'vitest'
import * as ExternalRedirectHook from 'hooks/use-external-redirect'

// Mock do hook
vi.mock('hooks/use-external-redirect', () => ({
  useExternalRedirect: vi.fn(),
  EXTERNAL_PRODUCT: {
    INTELLIGENCE: 'INTELLIGENCE',
    IRM: 'IRM',
    DASHBOARD: 'DASHBOARD',
    SETTINGS: 'SETTINGS',
    ENGAGEMENT: 'ENGAGEMENT',
  },
}))

// No seu teste
vi.mocked(ExternalRedirectHook.useExternalRedirect).mockReturnValue({
  getProductUrl: vi.fn().mockReturnValue('https://mocked-url.com/produto/pagina'),
  navigateTo: vi.fn(),
  isStaging: false,
  portalBaseUrl: 'https://mocked-url.com',
})
```

## Veja Também

- [useExternalRedirect](../../hooks/use-external-redirect/README.md) - O hook que alimenta este componente

## Licença

MIT
