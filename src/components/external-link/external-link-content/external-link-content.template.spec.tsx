import { describe, it, expect } from 'vitest'
import { customRender } from 'test'
import { ExternalLinkContent } from './external-link-content.template'

describe('ExternalLinkContent', () => {
  it('should render correctly with default props', () => {
    const { getByText } = customRender(<ExternalLinkContent href="https://example.com">Link Text</ExternalLinkContent>)

    const link = getByText('Link Text')
    expect(link).toBeInTheDocument()
    expect(link.tagName).toBe('A')
    expect(link).toHaveAttribute('href', 'https://example.com')
  })

  it('should pass additional attributes to the anchor element', () => {
    const { getByText } = customRender(
      <ExternalLinkContent
        href="https://example.com"
        target="_blank"
        rel="noopener noreferrer"
        data-testid="test-link"
        aria-label="External link"
      >
        Link Text
      </ExternalLinkContent>
    )

    const link = getByText('Link Text')
    expect(link).toHaveAttribute('target', '_blank')
    expect(link).toHaveAttribute('rel', 'noopener noreferrer')
    expect(link).toHaveAttribute('data-testid', 'test-link')
    expect(link).toHaveAttribute('aria-label', 'External link')
  })
})
