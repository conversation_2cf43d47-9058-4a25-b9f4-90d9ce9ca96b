import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuItemSubList } from './side-menu-item-sub-list.template'

describe('Side Menu Item Sub List Wrapper', () => {
  it('should be able to render sub list wrapper correctly is is active', () => {
    const { getByTestId } = customRender(<SideMenuItemSubList $active data-testid="sub-list-wrapper" />)

    const wrapper = getByTestId('sub-list-wrapper')

    expect(wrapper).toBeInTheDocument()
    expect(wrapper).toHaveStyleRule('max-height', '100vh')
    expect(wrapper).toHaveStyleRule('padding', '8px 0 18px 18px')
  })

  it('should be able to render sub list wrapper correctly', () => {
    const { getByTestId } = customRender(<SideMenuItemSubList $active={false} data-testid="sub-list-wrapper" />)

    const wrapper = getByTestId('sub-list-wrapper')

    expect(wrapper).toBeInTheDocument()
    expect(wrapper).toHaveStyleRule('max-height', '0')
  })
})
