import { PATH } from 'consts'
import { useState } from 'react'
import { i18n } from 'translate'
import { useLocation } from 'react-router-dom'
import { SideMenuTemplate } from './side-menu.template'
import { TSideMenu, TSideMenuItem, TSubRoutes } from './side-menu.types'

const sideMenuList: Array<TSideMenuItem> = [
  { name: i18n.t('sideMenu.summary'), path: `${PATH}/summary` },
  { name: i18n.t('sideMenu.keyStatistics'), path: `${PATH}/keystatistics` },
  { name: i18n.t('sideMenu.ownership'), path: `${PATH}/ownership` },
  { name: i18n.t('sideMenu.reports'), path: `${PATH}/reports` },
  { name: i18n.t('sideMenu.shareholders'), path: `${PATH}/shareholders` },
  { name: i18n.t('sideMenu.charts'), path: `${PATH}/charts` },
  {
    name: i18n.t('sideMenu.monitoring'),
    path: '',
    subRoutes: [
      {
        name: i18n.t('sideMenu.dailyPosition'),
        path: `${PATH}/monitoring/dailyPosition`,
      },
      {
        name: i18n.t('sideMenu.interestGroup'),
        path: `${PATH}/monitoring/interestGroup`,
      },
      {
        name: i18n.t('sideMenu.ninetyDayPositions'),
        path: `${PATH}/monitoring/ninetyDayPositions`,
      },
    ],
  },
  {
    name: i18n.t('sideMenu.history'),
    path: '',
    subRoutes: [
      { name: i18n.t('sideMenu.ticker'), path: `${PATH}/history/ticker` },
      { name: i18n.t('sideMenu.exports'), path: `${PATH}/history/exports` },
      { name: i18n.t('sideMenu.uploads'), path: `${PATH}/history/uploads` },
    ],
  },
]

export function SideMenu(props: TSideMenu) {
  const { title } = props
  const [isOpen, setOpen] = useState(true)
  const [expandedPaths, setExpandedPaths] = useState<TSubRoutes[]>([])
  const location = useLocation()
  const currentPath = location.pathname

  const toggleMenu = () => {
    setOpen(!isOpen)
  }

  const handleToggleSubList = (subRoutes: TSubRoutes[]) => {
    if (subRoutes === expandedPaths) {
      setExpandedPaths([])
    } else {
      setExpandedPaths(subRoutes)
    }
  }

  return (
    <SideMenuTemplate
      isOpen={isOpen}
      sideMenuList={sideMenuList}
      title={title}
      toggleMenu={toggleMenu}
      handleToggleSubList={handleToggleSubList}
      expandedPaths={expandedPaths}
      currentPath={currentPath}
    />
  )
}
