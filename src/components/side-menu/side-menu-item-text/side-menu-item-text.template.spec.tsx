import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { theme } from '@mz-codes/design-system'
import { SideMenuItemText } from './side-menu-item-text.template'

describe('Side Menu Item Link', () => {
  it('should be able to render item link correctly', () => {
    const { getByTestId } = customRender(<SideMenuItemText $active data-testid="item-title" />)

    const title = getByTestId('item-title')

    expect(title).toBeInTheDocument()
  })

  it('should be able to render item link correctly if on hover', () => {
    const { getByTestId } = customRender(<SideMenuItemText $active data-testid="item-title" />)

    const title = getByTestId('item-title')
    expect(title).toHaveStyleRule('color', theme.legacy.colors.primary.primary, { modifier: ':hover' })
  })
})
