import { theme } from '@mz-codes/design-system'
import styled, { css } from 'styled-components'

export const activeListItem = css`
  color: ${theme.legacy.colors.primary.primary};
  &::after,
  &::before {
    opacity: 1;
    left: -15px;
  }
`

export const SideMenuItemText = styled.h2<{ $active?: boolean }>`
  color: ${theme.legacy.colors.grayScale.texts};
  padding: 9.5px 0 9.5px 18px;
  font-size: 13px;
  font-weight: 700;
  display: flex;
  width: 100%;
  align-items: center;
  position: relative;
  justify-content: flex-start;
  cursor: pointer;
  color: ${(props) => props.theme.colors.neutral.white};

  &:hover {
    color: ${theme.legacy.colors.primary.primary};
  }

  &::before,
  &::after {
    content: '';
    position: absolute;
    display: block;
    width: 100%;
    height: 1px;
    left: -155px;
    opacity: 0;
    background-image: linear-gradient(90deg, ${theme.legacy.colors.primary.primary}, transparent 75%);
    transition: opacity 0.1s ease-in-out;
    transition: left 0.3s ease-in-out;
  }
  &::before {
    top: 0;
  }
  &::after {
    bottom: 0;
  }

  ${({ $active }) => $active && activeListItem}
`
