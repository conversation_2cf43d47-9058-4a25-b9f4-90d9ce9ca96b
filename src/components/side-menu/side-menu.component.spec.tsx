import { describe, it, expect } from 'vitest'
import userEvent from '@testing-library/user-event'
import { BrowserRouter as Router } from 'react-router-dom'
import { customRender } from 'test'
import { theme } from '@mz-codes/design-system'
import { SideMenu } from './side-menu.component'

describe('SideMenu', () => {
  it('should render with the title correctly', async () => {
    const { getByText } = customRender(
      <Router>
        <SideMenu title="Test menu" />
      </Router>
    )

    const title = getByText('Test menu')

    expect(title).toBeInTheDocument()
  })

  it('should change the width from 195px to 60px when collapsed button is clicked', async () => {
    const { getByTestId } = customRender(
      <Router>
        <SideMenu title="Test menu" />
      </Router>
    )

    const button = getByTestId('menu-button')
    await userEvent.click(button)

    const sideMenuContainer = getByTestId('side-menu-container')

    expect(sideMenuContainer).toHaveStyleRule('width', '60px')
  })

  it('should add the active class when path is clicked', async () => {
    const { getByText } = customRender(
      <Router>
        <SideMenu title="Test menu" />
      </Router>
    )

    const ownershipLink = getByText('Ownership')
    await userEvent.click(ownershipLink)

    expect(ownershipLink).toHaveStyleRule('color', theme.legacy.colors.primary.primary)
  })

  it('should hide the subroutes when toggle is clicked and subroutes is open', async () => {
    const { getByText, getAllByTestId } = customRender(
      <Router>
        <SideMenu title="Test menu" />
      </Router>
    )

    const ownershipLink = getByText('Monitoring')
    await userEvent.click(ownershipLink)

    await userEvent.click(ownershipLink)

    const subList = getAllByTestId('sub-list')[0]

    expect(subList).toHaveStyleRule('max-height', '0')
  })

  it('should hide the subroutes when toggle is clicked and subroutes is open', async () => {
    const { getByText, getAllByTestId } = customRender(
      <Router>
        <SideMenu title="Test menu" />
      </Router>
    )

    const ownershipLink = getByText('Monitoring')
    await userEvent.click(ownershipLink)

    await userEvent.click(ownershipLink)

    const subList = getAllByTestId('sub-list')[0]

    expect(subList).toHaveStyleRule('max-height', '0')
  })

  it('should show the subroutes when toggle is clicked and subroutes is close', async () => {
    const { getByText, getAllByTestId } = customRender(
      <Router>
        <SideMenu title="Test menu" />
      </Router>
    )

    const ownershipLink = getByText('Monitoring')
    await userEvent.click(ownershipLink)

    const subList = getAllByTestId('sub-list')[0]

    expect(subList).toHaveStyleRule('max-height', '100vh')
  })
})
