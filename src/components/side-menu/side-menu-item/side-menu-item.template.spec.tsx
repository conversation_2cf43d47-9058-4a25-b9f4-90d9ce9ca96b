import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuItem } from './side-menu-item.template'

describe('Side Menu Item', () => {
  it('should be able to render a item wrapper correctly', () => {
    const { getByTestId } = customRender(<SideMenuItem data-testid="item-wrapper" />)

    const wrapper = getByTestId('item-wrapper')

    expect(wrapper).toBeInTheDocument()
  })
})
