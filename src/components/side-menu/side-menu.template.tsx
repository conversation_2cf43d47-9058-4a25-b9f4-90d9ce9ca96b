import { IMAGES } from 'assets'

import { TSideMenuTemplate, TSubRoutes } from './side-menu.types'
import { SideMenuContainer } from './side-menu-container'
import { SideMenuButton } from './side-menu-button'
import { SideMenuListContent } from './side-menu-list-content'
import { SideMenuProductTitle } from './side-menu-product-title'
import { SideMenuItem } from './side-menu-item'
import { SideMenuItemLink } from './side-menu-item-link'
import { SideMenuItemPlaceholder } from './side-menu-item-placeholder'
import { SideMenuItemSubList } from './side-menu-item-sub-list'
import { SideMenuItemSubListItem } from './side-menu-item-sub-list-item'
import { SideMenuItemSubListItemLink } from './side-menu-item-sub-list-item-link'
import { SideMenuItemIcon } from './side-menu-item-icon'
import { SideMenuItemText } from './side-menu-item-text'

export function SideMenuTemplate(props: TSideMenuTemplate) {
  const { title, isOpen, toggleMenu, sideMenuList, expandedPaths, handleToggleSubList, currentPath } = props

  return (
    <SideMenuTemplate.Container data-testid="side-menu-container" open={isOpen}>
      <SideMenuTemplate.Button data-testid="menu-button" open={isOpen} onClick={toggleMenu}>
        <img src={IMAGES.MENU_ICON} alt="menu_icon" />
      </SideMenuTemplate.Button>
      <SideMenuTemplate.ProductTitle>{title}</SideMenuTemplate.ProductTitle>
      <SideMenuTemplate.ListContent>
        {sideMenuList &&
          sideMenuList.map((item) => (
            <SideMenuTemplate.Item key={`${item.name}-${item.path}`}>
              {item.path && (
                <SideMenuTemplate.ItemLink to={item.path}>
                  <SideMenuTemplate.ItemText $active={item.path === currentPath}>{item.name}</SideMenuTemplate.ItemText>
                </SideMenuTemplate.ItemLink>
              )}

              {!item.path && (
                <SideMenuTemplate.ItemPlaceholder onClick={() => item.subRoutes && handleToggleSubList(item.subRoutes)}>
                  <SideMenuTemplate.ItemText
                    $active={item?.subRoutes?.some((subRoute) => subRoute.path === currentPath)}
                  >
                    {item.name}
                    {item.subRoutes && (
                      <SideMenuTemplate.ItemIcon data-testid="item-icon" $expanded={expandedPaths === item.subRoutes} />
                    )}
                  </SideMenuTemplate.ItemText>
                </SideMenuTemplate.ItemPlaceholder>
              )}
              {item.subRoutes && (
                <SideMenuTemplate.ItemSubList data-testid="sub-list" $active={item.subRoutes === expandedPaths}>
                  {item.subRoutes.map((subRoute: TSubRoutes) => (
                    <SideMenuTemplate.ItemSubListItem key={subRoute.path}>
                      <SideMenuTemplate.ItemSubListItemLink to={subRoute.path}>
                        {subRoute.name}
                      </SideMenuTemplate.ItemSubListItemLink>
                    </SideMenuTemplate.ItemSubListItem>
                  ))}
                </SideMenuTemplate.ItemSubList>
              )}
            </SideMenuTemplate.Item>
          ))}
      </SideMenuTemplate.ListContent>
    </SideMenuTemplate.Container>
  )
}

SideMenuTemplate.Container = SideMenuContainer
SideMenuTemplate.Button = SideMenuButton
SideMenuTemplate.Item = SideMenuItem
SideMenuTemplate.ItemLink = SideMenuItemLink
SideMenuTemplate.ItemText = SideMenuItemText
SideMenuTemplate.ItemPlaceholder = SideMenuItemPlaceholder
SideMenuTemplate.ItemIcon = SideMenuItemIcon
SideMenuTemplate.ItemSubList = SideMenuItemSubList
SideMenuTemplate.ItemSubListItem = SideMenuItemSubListItem
SideMenuTemplate.ItemSubListItemLink = SideMenuItemSubListItemLink
SideMenuTemplate.ListContent = SideMenuListContent
SideMenuTemplate.ProductTitle = SideMenuProductTitle
