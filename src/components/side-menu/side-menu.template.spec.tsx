import { describe, it, expect, vi } from 'vitest'
import userEvent from '@testing-library/user-event'
import { BrowserRouter as Router } from 'react-router-dom'
import { customRender } from 'test'
import { waitFor } from '@testing-library/react'
import { SideMenuTemplate } from './side-menu.template'

function generateMockData() {
  const toggleMenu = vi.fn()
  const handleToggleSubList = vi.fn()
  const sideMenuList = [
    { name: 'Home', path: '/home' },
    {
      name: 'About',
      path: '',
      subRoutes: [
        { name: 'Team', path: '/about/team' },
        { name: 'Company', path: '/about/company' },
      ],
    },
    { name: 'Services', path: '/services' },
  ]
  const mockProps = {
    title: 'Test Menu',
    isOpen: true,
    toggleMenu,
    sideMenuList,
    expandedPaths: [],
    handleToggleSubList,
    currentPath: '/',
  }

  return { mockProps }
}

describe('SideMenuTemplate', () => {
  it('renders correctly', () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(
      <Router>
        <SideMenuTemplate {...mockProps} />
      </Router>
    )

    const menuText = getByText('Test Menu')

    expect(menuText).toBeInTheDocument()
  })

  it('shows menu links when open', () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(
      <Router>
        <SideMenuTemplate {...mockProps} />
      </Router>
    )

    mockProps.sideMenuList.forEach((item) => {
      const itemName = getByText(item.name)
      expect(itemName).toBeInTheDocument()
    })
  })

  it('shows icon next to menus with subRoutes', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(
      <Router>
        <SideMenuTemplate {...mockProps} />
      </Router>
    )

    const itemIcon = getByTestId('item-icon')

    mockProps.sideMenuList.forEach((item) => {
      if (item.subRoutes) {
        expect(itemIcon).toBeInTheDocument()
      }
    })
  })

  it('toggles menu on button click', async () => {
    const { mockProps } = generateMockData()

    const { getByAltText } = customRender(
      <Router>
        <SideMenuTemplate {...mockProps} />
      </Router>
    )

    const button = getByAltText('menu_icon')
    await userEvent.click(button)

    expect(mockProps.toggleMenu).toHaveBeenCalled()
  })

  it('expands subRoutes on placeholder click', () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(
      <Router>
        <SideMenuTemplate {...mockProps} />
      </Router>
    )

    const aboutMenu = getByText('About')
    userEvent.click(aboutMenu)

    waitFor(() => {
      expect(mockProps.handleToggleSubList).toHaveBeenCalledWith('/about', mockProps.sideMenuList[1].subRoutes)
    })
  })

  it('expands subRoutes on placeholder click', async () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(
      <Router>
        <SideMenuTemplate {...mockProps} />
      </Router>
    )

    const aboutMenu = getByText('About')
    userEvent.click(aboutMenu)

    await waitFor(() => {
      expect(mockProps.handleToggleSubList).toHaveBeenCalled()
    })
  })
})
