import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuItemIcon } from './side-menu-item-icon.template'

describe('Side Menu Item Icon', () => {
  it('should be able to render a item icon correctly when expanded', () => {
    const { getByTestId } = customRender(<SideMenuItemIcon data-testid="item-icon" $expanded />)

    const itemIcon = getByTestId('item-icon')

    expect(itemIcon).toBeInTheDocument()
    expect(itemIcon).toHaveStyleRule('transform', 'rotate(180deg)')
  })

  it('should be able to render a item icon correctly when not expanded', () => {
    const { getByTestId } = customRender(<SideMenuItemIcon data-testid="item-icon" $expanded={false} />)

    const itemIcon = getByTestId('item-icon')

    expect(itemIcon).toBeInTheDocument()
    expect(itemIcon).toHaveStyleRule('transform', 'rotate(0)')
  })
})
