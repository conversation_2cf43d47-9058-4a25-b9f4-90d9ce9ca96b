import { Icons, theme } from '@mz-codes/design-system'
import styled from 'styled-components'

import { TItemIcon } from './side-menu-item-icon.types'

export const SideMenuItemIcon = styled(Icons.AngleDown)<TItemIcon>`
  transform-origin: center center;
  transition: 0.5s;
  margin-left: 8px;
  font-size: 16px;
  transform: ${({ $expanded }) => ($expanded ? 'rotate(180deg)' : 'rotate(0)')};
  fill: ${({ $expanded }) => ($expanded ? theme.legacy.colors.primary.primary : 'white')};
`
