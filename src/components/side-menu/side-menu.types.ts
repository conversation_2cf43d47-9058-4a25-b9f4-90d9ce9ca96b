export type TSubRoutes = {
  name: string
  path: string
}

export type TSideMenuItem = {
  name: string
  path: string
  subRoutes?: Array<TSubRoutes>
}

export type TSideMenu = {
  title: string
}

export type TSideMenuTemplate = {
  title: string
  isOpen: boolean
  toggleMenu: () => void
  sideMenuList: TSideMenuItem[]
  handleToggleSubList: (subRoutes: TSubRoutes[]) => void
  expandedPaths: TSubRoutes[]
  currentPath: string
}
