import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuItemPlaceholder } from './side-menu-item-placeholder.template'

describe('Side Menu Item Placeholder', () => {
  it('should be able to render a item placeholder correctly', () => {
    const { getByTestId } = customRender(<SideMenuItemPlaceholder data-testid="item-placeholder" />)

    const itemPlaceholder = getByTestId('item-placeholder')

    expect(itemPlaceholder).toBeInTheDocument()
  })
})
