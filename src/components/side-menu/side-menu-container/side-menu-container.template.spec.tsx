import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuContainer } from './side-menu-container.template'

const generateCustomRender = (open: boolean) => {
  return customRender(
    <SideMenuContainer open={open} data-testid="container">
      <div data-testid="child">Child</div>
    </SideMenuContainer>
  )
}

describe('Side Menu Container', async () => {
  it('should be able to render a container correctly if open is true', async () => {
    const { getByTestId } = generateCustomRender(true)

    const container = getByTestId('container')

    expect(container).toBeInTheDocument()
    expect(container).toHaveStyleRule('width', '195px')
  })

  it('should be able to render a container correctly if open is false', async () => {
    const { getByTestId } = generateCustomRender(false)

    const container = getByTestId('container')

    expect(container).toBeInTheDocument()
    expect(container).toHaveStyleRule('width', '60px')
  })
})
