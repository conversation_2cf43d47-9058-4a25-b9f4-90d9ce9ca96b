import styled from 'styled-components'
import { SideMenuButton } from '../side-menu-button'
import { TSideMenuContainer } from './side-menu-container.types'

export const SideMenuContainer = styled.div<TSideMenuContainer>`
  position: relative;
  width: ${(props) => (props.open ? '195px' : '60px')};
  height: 100%;
  padding: 20px 0px;
  overflow: hidden auto;
  background-color: ${({ theme }) => theme.legacy.colors.neutral.contentBackground};
  transition: width 0.5s ease-in-out 0s;
  & > *:not(${SideMenuButton}) {
    opacity: ${(props) => (props.open ? 1 : 0)};
    visibility: ${(props) => (props.open ? 'visible' : 'hidden')};
    transition: 0.1s ease-in-out;
    transition-delay: ${(props) => (props.open ? '.3s' : '0s')};
  }
`
