import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuListContent } from './side-menu-list-content.template'

describe('Side Menu List Content', () => {
  it('should be able to render a list content correctly', () => {
    const { getByTestId } = customRender(<SideMenuListContent data-testid="list-content" />)

    const content = getByTestId('list-content')

    expect(content).toBeInTheDocument()
  })
})
