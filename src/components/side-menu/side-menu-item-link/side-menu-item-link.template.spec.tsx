import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { SideMenuItemLink } from './side-menu-item-link.template'

describe('Side Menu Item Link', () => {
  it('should be able to render item link correctly', () => {
    const { getByTestId } = customRender(
      <BrowserRouter>
        <SideMenuItemLink data-testid="item-link" to="test.test" />
      </BrowserRouter>
    )

    const itemLink = getByTestId('item-link')

    expect(itemLink).toBeInTheDocument()
  })
})
