import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuItemSubListItem } from './side-menu-item-sub-list-item.template'

describe('Side Menu Item Sub List Item', () => {
  it('should be able to render sub list item correctly', () => {
    const { getByTestId } = customRender(<SideMenuItemSubListItem data-testid="sub-list-item" />)

    const item = getByTestId('sub-list-item')

    expect(item).toBeInTheDocument()
  })
})
