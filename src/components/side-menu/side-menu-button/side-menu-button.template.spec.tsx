import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuButton } from './side-menu-button.template'

describe('Side Menu Button', () => {
  it('should be able to render a button correctly if open is true', () => {
    const { getByTestId } = customRender(
      <SideMenuButton open data-testid="button">
        Button
      </SideMenuButton>
    )

    const button = getByTestId('button')

    expect(button).toBeInTheDocument()
    expect(button).toHaveStyleRule('top', '16px')
    expect(button).toHaveStyleRule('transform', 'none')
  })

  it('should be able to render a button correctly if open is false', () => {
    const { getByTestId } = customRender(
      <SideMenuButton open={false} data-testid="button">
        Button
      </SideMenuButton>
    )

    const button = getByTestId('button')

    expect(button).toBeInTheDocument()
    expect(button).toHaveStyleRule('top', '13px')
    expect(button).toHaveStyleRule('transform', 'rotate(180deg)')
  })
})
