import styled from 'styled-components'
import { TSideMenuButton } from './side-menu-button.types'

export const SideMenuButton = styled.button<TSideMenuButton>`
  position: absolute;
  top: ${(props) => (props.open ? '16px' : '13px')};
  transform-origin: center center;
  right: 15px;
  transform: ${(props) => (props.open ? 'none' : 'rotate(180deg)')};
  background-color: transparent;
  z-index: 1;
  border: 0;
  cursor: pointer;
`
