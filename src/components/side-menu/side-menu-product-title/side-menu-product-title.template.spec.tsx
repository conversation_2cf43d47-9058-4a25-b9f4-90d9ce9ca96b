import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { SideMenuProductTitle } from './side-menu-product-title.template'

describe('Side Menu Product Title', () => {
  it('should be able to render product title correctly', () => {
    const { getByTestId } = customRender(<SideMenuProductTitle data-testid="title">Title</SideMenuProductTitle>)

    const title = getByTestId('title')

    expect(title).toBeInTheDocument()
  })
})
