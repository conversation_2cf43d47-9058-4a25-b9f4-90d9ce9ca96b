import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { <PERSON><PERSON>er<PERSON>out<PERSON> } from 'react-router-dom'
import { theme } from '@mz-codes/design-system'
import { SideMenuItemSubListItemLink } from './side-menu-item-sub-list-item-link.template'

describe('Side Menu Item Sub List Item Link', () => {
  it('should be able to render sub list item link correctly', () => {
    const { getByTestId } = customRender(
      <BrowserRouter>
        <SideMenuItemSubListItemLink to="path" data-testid="sub-list-item-link" />
      </BrowserRouter>
    )

    const link = getByTestId('sub-list-item-link')

    expect(link).toBeInTheDocument()
  })

  it('should be able to render sub list item link correctly if link is on hover', () => {
    const { getByTestId } = customRender(
      <BrowserRouter>
        <SideMenuItemSubListItemLink to="path" data-testid="sub-list-item-link" />
      </BrowserRouter>
    )

    const link = getByTestId('sub-list-item-link')

    expect(link).toHaveStyleRule('color', theme.legacy.colors.primary.primary, { modifier: ':hover' })
  })
})
