import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { theme } from '@mz-codes/design-system'
import { PageContentContainer } from './page-content-container.template'

describe('Page Content Container Template', () => {
  it('should render the page content container correctly if bgColor is filled', () => {
    const { getByTestId } = customRender(
      <PageContentContainer data-testid="page-content-container" $bgColor="tomato" />
    )

    const container = getByTestId('page-content-container')

    expect(container).toBeInTheDocument()
    expect(container).toHaveStyleRule('background-color', 'tomato')
  })

  it('should render the page content container correctly if bgColor is not filled', () => {
    const { getByTestId } = customRender(<PageContentContainer data-testid="page-content-container" />)

    const container = getByTestId('page-content-container')

    expect(container).toBeInTheDocument()
    expect(container).toHaveStyleRule('background-color', theme.legacy.colors.neutral.contentBackground)
  })
})
