import styled from 'styled-components'

import { ScrollBar } from '@mz-codes/design-system'

import { TContainer } from './page-content-container.types'

export const PageContentContainer = styled.div<TContainer>`
  background-color: ${({ $bgColor, theme }) => $bgColor || theme.legacy.colors.neutral.contentBackground};
  overflow: auto;
  padding-bottom: ${({ theme }) => theme.legacy.units.lg};
  border-radius: ${({ theme }) => theme.legacy.units.md};
  flex: 1 1 auto;
  height: -webkit-fill-available;
  ${ScrollBar}
`
