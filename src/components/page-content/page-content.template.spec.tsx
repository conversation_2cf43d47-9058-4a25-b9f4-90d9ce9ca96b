import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { PageContent } from './page-content.template'

describe('Page Content Template', () => {
  it('should render the page content correctly', () => {
    const { getByTestId, getByText } = customRender(
      <PageContent>
        <div>Children</div>
      </PageContent>
    )

    expect(getByTestId('page-content')).toBeInTheDocument()
    expect(getByText('Children')).toBeInTheDocument()
  })
})
