import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TearsheetModalDescription } from './tearsheet-modal-description.template'

describe('Tearsheet Modal Description', () => {
  it('shoul render correctly', () => {
    const { getByTestId } = customRender(<TearsheetModalDescription data-testid="tearhsheet-modal-description" />)

    expect(getByTestId('tearhsheet-modal-description')).toBeInTheDocument()
  })
})
