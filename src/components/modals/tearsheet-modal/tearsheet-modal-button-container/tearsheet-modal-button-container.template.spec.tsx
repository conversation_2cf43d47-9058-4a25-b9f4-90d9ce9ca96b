import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TearsheetButtonContainer } from './tearsheet-modal-button-container.template'

describe('Tearsheet Modal Button Container', () => {
  it('shoul render correctly', () => {
    const { getByTestId } = customRender(<TearsheetButtonContainer data-testid="tearhsheet-modal-button-container" />)

    expect(getByTestId('tearhsheet-modal-button-container')).toBeInTheDocument()
  })
})
