import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TearsheetModalOptionSelectedIcon } from './tearsheet-modal-option-selected-icon.template'

describe('Tearsheet Modal Option Selected Icon', () => {
  it('shoul render correctly', () => {
    const { getByTestId } = customRender(
      <TearsheetModalOptionSelectedIcon data-testid="tearhsheet-modal-option-selected-icon" />
    )

    expect(getByTestId('tearhsheet-modal-option-selected-icon')).toBeInTheDocument()
  })
})
