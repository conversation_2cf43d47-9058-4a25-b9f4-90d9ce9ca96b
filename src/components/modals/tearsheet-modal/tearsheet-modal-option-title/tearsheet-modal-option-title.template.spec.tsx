import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TearsheetModalOptionTitle } from './tearsheet-modal-option-title.template'

describe('Tearsheet Modal Option Title', () => {
  it('shoul render enabled correctly', () => {
    const { getByTestId } = customRender(<TearsheetModalOptionTitle data-testid="tearhsheet-modal-option-title" />)

    expect(getByTestId('tearhsheet-modal-option-title')).toBeInTheDocument()
  })
})
