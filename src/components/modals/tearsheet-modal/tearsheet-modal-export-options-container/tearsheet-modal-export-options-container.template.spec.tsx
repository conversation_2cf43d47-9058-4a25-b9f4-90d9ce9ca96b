import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TearsheetModalExportOptionsContainer } from './tearsheet-modal-export-options-container.template'

describe('Tearsheet Modal Export Options Container', () => {
  it('shoul render correctly', () => {
    const { getByTestId } = customRender(
      <TearsheetModalExportOptionsContainer data-testid="tearhsheet-modal-export-options-container" />
    )

    expect(getByTestId('tearhsheet-modal-export-options-container')).toBeInTheDocument()
  })
})
