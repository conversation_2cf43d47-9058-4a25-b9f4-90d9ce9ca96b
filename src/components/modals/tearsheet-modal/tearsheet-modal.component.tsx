import { useEffect, useState } from 'react'

import { TTearsheetModal, ExportTearsheetType, TExportTearsheetType } from './tearsheet-modal.types'
import { TearsheetModalTemplate } from './tearsheet-modal.template'

export function TearsheetModal(props: TTearsheetModal) {
  const { show, onClose, onExportSelectedPeriod, onExportHistoricPosition } = props

  const [exportType, setExportType] = useState<TExportTearsheetType | undefined>(undefined)

  const handleActiveExportType = (activeExport: TExportTearsheetType) => {
    setExportType(activeExport)
  }

  const handleExport = (exportTypeParam?: TExportTearsheetType) => {
    if (exportTypeParam === ExportTearsheetType.period) {
      onExportSelectedPeriod()
    }
    if (exportTypeParam === ExportTearsheetType.history) {
      onExportHistoricPosition()
    }
    onClose()
  }

  useEffect(() => {
    setExportType(undefined)
  }, [show])

  return (
    <TearsheetModalTemplate
      exportType={exportType}
      handleActiveExportType={handleActiveExportType}
      handleExport={handleExport}
      onClose={onClose}
      show={show}
    />
  )
}
