import styled, { DefaultTheme } from 'styled-components'
import { TearsheetModalOptionDescription } from '../tearsheet-modal-option-description'
import { TearsheetModalOptionTitle } from '../tearsheet-modal-option-title'
import { TearsheetModalOptionHistoryIcon } from '../tearsheet-modal-option-history-icon'
import { TearsheetModalOptionCalendarIcon } from '../tearsheet-modal-option-calendar-icon'
import { TOptionContainer } from './tearsheet-modal-option-container.types'

const getBorderStyle = (isActive: boolean, theme: DefaultTheme): string =>
  isActive
    ? `1px solid ${theme.legacy.colors.primary.highlight}`
    : `1px dashed ${theme.legacy.colors.grayScale.textsOpacity}`

const getHighlightColor = (isActive: boolean, theme: DefaultTheme): string =>
  isActive ? theme.legacy.colors.primary.highlight : 'white'

export const TearsheetModalOptionContainer = styled.div<TOptionContainer>`
  width: 47%;
  display: flex;
  position: relative;
  background-color: transparent;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 20px;
  border: ${({ $isActive, theme }) => getBorderStyle($isActive, theme)};
  text-align: center;
  min-height: 200px;
  transition: background-color 0.3s;

  &:hover {
    background-color: #202330;
    cursor: pointer;
  }

  ${TearsheetModalOptionTitle},
  ${TearsheetModalOptionDescription} {
    color: ${({ $isActive, theme }) => getHighlightColor($isActive, theme)};
  }

  ${TearsheetModalOptionCalendarIcon} {
    stroke: ${({ $isActive, theme }) => getHighlightColor($isActive, theme)};
  }

  ${TearsheetModalOptionHistoryIcon} {
    color: ${({ $isActive, theme }) => getHighlightColor($isActive, theme)};
  }

  svg {
    margin-bottom: auto;
  }
`
