import { describe, it, expect } from 'vitest'
import { customRender } from 'test'
import { theme } from '@mz-codes/design-system'

import { TearsheetModalOptionContainer } from './tearsheet-modal-option-container.template'
import { TearsheetModalOptionDescription } from '../tearsheet-modal-option-description'
import { TearsheetModalOptionTitle } from '../tearsheet-modal-option-title'
import { TearsheetModalOptionHistoryIcon } from '../tearsheet-modal-option-history-icon'
import { TearsheetModalOptionCalendarIcon } from '../tearsheet-modal-option-calendar-icon'

const renderOptionContainer = (isActive: boolean) => {
  return customRender(
    <TearsheetModalOptionContainer $isActive={isActive} data-testid="option-container">
      <TearsheetModalOptionTitle>Title</TearsheetModalOptionTitle>
      <TearsheetModalOptionDescription>Description</TearsheetModalOptionDescription>
      <TearsheetModalOptionHistoryIcon />
      <TearsheetModalOptionCalendarIcon />
    </TearsheetModalOptionContainer>
  )
}

describe('TearsheetModalOptionContainer', () => {
  it('applies active styles correctly when $isActive is true', () => {
    const { getByTestId } = renderOptionContainer(true)

    const container = getByTestId('option-container')

    expect(container).toHaveStyleRule('border', `1px solid ${theme.legacy.colors.primary.highlight}`)
    expect(container).toHaveStyleRule('color', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalOptionTitle}`,
    })
    expect(container).toHaveStyleRule('color', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalOptionDescription}`,
    })
    expect(container).toHaveStyleRule('stroke', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalOptionCalendarIcon}`,
    })
    expect(container).toHaveStyleRule('color', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalOptionHistoryIcon}`,
    })
  })

  it('applies inactive styles correctly when $isActive is false', () => {
    const { getByTestId } = renderOptionContainer(false)

    const container = getByTestId('option-container')

    expect(container).toHaveStyleRule('border', `1px dashed ${theme.legacy.colors.grayScale.textsOpacity}`)
    expect(container).toHaveStyleRule('color', `white`, {
      modifier: `${TearsheetModalOptionTitle}`,
    })
    expect(container).toHaveStyleRule('color', `white`, {
      modifier: `${TearsheetModalOptionDescription}`,
    })
    expect(container).toHaveStyleRule('stroke', `white`, {
      modifier: `${TearsheetModalOptionCalendarIcon}`,
    })
    expect(container).toHaveStyleRule('color', `white`, {
      modifier: `${TearsheetModalOptionHistoryIcon}`,
    })
  })
})
