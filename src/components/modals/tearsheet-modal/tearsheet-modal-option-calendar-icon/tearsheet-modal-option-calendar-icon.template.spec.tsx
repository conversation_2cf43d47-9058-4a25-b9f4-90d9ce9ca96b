import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TearsheetModalOptionCalendarIcon } from './tearsheet-modal-option-calendar-icon.template'

describe('Tearsheet Modal Option Calendar Icon', () => {
  it('shoul render correctly', () => {
    const { getByTestId } = customRender(
      <TearsheetModalOptionCalendarIcon data-testid="tearhsheet-modal-option-calendar-icon" />
    )

    expect(getByTestId('tearhsheet-modal-option-calendar-icon')).toBeInTheDocument()
  })
})
