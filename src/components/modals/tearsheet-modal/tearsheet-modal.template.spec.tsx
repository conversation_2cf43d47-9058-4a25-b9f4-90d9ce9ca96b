import { describe, it, expect, vi } from 'vitest'

import { customRender } from 'test'
import { theme } from '@mz-codes/design-system'

import { TearsheetModalTemplate } from './tearsheet-modal.template'
import { TTearsheetModalTemplate, ExportTearsheetType } from './tearsheet-modal.types'

const generateMockData = () => {
  const onClose = vi.fn()
  const handleActiveExportType = vi.fn()
  const handleExport = vi.fn()

  const mockProps: TTearsheetModalTemplate = {
    show: true,
    onClose,
    handleActiveExportType,
    handleExport,
    exportType: undefined,
  }

  return { mockProps }
}

const renderTearsheetModalTemplate = (props: TTearsheetModalTemplate) => {
  return customRender(<TearsheetModalTemplate {...props} />)
}

describe('Tearsheet Modal Template', () => {
  it('should renders the component correctly', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
    })

    const period = getByTestId('tearsheet-modal')

    expect(period).toBeInTheDocument()
  })

  it('should renders header correctly', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
    })

    const header = getByTestId('tearsheet-modal-header')

    expect(header).toBeInTheDocument()
  })

  it('should renders body correctly', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
    })

    const header = getByTestId('tearsheet-modal-body')

    expect(header).toBeInTheDocument()
  })

  it('renders correctly when period is active', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
      exportType: ExportTearsheetType.period,
    })

    const period = getByTestId('option-container-period')

    expect(period).toBeInTheDocument()
    expect(period).toHaveStyleRule('border', `1px solid ${theme.legacy.colors.primary.highlight}`)
    expect(period).toHaveStyleRule('color', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalTemplate.OptionTitle}`,
    })
    expect(period).toHaveStyleRule('color', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalTemplate.OptionDescription}`,
    })
    expect(period).toHaveStyleRule('stroke', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalTemplate.OptionCalendarIcon}`,
    })
  })

  it('renders correctly when history is active', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
      exportType: ExportTearsheetType.history,
    })

    const history = getByTestId('option-container-history')

    expect(history).toBeInTheDocument()
    expect(history).toHaveStyleRule('border', `1px solid ${theme.legacy.colors.primary.highlight}`)
    expect(history).toHaveStyleRule('color', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalTemplate.OptionTitle}`,
    })
    expect(history).toHaveStyleRule('color', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalTemplate.OptionDescription}`,
    })
    expect(history).toHaveStyleRule('stroke', `${theme.legacy.colors.primary.highlight}`, {
      modifier: `${TearsheetModalTemplate.OptionCalendarIcon}`,
    })
  })

  it('renders correctly when exportType is undefined', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
    })

    const history = getByTestId('option-container-history')
    const period = getByTestId('option-container-period')

    expect(history).toBeInTheDocument()
    expect(history).toHaveStyleRule('border', `1px dashed ${theme.legacy.colors.grayScale.textsOpacity}`)

    expect(period).toBeInTheDocument()
    expect(period).toHaveStyleRule('border', `1px dashed ${theme.legacy.colors.grayScale.textsOpacity}`)
  })

  it('should render the cancel button correctly', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
    })

    const button = getByTestId('cancel-button')

    expect(button).toBeInTheDocument()
  })

  it('should render the export button enabled if the exportType is other than undefined', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
      exportType: ExportTearsheetType.history,
    })

    const button = getByTestId('export-button')

    expect(button).not.toBeDisabled()
  })

  it('should render the export button disabled if the exportType is undefined', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModalTemplate({
      ...mockProps,
    })

    const button = getByTestId('export-button')

    expect(button).toBeDisabled()
  })
})
