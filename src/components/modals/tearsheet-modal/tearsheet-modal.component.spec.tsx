import { describe, it, expect, vi } from 'vitest'
import userEvent from '@testing-library/user-event'

import { customRender } from 'test'
import { TTearsheetModal } from './tearsheet-modal.types'
import { TearsheetModal } from './tearsheet-modal.component'

const generateMockData = () => {
  const onClose = vi.fn()
  const onExportSelectedPeriod = vi.fn()
  const onExportHistoricPosition = vi.fn()

  const mockProps: TTearsheetModal = {
    show: true,
    onClose,
    onExportSelectedPeriod,
    onExportHistoricPosition,
  }

  return { mockProps }
}

const renderTearsheetModal = (props: TTearsheetModal) => {
  return customRender(<TearsheetModal {...props} />)
}

describe('TearsheetModal', () => {
  it('calls onExportSelectedPeriod when period export is selected and export button is clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModal(mockProps)

    const periodOption = getByTestId('option-container-period')
    await userEvent.click(periodOption)

    const exportButton = getByTestId('export-button')
    await userEvent.click(exportButton)

    expect(mockProps.onExportSelectedPeriod).toHaveBeenCalledTimes(1)
    expect(mockProps.onClose).toHaveBeenCalledTimes(1)
  })

  it('calls onExportHistoricPosition when history export is selected and export button is clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModal(mockProps)

    const historyOption = getByTestId('option-container-history')
    await userEvent.click(historyOption)

    const exportButton = getByTestId('export-button')
    await userEvent.click(exportButton)

    expect(mockProps.onExportHistoricPosition).toHaveBeenCalledTimes(1)
    expect(mockProps.onClose).toHaveBeenCalledTimes(1)
  })

  it('does not call export functions when exportType is undefined and export button is clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = renderTearsheetModal(mockProps)

    const exportButton = getByTestId('export-button')
    await userEvent.click(exportButton)

    expect(mockProps.onExportSelectedPeriod).toHaveBeenCalledTimes(0)
    expect(mockProps.onExportHistoricPosition).toHaveBeenCalledTimes(0)
    expect(mockProps.onClose).toHaveBeenCalledTimes(0)
  })
})
