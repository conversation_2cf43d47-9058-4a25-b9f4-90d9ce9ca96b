import { theme, BaseModal, Icons, Buttons } from '@mz-codes/design-system'

import { TTearsheetModalTemplate, ExportTearsheetType } from './tearsheet-modal.types'
import { TearsheetModalExportOptionsContainer } from './tearsheet-modal-export-options-container'
import { TearsheetModalDescription } from './tearsheet-modal-description'
import { TearsheetModalOptionContainer } from './tearsheet-modal-option-container'
import { TearsheetModalOptionTitle } from './tearsheet-modal-option-title'
import { TearsheetModalOptionDescription } from './tearsheet-modal-option-description'
import { TearsheetModalOptionCalendarIcon } from './tearsheet-modal-option-calendar-icon'
import { TearsheetModalOptionHistoryIcon } from './tearsheet-modal-option-history-icon'
import { TearsheetButtonContainer } from './tearsheet-modal-button-container'
import { TearsheetModalOptionSelectedIcon } from './tearsheet-modal-option-selected-icon'
import { translations } from './tearsheet-modal.translations'

export function TearsheetModalTemplate(props: TTearsheetModalTemplate) {
  const { show, onClose, handleActiveExportType, handleExport, exportType } = props

  return (
    <BaseModal show={show} onClose={onClose} data-testid="tearsheet-modal">
      <BaseModal.Header data-testid="tearsheet-modal-header">
        <BaseModal.Title>{translations.export}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body data-testid="tearsheet-modal-body">
        <TearsheetModalTemplate.Description>{translations.selectOneOption}</TearsheetModalTemplate.Description>
        <TearsheetModalTemplate.ExportOptionsContainer>
          <TearsheetModalTemplate.OptionContainer
            data-testid="option-container-period"
            onClick={() => handleActiveExportType(ExportTearsheetType.period)}
            $isActive={exportType === ExportTearsheetType.period}
          >
            <TearsheetModalTemplate.OptionTitle>{translations.selectedPeriod}</TearsheetModalTemplate.OptionTitle>
            <TearsheetModalTemplate.OptionCalendarIcon size={45} />
            <TearsheetModalTemplate.OptionDescription>
              {translations.selectedPeriodDescription}
            </TearsheetModalTemplate.OptionDescription>
            {exportType === ExportTearsheetType.period && (
              <TearsheetModalTemplate.OptionSelectedIcon>
                <Icons.Check size={12} color={theme.legacy.colors.grayScale.texts} />
              </TearsheetModalTemplate.OptionSelectedIcon>
            )}
          </TearsheetModalTemplate.OptionContainer>
          <TearsheetModalTemplate.OptionContainer
            data-testid="option-container-history"
            onClick={() => handleActiveExportType(ExportTearsheetType.history)}
            $isActive={exportType === ExportTearsheetType.history}
          >
            <TearsheetModalTemplate.OptionTitle>{translations.positionHistory}</TearsheetModalTemplate.OptionTitle>
            <TearsheetModalTemplate.OptionHistoryIcon size={45} />
            <TearsheetModalTemplate.OptionDescription>
              {translations.positionHistoryDescription}
            </TearsheetModalTemplate.OptionDescription>
            {exportType === ExportTearsheetType.history && (
              <TearsheetModalTemplate.OptionSelectedIcon>
                <Icons.Check size={12} color={theme.legacy.colors.grayScale.texts} />
              </TearsheetModalTemplate.OptionSelectedIcon>
            )}
          </TearsheetModalTemplate.OptionContainer>
        </TearsheetModalTemplate.ExportOptionsContainer>

        <BaseModal.Footer>
          <BaseModal.ButtonWrapper>
            <Buttons.Cancel data-testid="cancel-button" onClick={onClose}>
              {translations.cancelButtonLabel}
            </Buttons.Cancel>

            <Buttons.Primary
              data-testid="export-button"
              disabled={exportType === undefined}
              onClick={() => handleExport(exportType)}
            >
              {translations.export}
            </Buttons.Primary>
          </BaseModal.ButtonWrapper>
        </BaseModal.Footer>
      </BaseModal.Body>
    </BaseModal>
  )
}

TearsheetModalTemplate.ExportOptionsContainer = TearsheetModalExportOptionsContainer
TearsheetModalTemplate.Description = TearsheetModalDescription
TearsheetModalTemplate.OptionContainer = TearsheetModalOptionContainer
TearsheetModalTemplate.OptionTitle = TearsheetModalOptionTitle
TearsheetModalTemplate.OptionDescription = TearsheetModalOptionDescription
TearsheetModalTemplate.OptionCalendarIcon = TearsheetModalOptionCalendarIcon
TearsheetModalTemplate.OptionHistoryIcon = TearsheetModalOptionHistoryIcon
TearsheetModalTemplate.ButtonContainer = TearsheetButtonContainer
TearsheetModalTemplate.OptionSelectedIcon = TearsheetModalOptionSelectedIcon
