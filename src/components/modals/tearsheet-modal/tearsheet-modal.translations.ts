import { i18n } from 'translate'

export const translations = {
  export: i18n.t('pagePrivateTearSheet.tearsheetModal.export'),
  selectOneOption: i18n.t('pagePrivateTearSheet.tearsheetModal.selectOneOption'),
  selectedPeriod: i18n.t('pagePrivateTearSheet.tearsheetModal.selectedPeriod'),
  selectedPeriodDescription: i18n.t('pagePrivateTearSheet.tearsheetModal.selectedPeriodDescription'),
  positionHistory: i18n.t('pagePrivateTearSheet.tearsheetModal.positionHistory'),
  positionHistoryDescription: i18n.t('pagePrivateTearSheet.tearsheetModal.positionHistoryDescription'),
  cancelButtonLabel: i18n.t('components.confirmModal.cancelButton'),
}
