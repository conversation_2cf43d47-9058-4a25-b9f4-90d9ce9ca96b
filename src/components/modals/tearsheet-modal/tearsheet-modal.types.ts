export type TTearsheetModal = {
  show: boolean
  onClose: () => void
  onExportSelectedPeriod: () => void
  onExportHistoricPosition: () => void
}

export type TTearsheetModalTemplate = {
  show: boolean
  onClose: () => void
  handleActiveExportType: (activeExport: TExportTearsheetType) => void
  handleExport: (exportTypeParam?: TExportTearsheetType) => void
  exportType?: 'history' | 'period'
}

export const ExportTearsheetType = {
  period: 'period',
  history: 'history',
} as const

export type TExportTearsheetType = keyof typeof ExportTearsheetType
