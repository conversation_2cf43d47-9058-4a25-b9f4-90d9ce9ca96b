import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TearsheetModalOptionDescription } from './tearsheet-modal-option-description.template'

describe('Tearsheet Modal Option Description', () => {
  it('shoul render correctly', () => {
    const { getByTestId } = customRender(
      <TearsheetModalOptionDescription data-testid="tearhsheet-modal-option-description" />
    )

    expect(getByTestId('tearhsheet-modal-option-description')).toBeInTheDocument()
  })
})
