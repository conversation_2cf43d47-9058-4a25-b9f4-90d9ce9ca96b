import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { TearsheetModalOptionHistoryIcon } from './tearsheet-modal-option-history-icon.template'

describe('Tearsheet Modal Option History Icon', () => {
  it('shoul render correctly', () => {
    const { getByTestId } = customRender(
      <TearsheetModalOptionHistoryIcon data-testid="tearhsheet-modal-option-history-icon" />
    )

    expect(getByTestId('tearhsheet-modal-option-history-icon')).toBeInTheDocument()
  })
})
