import parse from 'html-react-parser'
import { BaseModal, BaseLinedInput, Table } from '@mz-codes/design-system'
import { translations } from './monitored-shareholders-search-modal.translations'
import { TMonitoredShareholdersSearchModalTemplate } from './monitored-shareholders-search-modal.types'
import { ShareholderTypeWrapper } from './monitored-shareholders-search-modal-shareholder-type-wrapper'
import { MonitoredShareholdersSearchModalTableContainer } from './monitored-shareholders-search-modal-table-container'

export function MonitoredShareholdersSearchModalTemplate(props: TMonitoredShareholdersSearchModalTemplate) {
  const {
    visibility,
    title,
    onClose,
    message,
    inputValue,
    inputRef,
    inputHandleChange,
    handleScroll,
    shareholders,
    isFetching,
    handleSelect,
    ...rest
  } = props

  const { name, document, type, searchPlaceholder } = translations

  return (
    <BaseModal show={visibility} onClose={onClose} width="780px" {...rest}>
      <BaseModal.Header>
        <BaseModal.Title>{title}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body>
        <BaseLinedInput
          name="search"
          value={inputValue}
          placeholder={searchPlaceholder}
          onChange={inputHandleChange}
          autocomplete="off"
          ref={inputRef}
        />
        <MonitoredShareholdersSearchModalTableContainer height={440}>
          <Table>
            <Table.THead>
              <Table.TR>
                <Table.TH>{name}</Table.TH>
                <Table.TH>{document}</Table.TH>
                <Table.TH>{type}</Table.TH>
              </Table.TR>
            </Table.THead>
            <Table.TBody onScroll={handleScroll} data-testid="table-body">
              {shareholders.map((shareholder) => {
                const {
                  contactId,
                  name: shareholderName,
                  document: shareholderDocument,
                  shareholderImage,
                  translatedShareholderType,
                } = shareholder
                return (
                  <Table.TR
                    key={contactId}
                    onClick={() => handleSelect(shareholder)}
                    data-testid="monitored-shareholders-search-table"
                    style={{ cursor: isFetching ? 'wait' : 'pointer' }}
                  >
                    <Table.TD>{shareholderName}</Table.TD>
                    <Table.TD>{shareholderDocument}</Table.TD>
                    <Table.TD>
                      <ShareholderTypeWrapper>
                        {shareholderImage && <img alt="shareholder" src={shareholderImage} />}
                        {translatedShareholderType}
                      </ShareholderTypeWrapper>
                    </Table.TD>
                  </Table.TR>
                )
              })}
            </Table.TBody>
          </Table>
        </MonitoredShareholdersSearchModalTableContainer>
      </BaseModal.Body>
      <BaseModal.Footer>{message && <BaseModal.Text>{parse(message)}</BaseModal.Text>}</BaseModal.Footer>
    </BaseModal>
  )
}
