import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { customRender } from 'test'
import { SerializedMonitoredShareholder } from 'hooks'
import userEvent from '@testing-library/user-event'
import { MonitoredShareholdersSearchModalTemplate } from './monitored-shareholders-search-modal.template'

const stubFunctionMock = () => {}

describe('Monitored Shareholders Search Modal', () => {
  beforeEach(async () => {
    vi.clearAllMocks()

    vi.mock('components', async () => {
      const { BaseModal } = await vi.importActual('@mz-codes/design-system')
      const { BaseLinedInput } = await vi.importActual('@mz-codes/design-system')
      const { Table } = await vi.importActual('components')

      return {
        BaseModal,
        BaseLinedInput,
        Table,
      }
    })

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn().mockImplementation((key: string) => key),
      },
    }))
  })

  it('should be able to render modal', async () => {
    const { getByTestId } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={[]}
      />
    )
    expect(getByTestId('monitored-shareholders-modal')).toBeInTheDocument()
  })

  it('should not render modal when visibility is false', async () => {
    const { queryByTestId } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility={false}
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={[]}
      />
    )
    expect(queryByTestId('monitored-shareholders-modal')).not.toBeInTheDocument()
  })

  it('should render show title props on document', async () => {
    const title = 'any-title'
    const { getByText } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title={title}
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={[]}
      />
    )
    expect(getByText(title)).toBeInTheDocument()
  })

  it('should render message if message prop is passed', async () => {
    const message = 'any-message'

    const { getByText } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        message={message}
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={[]}
      />
    )
    expect(getByText(message)).toBeInTheDocument()
  })

  it('should render shareholders document', async () => {
    const shareholders: SerializedMonitoredShareholder[] = [
      {
        document: 'document-1',
        name: 'any-name-1',
        contactId: 'any-id-1',
        documentType: 1,
        translatedShareholderType: 'any-type-1',
        shareholderImage: '',
      },
      {
        document: 'document-2',
        name: 'any-name-2',
        contactId: 'any-id-2',
        documentType: 1,
        translatedShareholderType: 'any-type-2',
        shareholderImage: '',
      },
    ]

    const { getByText } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={shareholders}
      />
    )
    expect(getByText('document-1')).toBeInTheDocument()
    expect(getByText('document-2')).toBeInTheDocument()
  })

  it('should render shareholders name', async () => {
    const shareholders: SerializedMonitoredShareholder[] = [
      {
        document: 'document-1',
        name: 'any-name-1',
        contactId: 'any-id-1',
        documentType: 1,
        translatedShareholderType: 'any-type-1',
        shareholderImage: '',
      },
      {
        document: 'document-2',
        name: 'any-name-2',
        contactId: 'any-id-2',
        documentType: 1,
        translatedShareholderType: 'any-type-2',
        shareholderImage: '',
      },
    ]

    const { getByText } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={shareholders}
      />
    )
    expect(getByText('any-name-1')).toBeInTheDocument()
    expect(getByText('any-name-2')).toBeInTheDocument()
  })

  it('should render translated shareholders type', async () => {
    const shareholders: SerializedMonitoredShareholder[] = [
      {
        document: 'document-1',
        name: 'any-name-1',
        contactId: 'any-id-1',
        documentType: 1,
        translatedShareholderType: 'any-type-1',
        shareholderImage: '',
      },
      {
        document: 'document-2',
        name: 'any-name-2',
        contactId: 'any-id-2',
        documentType: 1,
        translatedShareholderType: 'any-type-2',
        shareholderImage: '',
      },
    ]

    const { getByText } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={shareholders}
      />
    )
    expect(getByText('any-type-1')).toBeInTheDocument()
    expect(getByText('any-type-2')).toBeInTheDocument()
  })

  it('should not render shareholders image if no shareholder img is sent', async () => {
    const shareholders: SerializedMonitoredShareholder[] = [
      {
        document: 'document-1',
        name: 'any-name-1',
        contactId: 'any-id-1',
        documentType: 1,
        translatedShareholderType: 'any-type-1',
        shareholderImage: '',
      },
      {
        document: 'document-2',
        name: 'any-name-2',
        contactId: 'any-id-2',
        documentType: 1,
        translatedShareholderType: 'any-type-2',
        shareholderImage: '',
      },
    ]

    const { queryByAltText } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={shareholders}
      />
    )
    expect(queryByAltText('shareholder')).not.toBeInTheDocument()
  })

  it('should render shareholders image if shareholder img is sent', async () => {
    const shareholders: SerializedMonitoredShareholder[] = [
      {
        document: 'document-1',
        name: 'any-name-1',
        contactId: 'any-id-1',
        documentType: 1,
        translatedShareholderType: 'any-type-1',
        shareholderImage: 'any-image',
      },
      {
        document: 'document-2',
        name: 'any-name-2',
        contactId: 'any-id-2',
        documentType: 1,
        translatedShareholderType: 'any-type-2',
        shareholderImage: '',
      },
    ]

    const { queryByAltText } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={shareholders}
      />
    )
    expect(queryByAltText('shareholder')).toBeInTheDocument()
  })

  it('should call handle change on click in contact table', async () => {
    const handleSelectSpy = vi.fn()

    const shareholders: SerializedMonitoredShareholder[] = [
      {
        document: 'document-1',
        name: 'any-name-1',
        contactId: 'any-id-1',
        documentType: 1,
        translatedShareholderType: 'any-type-1',
        shareholderImage: '',
      },
    ]

    const { getByTestId } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={handleSelectSpy}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={shareholders}
      />
    )

    const table = getByTestId('monitored-shareholders-search-table')
    await userEvent.click(table)

    expect(handleSelectSpy).toHaveBeenCalledOnce()
  })

  it('should set cursor to wait when isFetching is true', async () => {
    const shareholders: SerializedMonitoredShareholder[] = [
      {
        document: 'document-1',
        name: 'any-name-1',
        contactId: 'any-id-1',
        documentType: 1,
        translatedShareholderType: 'any-type-1',
        shareholderImage: '',
      },
    ]

    const { getByTestId } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching
        inputRef={React.createRef()}
        inputValue=""
        shareholders={shareholders}
      />
    )

    const table = getByTestId('monitored-shareholders-search-table')

    expect(table).toHaveStyle('cursor: wait')
  })

  it('should set cursor to pointer when isFetching is false', async () => {
    const shareholders: SerializedMonitoredShareholder[] = [
      {
        document: 'document-1',
        name: 'any-name-1',
        contactId: 'any-id-1',
        documentType: 1,
        translatedShareholderType: 'any-type-1',
        shareholderImage: '',
      },
    ]

    const { getByTestId } = customRender(
      <MonitoredShareholdersSearchModalTemplate
        data-testid="monitored-shareholders-modal"
        visibility
        title="any-title"
        onClose={stubFunctionMock}
        handleScroll={stubFunctionMock}
        handleSelect={stubFunctionMock}
        inputHandleChange={stubFunctionMock}
        isFetching={false}
        inputRef={React.createRef()}
        inputValue=""
        shareholders={shareholders}
      />
    )

    const table = getByTestId('monitored-shareholders-search-table')

    expect(table).toHaveStyle('cursor: pointer')
  })
})
