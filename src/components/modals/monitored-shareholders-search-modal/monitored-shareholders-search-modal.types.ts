import { TOption } from '@mz-codes/design-system'
import { SerializedMonitoredShareholder } from 'hooks'

export type TMonitoredShareholdersSearchModal = {
  title: string
  message?: string
  visibility: boolean
  onClose: () => void
  classification: TOption

  setSelected(monitoredShareholder: SerializedMonitoredShareholder): void
}

export type TMonitoredShareholdersSearchModalTemplate = {
  visibility: boolean
  title: string
  onClose: () => void
  message?: string
  inputValue: string
  inputRef: React.RefObject<HTMLInputElement>
  inputHandleChange(event: React.ChangeEvent<HTMLInputElement>): void
  handleScroll(event: React.UIEvent): void
  shareholders: SerializedMonitoredShareholder[]
  isFetching: boolean
  handleSelect(monitoredShareholder: SerializedMonitoredShareholder): void
}
