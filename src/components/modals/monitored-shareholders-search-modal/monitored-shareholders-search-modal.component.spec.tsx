import { customRender } from 'test'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { BaseError } from 'errors'
import { userEvent } from '@testing-library/user-event'
import { fireEvent } from '@testing-library/react'
import { DOCUMENT_TYPES } from 'types/shareholders'
import { MonitoredShareholdersSearchModal } from './monitored-shareholders-search-modal.component'

const generateMockData = () => {
  const getSerializedMonitoredShareholdersMock = vi.hoisted(() => vi.fn())
  getSerializedMonitoredShareholdersMock.mockImplementation(() => {
    return [
      {
        contactId: `${Math.random()}`,
        name: 'all',
        document: 'any-document',
        documentType: DOCUMENT_TYPES.UNKNOW,
        translatedShareholderType: 'any-type',
        shareholderImage: '',
      },
    ]
  })
  const createToastMock = vi.hoisted(() => vi.fn())
  return { getSerializedMonitoredShareholdersMock, createToastMock }
}

describe('Monitored Shareholders Search Modal Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn((key: string) => key),
      },
    }))

    vi.mock('hooks', () => {
      const useDebounce = vi.fn().mockImplementation((value) => value)
      return {
        useDebounce,
      }
    })

    vi.mock('@mz-codes/design-system', async () => {
      const actual = await vi.importActual('@mz-codes/design-system')
      const useToast = vi.fn().mockReturnValue({ createToast: vi.fn() })
      return {
        ...actual,
        useToast,
      }
    })

    vi.mock('components', async () => {
      const { BaseModal } = await vi.importActual('@mz-codes/design-system')
      const { BaseLinedInput } = await vi.importActual('@mz-codes/design-system')
      const { Table } = await vi.importActual('components')

      return {
        BaseModal,
        BaseLinedInput,
        Table,
      }
    })
  })

  it('should render without crashing', () => {
    const { getByTestId } = customRender(
      <MonitoredShareholdersSearchModal
        setSelected={() => {}}
        title="any-title"
        onClose={() => {}}
        visibility
        classification={{ label: '', value: '' }}
      />
    )
    expect(getByTestId('portal-test')).toBeInTheDocument()
  })

  it('should call handleChange on textbox write select', async () => {
    const SHAREHOLDER_NAME = 'any-name'
    const { getSerializedMonitoredShareholdersMock } = generateMockData()
    vi.mock('hooks/useMonitoredShareholders', () => ({
      getSerializedMonitoredShareholders: getSerializedMonitoredShareholdersMock,
      SerializedMonitoredShareholder: null,
    }))

    const { getByRole } = customRender(
      <MonitoredShareholdersSearchModal
        setSelected={() => {}}
        title="any-title"
        onClose={() => {}}
        visibility
        classification={{ label: '', value: '' }}
      />
    )
    const textbox = getByRole('textbox')
    await userEvent.type(textbox, SHAREHOLDER_NAME)
    expect(getSerializedMonitoredShareholdersMock).toBeCalledTimes(SHAREHOLDER_NAME.length)
  })

  it('should call createToast on BaseError', async () => {
    const SHAREHOLDER_NAME = 'any-name'
    const { getSerializedMonitoredShareholdersMock, createToastMock } = generateMockData()
    const baseError = new BaseError()
    getSerializedMonitoredShareholdersMock.mockImplementationOnce(() => {
      throw baseError
    })
    vi.mock('hooks/useMonitoredShareholders', () => ({
      getSerializedMonitoredShareholders: getSerializedMonitoredShareholdersMock,
      SerializedMonitoredShareholder: null,
    }))

    vi.mock('@mz-codes/design-system', async () => {
      const actual = await vi.importActual('@mz-codes/design-system')
      const useToast = vi.fn().mockReturnValue({ createToast: vi.fn() })
      return {
        ...actual,
        useToast,
      }
    })

    vi.mock('hooks', () => {
      const useDebounce = vi.fn().mockImplementation((value) => value)
      return {
        useDebounce,
      }
    })

    const { getByRole } = customRender(
      <MonitoredShareholdersSearchModal
        setSelected={() => {}}
        title="any-title"
        onClose={() => {}}
        visibility
        classification={{ label: '', value: '' }}
      />
    )
    const textbox = getByRole('textbox')
    await userEvent.type(textbox, SHAREHOLDER_NAME)
    expect(createToastMock).toBeCalledWith({
      type: 'error',
      title: baseError.title,
      description: baseError.message,
    })
  })

  it('should call createToast on generic Error', async () => {
    const SHAREHOLDER_NAME = 'any-name'
    const { getSerializedMonitoredShareholdersMock, createToastMock } = generateMockData()
    getSerializedMonitoredShareholdersMock.mockImplementationOnce(() => {
      throw new Error()
    })
    vi.mock('hooks/useMonitoredShareholders', () => ({
      getSerializedMonitoredShareholders: getSerializedMonitoredShareholdersMock,
      SerializedMonitoredShareholder: null,
    }))

    vi.mock('@mz-codes/design-system', async () => {
      const actual = await vi.importActual('@mz-codes/design-system')
      const useToast = vi.fn().mockReturnValue({ createToast: createToastMock })
      return {
        ...actual,
        useToast,
      }
    })

    vi.mock('hooks', () => {
      const useDebounce = vi.fn().mockImplementation((value) => value)
      return {
        useDebounce,
      }
    })

    const { getByRole } = customRender(
      <MonitoredShareholdersSearchModal
        setSelected={() => {}}
        title="any-title"
        onClose={() => {}}
        visibility
        classification={{ label: '', value: '' }}
      />
    )
    const textbox = getByRole('textbox')
    await userEvent.type(textbox, SHAREHOLDER_NAME)
    expect(createToastMock).toBeCalledWith({
      type: 'error',
      title: 'globals.errors.requestFail.title',
      description: 'globals.errors.requestFail.message',
    })
  })

  it('should call handleSelect on shareholder select', async () => {
    const SHAREHOLDER_NAME = 'a'
    const { getSerializedMonitoredShareholdersMock, createToastMock } = generateMockData()
    vi.mock('hooks/useMonitoredShareholders', () => ({
      getSerializedMonitoredShareholders: getSerializedMonitoredShareholdersMock,
      SerializedMonitoredShareholder: null,
    }))

    vi.mock('hooks', () => {
      const useDebounce = vi.fn().mockImplementation((value) => value)
      const useToast = vi.fn().mockReturnValue({ createToast: createToastMock })
      return {
        useDebounce,
        useToast,
      }
    })

    const onCloseMock = vi.fn()

    const { getByRole } = customRender(
      <MonitoredShareholdersSearchModal
        setSelected={() => {}}
        title="any-title"
        onClose={onCloseMock}
        visibility
        classification={{ label: '', value: '' }}
      />
    )
    const textbox = getByRole('textbox')
    await userEvent.type(textbox, SHAREHOLDER_NAME)
    const shareholderToClick = getByRole('cell', { name: /any-document/i })
    await userEvent.click(shareholderToClick)
    expect(onCloseMock).toBeCalledTimes(1)
  })

  it('should call fetchData on table scroll', async () => {
    const SHAREHOLDER_NAME = 'a'
    const { getSerializedMonitoredShareholdersMock, createToastMock } = generateMockData()
    vi.mock('hooks/useMonitoredShareholders', () => ({
      getSerializedMonitoredShareholders: getSerializedMonitoredShareholdersMock,
      SerializedMonitoredShareholder: null,
    }))

    vi.mock('hooks', () => {
      const useDebounce = vi.fn().mockImplementation((value) => value)
      const useToast = vi.fn().mockReturnValue({ createToast: createToastMock })
      return {
        useDebounce,
        useToast,
      }
    })

    const onCloseMock = vi.fn()

    const { getByRole, getByTestId } = customRender(
      <MonitoredShareholdersSearchModal
        setSelected={() => {}}
        title="any-title"
        onClose={onCloseMock}
        visibility
        classification={{ label: '', value: '' }}
      />
    )
    const textbox = getByRole('textbox')
    await userEvent.type(textbox, SHAREHOLDER_NAME)
    const table = getByTestId('table-body')
    fireEvent.scroll(table, { target: { scrollY: 100 } })

    expect(getSerializedMonitoredShareholdersMock).toBeCalledTimes(2)
  })
})
