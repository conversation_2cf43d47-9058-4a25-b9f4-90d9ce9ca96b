import React, { useEffect, useState, useCallback, useRef } from 'react'
import { i18n } from 'translate'

import { useToast } from '@mz-codes/design-system'

import { useDebounce } from 'hooks'
import { getSerializedMonitoredShareholders, SerializedMonitoredShareholder } from 'hooks/useMonitoredShareholders'
import { DOCUMENT_TYPES } from 'types/shareholders'
import { BaseError } from 'errors'

import { TMonitoredShareholdersSearchModal } from './monitored-shareholders-search-modal.types'
import { MonitoredShareholdersSearchModalTemplate } from './monitored-shareholders-search-modal.template'

export const allMonitoredShareholder: SerializedMonitoredShareholder = {
  contactId: '-1',
  name: i18n.t('all'),
  document: '',
  documentType: DOCUMENT_TYPES.UNKNOW,
  translatedShareholderType: '',
  shareholderImage: '',
}

const limit = 20

export function MonitoredShareholdersSearchModal(props: TMonitoredShareholdersSearchModal) {
  const { visibility, classification, title, message, onClose, setSelected } = props

  const ref = useRef<HTMLInputElement>(null)

  const { createToast } = useToast()

  const [offset, setOffset] = useState(0)
  const [isFetching, setIsFetching] = useState(false)
  const [search, setSearch] = useState('')

  const [shareholders, setShareholders] = useState<SerializedMonitoredShareholder[]>([allMonitoredShareholder])

  const debouncedSearch = useDebounce(search)

  const resetData = useCallback(() => {
    setSearch('')
    setOffset(0)
    setShareholders([allMonitoredShareholder])
  }, [])

  useEffect(() => {
    if (visibility) {
      resetData()
      ref.current?.focus()
    }
  }, [visibility, resetData])

  useEffect(() => {
    const fetchdata = async () => {
      try {
        setIsFetching(true)

        const searchResults = await getSerializedMonitoredShareholders({
          search: debouncedSearch,
          classification: classification.value as string,
          limit,
          offset,
        })

        setShareholders((previousValue) => {
          return [...previousValue, ...searchResults]
        })
      } catch (err: unknown) {
        if (err instanceof BaseError) {
          createToast({
            type: 'error',
            title: err.title,
            description: err.message,
          })
          return
        }
        createToast({
          type: 'error',
          title: i18n.t('globals.errors.requestFail.title'),
          description: i18n.t('globals.errors.requestFail.message'),
        })
      } finally {
        setIsFetching(false)
      }
    }

    if (debouncedSearch) {
      fetchdata()
    }
  }, [debouncedSearch, setShareholders, offset, classification.value, createToast])

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    resetData()
    setSearch(event.target.value)
  }

  const handleSelect = (monitoredShareholder: SerializedMonitoredShareholder) => {
    setSelected(monitoredShareholder)
    onClose()
  }

  const handleScroll = (event: React.UIEvent) => {
    const target = event.target as Element
    const { scrollTop, scrollHeight, clientHeight } = target

    if (scrollTop + clientHeight + 1 >= scrollHeight) {
      setOffset(offset + limit)
    }
  }

  return (
    <MonitoredShareholdersSearchModalTemplate
      visibility={visibility}
      onClose={onClose}
      title={title}
      message={message}
      inputValue={search}
      inputRef={ref}
      inputHandleChange={handleChange}
      handleScroll={handleScroll}
      shareholders={shareholders}
      isFetching={isFetching}
      handleSelect={handleSelect}
    />
  )
}
