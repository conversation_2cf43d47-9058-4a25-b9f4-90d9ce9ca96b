import { customRender } from 'test'
import { describe, it, expect } from 'vitest'
import { ShareholderTypeWrapper } from './monitored-shareholders-search-modal-shareholder-type-wrapper.template'

describe('Shareholder Type Wrapper Template', () => {
  it('should render Template without crashing', async () => {
    const { getByTestId } = customRender(<ShareholderTypeWrapper data-testid="component" />)
    expect(getByTestId('component')).toBeInTheDocument()
  })
})
