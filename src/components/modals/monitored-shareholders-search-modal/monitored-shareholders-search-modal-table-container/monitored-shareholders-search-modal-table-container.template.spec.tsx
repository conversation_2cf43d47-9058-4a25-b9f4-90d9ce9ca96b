import { customRender } from 'test'
import { describe, it, expect } from 'vitest'
import { MonitoredShareholdersSearchModalTableContainer } from './monitored-shareholders-search-modal-table-container.template'

describe('Monitored Shareholders Search Modal Table Container Template', () => {
  it('should render Template without crashing', async () => {
    const { getByTestId } = customRender(<MonitoredShareholdersSearchModalTableContainer data-testid="component" />)
    expect(getByTestId('component')).toBeInTheDocument()
  })

  it('should set height according to prop', async () => {
    const HEIGHT = Math.random() * 10
    const { getByTestId } = customRender(
      <MonitoredShareholdersSearchModalTableContainer height={HEIGHT} data-testid="component" />
    )
    expect(getByTestId('component')).toHaveStyleRule('height', `${HEIGHT}px`)
  })

  it('should set height to 400px if no props is passed', async () => {
    const { getByTestId } = customRender(<MonitoredShareholdersSearchModalTableContainer data-testid="component" />)
    expect(getByTestId('component')).toHaveStyleRule('height', '400px')
  })
})
