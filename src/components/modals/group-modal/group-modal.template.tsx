import { BaseModal, Buttons } from '@mz-codes/design-system'
import { NewOptionDropdown } from 'components/new-option-dropdown'
import { translations } from './group-modal.translations'
import { TGroupModalTemplate } from './group-modal.types'

export function GroupModalTemplate(props: TGroupModalTemplate) {
  const {
    visibility,
    optionsVisibility = false,
    closeMenu,
    loading,
    disableButton,
    title,
    inputValue,
    options,
    selectedValue,
    handleInputChange,
    handleChange,
    handleClose,
    handleConfirm,
    handleCreateGroup,
    ...rest
  } = props

  return (
    <BaseModal data-testid="group-modal-template" show={visibility} onClose={handleClose} width="505px" {...rest}>
      <BaseModal.Header data-testid="group-modal-template-header">
        <BaseModal.Title>{title}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body data-testid="group-modal-template-body">
        <GroupModalTemplate.Dropdown
          data-testid="group-modal-template-dropdown"
          value={selectedValue}
          options={options}
          menuOpen={optionsVisibility}
          closeMenuOnSelect={closeMenu}
          inputValue={inputValue}
          handleInputChange={handleInputChange}
          handleChange={handleChange}
          placeholder={translations.searchGroup}
          loading={loading}
          disabled={loading}
          newOptionLabel={translations.newGroup}
          handleNewOption={handleCreateGroup}
        />
      </BaseModal.Body>
      <BaseModal.Footer data-testid="group-modal-template-footer">
        <BaseModal.ButtonWrapper>
          <Buttons.Cancel data-testid="group-modal-template-cancel-button" onClick={handleClose}>
            {translations.cancel}
          </Buttons.Cancel>
          <Buttons.Primary
            data-testid="group-modal-template-primary-button"
            disabled={disableButton}
            onClick={handleConfirm}
          >
            {translations.groupModalButton}
          </Buttons.Primary>
        </BaseModal.ButtonWrapper>
      </BaseModal.Footer>
    </BaseModal>
  )
}

GroupModalTemplate.Dropdown = NewOptionDropdown
