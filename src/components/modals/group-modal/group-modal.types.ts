import { TOption } from '@mz-codes/design-system'
import { SingleValue } from 'react-select'

export type TGroupModal = {
  title: string
  options?: Array<TOption>
  visibility: boolean
  onClose(): void
  onConfirm(selectedShareholderGroupInfo: TOption): Promise<void>
  onCreateGroup(groupName?: string): Promise<void> | void
}

export type TGroupModalTemplate = {
  visibility: boolean
  optionsVisibility?: boolean
  closeMenu: boolean
  loading: boolean
  disableButton: boolean
  title: string
  inputValue: string
  options?: Array<TOption>
  selectedValue: SingleValue<TOption>
  handleInputChange(newValue: string): void
  handleChange(selectedOption: SingleValue<TOption> | null): void
  handleClose(): void
  handleConfirm(): Promise<void>
  handleCreateGroup(groupName: string): Promise<void>
}
