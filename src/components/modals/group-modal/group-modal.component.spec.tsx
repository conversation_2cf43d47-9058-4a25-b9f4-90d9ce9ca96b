import { customRender } from 'test'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import userEvent from '@testing-library/user-event'
import { GroupModal } from './group-modal.component'

function generateMockData() {
  const onClose = vi.fn()
  const onConfirm = vi.fn().mockImplementation(
    async () =>
      new Promise<void>((resolve) => {
        setTimeout(resolve, 500)
      })
  )
  const onCreateGroup = vi.fn().mockImplementation(
    async () =>
      new Promise<void>((resolve) => {
        setTimeout(resolve, 500)
      })
  )

  const options = [
    { value: 'test-1', label: 'Option 1' },
    { value: 'test-2', label: 'Option 2' },
  ]

  const mockProps = {
    title: 'Test Modal',
    visibility: true,
    options,
    onClose,
    onConfirm,
    onCreateGroup,
  }

  return { mockProps, options }
}

describe('New Group Modal', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn().mockImplementation((key: string) => key),
      },
    }))

    vi.mock('hooks', () => {
      return {
        useOutsideClick: vi.fn(),
      }
    })
  })

  it('should be able to render New Group Modal', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<GroupModal {...mockProps} />)

    expect(getByTestId('new-group-modal-component')).toBeInTheDocument()
  })

  it('should be able to select an option', async () => {
    const { mockProps, options } = generateMockData()

    const { queryByText, getByRole, getByText } = customRender(<GroupModal {...mockProps} />)

    await userEvent.type(getByRole('combobox'), 'option')
    expect(getByText(options[0].label)).toBeInTheDocument()
    expect(getByText(options[1].label)).toBeInTheDocument()

    await userEvent.click(getByText(options[0].label))

    expect(getByText(options[0].label)).toBeInTheDocument()
    expect(queryByText(options[1].label)).not.toBeInTheDocument()
  })

  it('should be able to close a modal when user click on cancel button', async () => {
    const { mockProps } = generateMockData()

    const { getByRole } = customRender(<GroupModal {...mockProps} />)

    await userEvent.click(getByRole('button', { name: 'globals.cancel' }))

    expect(mockProps.onClose).toBeCalledTimes(1)
  })

  it('should be able to close a modal when user click on close button', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<GroupModal {...mockProps} />)

    await userEvent.click(getByTestId('close-button'))

    expect(mockProps.onClose).toBeCalledTimes(1)
  })

  it('should be able to group a shareholder', async () => {
    const { mockProps, options } = generateMockData()

    const { getByRole, getByText } = customRender(<GroupModal {...mockProps} />)

    await userEvent.type(getByRole('combobox'), 'option')
    await userEvent.click(getByText(options[0].label))

    expect(getByText(options[0].label)).toBeInTheDocument()
    expect(getByRole('button', { name: 'pageSmartGrouping.groupModalButton' })).toBeEnabled()

    await userEvent.click(getByRole('button', { name: 'pageSmartGrouping.groupModalButton' }))
    expect(mockProps.onConfirm).toBeCalledTimes(1)
  })

  it('should not be able to group a shareholder without select a group', async () => {
    const { mockProps } = generateMockData()

    const { getByRole } = customRender(<GroupModal {...mockProps} />)

    await userEvent.click(getByRole('button', { name: 'pageSmartGrouping.groupModalButton' }))

    expect(mockProps.onConfirm).not.toBeCalled()
  })

  it('should be able to enable the confirmation button when select group a shareholder', async () => {
    const { mockProps, options } = generateMockData()

    const { getByRole, getByText } = customRender(<GroupModal {...mockProps} />)

    await userEvent.type(getByRole('combobox'), 'option')
    await userEvent.click(getByText(options[0].label))

    expect(getByRole('button', { name: 'pageSmartGrouping.groupModalButton' })).toBeEnabled()
  })

  it('should be able to create a new group', async () => {
    const { mockProps } = generateMockData()

    const { getByRole } = customRender(<GroupModal {...mockProps} />)

    await userEvent.type(getByRole('combobox'), 'new-option')
    const createGroupButton = getByRole('button', { name: /new-option pagesmartgrouping\.newgroup \+/i })

    expect(createGroupButton).toBeInTheDocument()
    await userEvent.click(createGroupButton)

    expect(mockProps.onCreateGroup).toBeCalledTimes(1)
    expect(mockProps.onCreateGroup).toBeCalledWith('new-option')
  })
})
