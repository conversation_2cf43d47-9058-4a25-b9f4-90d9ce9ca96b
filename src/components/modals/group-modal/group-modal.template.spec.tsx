import { customRender } from 'test'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { TOption } from '@mz-codes/design-system'
import { GroupModalTemplate } from './group-modal.template'

function generateMockData() {
  const handleInputChange = vi.fn()
  const handleChange = vi.fn()
  const handleClose = vi.fn()
  const handleConfirm = vi.fn()
  const handleCreateGroup = vi.fn()

  const options: TOption[] = [
    { value: 'teste-1', label: 'option 1' },
    { value: 'teste-2', label: 'option 2' },
  ]

  const mockProps = {
    visibility: true,
    closeMenu: true,
    loading: false,
    disableButton: false,
    title: 'Test Modal',
    inputValue: '',
    options,
    selectedValue: options[0],
    handleInputChange,
    handleChange,
    handleClose,
    handleConfirm,
    handleCreateGroup,
  }

  return { mockProps }
}

describe('Group Modal Template', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn((key: string) => key),
      },
    }))

    vi.mock('hooks', () => {
      return {
        useOutsideClick: vi.fn(),
      }
    })
  })

  it('should be able to render Group Modal Template', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<GroupModalTemplate {...mockProps} />)

    expect(getByTestId('group-modal-template')).toBeInTheDocument()
  })

  it('should be able to render Group Modal Template header with correct title', () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByText } = customRender(<GroupModalTemplate {...mockProps} />)

    const header = getByTestId('group-modal-template-header')
    const title = getByText(mockProps.title)

    expect(header).toBeInTheDocument()
    expect(title).toBeInTheDocument()
    expect(header.querySelector('h3')?.textContent).toBe(mockProps.title)
  })

  it('should be able to render Group Modal Template body', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<GroupModalTemplate {...mockProps} />)

    const body = getByTestId('group-modal-template-body')
    const dropdown = getByTestId('group-modal-template-dropdown')

    expect(body).toBeInTheDocument()
    expect(dropdown).toBeInTheDocument()
  })

  it('should be able to render Group Modal Template footer with correct buttons', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<GroupModalTemplate {...mockProps} />)

    const footer = getByTestId('group-modal-template-footer')
    const cancelButton = getByTestId('group-modal-template-cancel-button')
    const primaryButton = getByTestId('group-modal-template-primary-button')

    expect(footer).toBeInTheDocument()
    expect(cancelButton).toBeInTheDocument()
    expect(primaryButton).toBeInTheDocument()
  })
})
