import { TOption } from '@mz-codes/design-system'
import { useEffect, useState } from 'react'
import { SingleValue } from 'react-select'
import { TGroupModal } from './group-modal.types'
import { GroupModalTemplate } from './group-modal.template'

export function GroupModal(props: TGroupModal) {
  const [selectedValue, setSelectedValue] = useState<SingleValue<TOption> | null>(null)
  const [inputValue, setInputValue] = useState<string>('')
  const [disableButton, setDisableButton] = useState<boolean>(true)
  const [loading, setLoading] = useState<boolean>(false)
  const [closeMenu, setCloseMenu] = useState(false)

  const { title, visibility, options, onClose, onConfirm, onCreateGroup } = props

  const handleInputChange = (value: string) => {
    setInputValue(value)
  }

  const handleChange = (selectedOption: SingleValue<TOption> | null) => {
    setCloseMenu(true)
    setSelectedValue(selectedOption)
    setDisableButton(!selectedOption)
  }

  const handleClose = () => {
    onClose()
  }

  const handleConfirm = async () => {
    if (!selectedValue) {
      return
    }

    setLoading(true)

    const result = onConfirm(selectedValue)

    if (result instanceof Promise) {
      await result
    }

    handleClose()
  }

  const handleCreateGroup = async (groupName: string) => {
    setDisableButton(true)
    setLoading(true)

    const result = onCreateGroup(groupName)
    if (result instanceof Promise) {
      await result
    }

    handleClose()
  }

  useEffect(() => {
    setSelectedValue(null)
    setInputValue('')
    setLoading(false)
    setDisableButton(true)
  }, [visibility])

  return (
    <GroupModalTemplate
      data-testid="new-group-modal-component"
      visibility={visibility}
      closeMenu={closeMenu}
      loading={loading}
      disableButton={disableButton}
      title={title}
      inputValue={inputValue}
      options={options}
      selectedValue={selectedValue}
      handleInputChange={handleInputChange}
      handleChange={handleChange}
      handleClose={handleClose}
      handleConfirm={() => handleConfirm()}
      handleCreateGroup={handleCreateGroup}
    />
  )
}
