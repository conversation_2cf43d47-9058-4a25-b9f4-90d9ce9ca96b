import { userEvent } from '@testing-library/user-event'
import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import { EditModal } from './edit-modal.component'

function generateMockData() {
  const onClose = vi.fn()
  const onConfirm = vi.fn()

  const mockProps = {
    show: true,
    disabled: true,
    title: 'Test Modal',
    label: 'Label Modal',
    value: '',
    onClose,
    onConfirm,
  }

  return { mockProps }
}

describe('Edit Modal', () => {
  it('should be able to click confirm button passing input value', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByRole } = customRender(<EditModal {...mockProps} />)

    const confirmButton = getByTestId('edit-modal-primary-button')
    await userEvent.type(getByRole('textbox'), 'new-value')
    expect(getByRole('textbox')).toHaveValue('new-value')

    expect(confirmButton).toBeEnabled()

    await userEvent.click(confirmButton)

    expect(mockProps.onConfirm).toBeCalledTimes(1)
    expect(mockProps.onConfirm).toBeCalledWith('new-value')
  })

  it("should disable confirm button if input doesn't have value", async () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<EditModal {...mockProps} />)
    const confirmButton = getByTestId('edit-modal-primary-button')

    expect(confirmButton).toBeDisabled()

    await userEvent.click(confirmButton)

    expect(mockProps.onConfirm).toBeCalledTimes(0)
  })

  it('should be able to close a modal when user click on cancel button', async () => {
    const { mockProps } = generateMockData()

    const { getByRole } = customRender(<EditModal {...mockProps} />)

    await userEvent.click(getByRole('button', { name: 'Cancel' }))

    expect(mockProps.onClose).toBeCalledTimes(1)
  })

  it('should be able to close a modal when user click on close button', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<EditModal {...mockProps} />)

    await userEvent.click(getByTestId('close-button'))

    expect(mockProps.onClose).toBeCalledTimes(1)
  })
})
