export type TEditModal = {
  show: boolean
  title: string
  label: string
  placeholder?: string
  inputName?: string
  width?: string
  onClose(): void
  onConfirm(description: string): void
  value: string
}

export type TEditModalTemplate = {
  show: boolean
  disabled: boolean
  title: string
  label: string
  placeholder?: string
  inputName?: string
  width?: string
  onClose(): void
  onConfirm(): void
  value: string
  onChange(event: React.ChangeEvent<HTMLInputElement>): void
}
