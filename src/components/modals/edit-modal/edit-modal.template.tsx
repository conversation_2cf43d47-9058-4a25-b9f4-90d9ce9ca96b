import { BaseModal, Buttons, Inputs } from '@mz-codes/design-system'

import { i18n } from 'translate'

import { TEditModalTemplate } from './edit-modal.types'

export function EditModalTemplate(props: TEditModalTemplate) {
  const {
    show,
    disabled,
    title,
    label,
    placeholder,
    inputName,
    onClose,
    onConfirm,
    onChange,
    value,
    width = '600px',
  } = props
  return (
    <BaseModal show={show} onClose={onClose} width={width} data-testid="edit-modal">
      <BaseModal.Header data-testid="edit-modal-header">
        <BaseModal.Title>{title}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body data-testid="group-modal-body">
        <Inputs.Label>
          <Inputs.Text>{label}</Inputs.Text>
          <Inputs.BaseLined
            data-testid="edit-modal-input"
            name={inputName ?? 'edit-modal-name-input'}
            value={value}
            placeholder={placeholder}
            onChange={onChange}
          />
        </Inputs.Label>
      </BaseModal.Body>
      <BaseModal.Footer data-testid="edit-modal-footer">
        <BaseModal.ButtonWrapper>
          <Buttons.Cancel data-testid="edit-modal-cancel-button" onClick={onClose}>
            {i18n.t('globals.cancel')}
          </Buttons.Cancel>
          <Buttons.Primary data-testid="edit-modal-primary-button" onClick={onConfirm} disabled={disabled}>
            {i18n.t('save')}
          </Buttons.Primary>
        </BaseModal.ButtonWrapper>
      </BaseModal.Footer>
    </BaseModal>
  )
}
