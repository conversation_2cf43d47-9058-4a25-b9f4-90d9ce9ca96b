import { useEffect, useState } from 'react'
import { EditModalTemplate } from './edit-modal.template'
import { TEditModal } from './edit-modal.types'

export function EditModal(props: TEditModal) {
  const { show, title, label, placeholder, inputName, width, onClose, onConfirm } = props

  const [value, setValue] = useState('')

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value)
  }

  const handleConfirm = () => {
    return onConfirm(value)
  }

  const buttonDisabled = !value

  useEffect(() => {
    setValue('')
  }, [show])

  return (
    <EditModalTemplate
      show={show}
      disabled={buttonDisabled}
      title={title}
      label={label}
      placeholder={placeholder}
      inputName={inputName}
      width={width}
      onClose={onClose}
      onConfirm={handleConfirm}
      onChange={handleChange}
      value={value}
    />
  )
}
