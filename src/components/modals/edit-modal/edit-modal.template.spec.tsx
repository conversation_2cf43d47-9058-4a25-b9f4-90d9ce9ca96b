import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import { EditModalTemplate } from './edit-modal.template'

function generateMockData() {
  const onClose = vi.fn()
  const onConfirm = vi.fn()
  const onChange = vi.fn()

  const mockProps = {
    show: true,
    disabled: true,
    title: 'Test Modal',
    label: 'Label Modal',
    value: 'Input Value',
    onClose,
    onConfirm,
    onChange,
  }

  return { mockProps }
}

describe('Group Modal Template', () => {
  it('should be able to render Edit Modal', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<EditModalTemplate {...mockProps} />)

    expect(getByTestId('edit-modal')).toBeInTheDocument()
  })

  it('should be able to render Edit Modal header with correct title', () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByText } = customRender(<EditModalTemplate {...mockProps} />)

    const header = getByTestId('edit-modal-header')
    const title = getByText(mockProps.title)

    expect(header).toBeInTheDocument()
    expect(title).toBeInTheDocument()
    expect(header.querySelector('h3')?.textContent).toBe(mockProps.title)
  })

  it('should be able to render Edit Modal body', () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByDisplayValue } = customRender(<EditModalTemplate {...mockProps} />)

    const body = getByTestId('group-modal-body')
    const input = getByDisplayValue(mockProps.value)

    expect(body).toBeInTheDocument()
    expect(input).toBeInTheDocument()
  })

  it('should be able to render Edit Modal footer with correct buttons', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<EditModalTemplate {...mockProps} />)

    const footer = getByTestId('edit-modal-footer')
    const cancelButton = getByTestId('edit-modal-cancel-button')
    const primaryButton = getByTestId('edit-modal-primary-button')

    expect(footer).toBeInTheDocument()
    expect(cancelButton).toBeInTheDocument()
    expect(primaryButton).toBeInTheDocument()
  })
})
