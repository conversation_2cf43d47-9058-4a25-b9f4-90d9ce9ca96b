import { useEffect, useState } from 'react'
import { DOCUMENT_TYPES, DocumentType } from 'types/shareholders'
import { formatDocumentInputMask } from 'utils/formatDocument'
import { NewShareholderModalTemplate } from './new-shareholder-modal.template'
import { TNewShareholderModal } from './new-shareholder-modal.types'

export function NewShareholderModal(props: TNewShareholderModal) {
  const [selectedDocument, setSelectedDocument] = useState<DocumentType>(DOCUMENT_TYPES.FUND)
  const [nameInput, setNameInput] = useState<string>('')
  const [documentInput, setDocumentInput] = useState<string>('')
  const { title, show, onClose, onConfirm } = props

  const handleNameInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNameInput(event.target.value)
  }

  const handleDocumentInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDocumentInput(formatDocumentInputMask(event.target.value, selectedDocument))
  }

  const handleOptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedValue: DocumentType = Number(event.target.value) as DocumentType
    setSelectedDocument(selectedValue)
    setDocumentInput('')
  }

  const handleConfirm = async () => {
    const formattedDocument = documentInput.replace(/\D/g, '')
    await onConfirm(nameInput, formattedDocument, selectedDocument)
    onClose()
  }

  const validateDocumentFormat = (documentType: DocumentType) => {
    const documentTypes = {
      [DOCUMENT_TYPES.INDIVIDUAL]: documentInput.length < 11,
      [DOCUMENT_TYPES.FUND]: documentInput.length < 14,
      [DOCUMENT_TYPES.UNKNOW]: null,
    }
    return documentTypes[documentType] as boolean
  }

  useEffect(() => {
    setNameInput('')
    setDocumentInput('')
    setSelectedDocument(DOCUMENT_TYPES.FUND)
  }, [show])

  const isPrimaryButtonDisabled = nameInput.length < 3 || validateDocumentFormat(selectedDocument)

  return (
    <NewShareholderModalTemplate
      show={show}
      handleOnClose={onClose}
      title={title}
      nameInput={nameInput}
      handleNameInputChange={handleNameInputChange}
      handleOptionChange={handleOptionChange}
      selectedDocument={selectedDocument}
      documentInput={documentInput}
      handleDocumentInputChange={handleDocumentInputChange}
      isPrimaryButtonDisabled={isPrimaryButtonDisabled}
      handleConfirm={handleConfirm}
    />
  )
}
