import { TOption } from '@mz-codes/design-system'
import { DocumentType } from 'types/shareholders'

export type TNewShareholderModal = {
  title: string
  options?: Array<TOption>
  show: boolean
  onClose(): void
  onConfirm(name: string, document: string, documentType: DocumentType): Promise<void>
}

export type TNewShareholderModalTemplate = {
  show: boolean
  handleOnClose(): void
  title: string
  nameInput: string
  handleNameInputChange(event: React.ChangeEvent<HTMLInputElement>): void
  handleOptionChange(event: React.ChangeEvent<HTMLInputElement>): void
  selectedDocument: DocumentType
  documentInput: string
  handleDocumentInputChange(event: React.ChangeEvent<HTMLInputElement>): void
  isPrimaryButtonDisabled: boolean
  handleConfirm(): Promise<void>
}
