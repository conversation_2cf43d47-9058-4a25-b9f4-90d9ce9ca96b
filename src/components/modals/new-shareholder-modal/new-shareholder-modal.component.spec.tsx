import { userEvent } from '@testing-library/user-event'
import { DOCUMENT_TYPES } from 'types/shareholders'
import { customRender } from 'test'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { NewShareholderModal } from './new-shareholder-modal.component'

function generateMockData() {
  const onClose = vi.fn()
  const onConfirm = vi.fn()

  const mockProps = {
    show: true,
    title: 'Test Modal',
    onClose,
    onConfirm,
  }

  return { mockProps }
}

describe('New Shareholder Modal', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn().mockImplementation((key: string) => key),
      },
    }))

    vi.mock('hooks', () => {
      return {
        useOutsideClick: vi.fn(),
      }
    })
  })

  it('should be able to render New Group Modal', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<NewShareholderModal {...mockProps} />)

    expect(getByTestId('new-shareholder-modal')).toBeInTheDocument()
  })

  it('disables primary button when input is invalid', async () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<NewShareholderModal {...mockProps} />)
    const primaryButton = getByTestId('new-shareholder-modal-footer-primary-button')

    expect(primaryButton).toBeDisabled()

    const nameInput = getByTestId('new-shareholder-modal-name-input')
    await userEvent.type(nameInput, 'New Name')

    const documentInput = getByTestId('new-shareholder-modal-document-input')
    await userEvent.type(documentInput, '44444444444444')

    expect(primaryButton).not.toBeDisabled()
  })

  it('should call onConfirm and onClose when primary button is clicked', async () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<NewShareholderModal {...mockProps} />)
    const primaryButton = getByTestId('new-shareholder-modal-footer-primary-button')

    const nameInput = getByTestId('new-shareholder-modal-name-input')
    await userEvent.type(nameInput, 'new-name')

    const documentInput = getByTestId('new-shareholder-modal-document-input')
    await userEvent.type(documentInput, '44444444444444')

    await userEvent.click(primaryButton)

    expect(mockProps.onConfirm).toBeCalledWith('new-name', '44444444444444', DOCUMENT_TYPES.FUND)
    expect(mockProps.onClose).toBeCalledTimes(1)
  })

  it('should handle document radios and clear document input when change', async () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<NewShareholderModal {...mockProps} />)

    const documentInput = getByTestId('new-shareholder-modal-document-input')
    await userEvent.type(documentInput, '44444444444444')

    const individualRadio = getByTestId('new-shareholder-modal-individual-radio')
    await userEvent.click(individualRadio)

    expect(individualRadio).toBeChecked()
    expect(getByTestId('new-shareholder-modal-fund-radio')).not.toBeChecked()
    expect(getByTestId('new-shareholder-modal-document-input')).toHaveValue('')

    const fundRadio = getByTestId('new-shareholder-modal-fund-radio')
    await userEvent.click(fundRadio)

    expect(fundRadio).toBeChecked()
    expect(getByTestId('new-shareholder-modal-individual-radio')).not.toBeChecked()
    expect(getByTestId('new-shareholder-modal-document-input')).toHaveValue('')
  })
})
