import { userEvent } from '@testing-library/user-event'
import { DOCUMENT_TYPES } from 'types/shareholders'
import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import { NewShareholderModalTemplate } from './new-shareholder-modal.template'

function generateMockData() {
  const handleDocumentInputChange = vi.fn()
  const handleOptionChange = vi.fn()
  const handleOnClose = vi.fn()
  const handleConfirm = vi.fn()
  const handleNameInputChange = vi.fn()

  const mockProps = {
    show: true,
    title: 'Test Modal',
    nameInput: 'Name',
    documentInput: '456.456.456-48',
    selectedDocument: DOCUMENT_TYPES.INDIVIDUAL,
    isPrimaryButtonDisabled: false,
    handleOnClose,
    handleNameInputChange,
    handleOptionChange,
    handleDocumentInputChange,
    handleConfirm,
  }

  return { mockProps }
}

describe('New Shareholder Modal Template', () => {
  it('should be able render New Shareholder Modal Template', () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<NewShareholderModalTemplate {...mockProps} />)

    expect(getByTestId('new-shareholder-modal')).toBeInTheDocument()
  })

  it('should be able render New Shareholder Modal Template Header', () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<NewShareholderModalTemplate {...mockProps} />)
    const header = getByTestId('new-shareholder-modal-header')
    const title = getByTestId('new-shareholder-modal-header-title')
    expect(header).toBeInTheDocument()
    expect(title).toHaveTextContent('Test Modal')
  })

  it('should be able render New Shareholder Modal Template Body', () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<NewShareholderModalTemplate {...mockProps} />)
    const body = getByTestId('new-shareholder-modal-body')
    const nameInput = getByTestId('new-shareholder-modal-name-input')
    const documentInput = getByTestId('new-shareholder-modal-document-input')
    const fundRadio = getByTestId('new-shareholder-modal-fund-radio')
    const individualRadio = getByTestId('new-shareholder-modal-individual-radio')

    expect(body).toBeInTheDocument()

    expect(nameInput).toHaveValue(mockProps.nameInput)
    expect(documentInput).toHaveValue(mockProps.documentInput)

    expect(fundRadio).not.toBeChecked()
    expect(individualRadio).toBeChecked()
  })

  it('should be able to render New Shareholder Modal Footer', async () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<NewShareholderModalTemplate {...mockProps} />)
    const footer = getByTestId('new-shareholder-modal-footer')
    const button = getByTestId('new-shareholder-modal-footer-primary-button')

    expect(footer).toBeInTheDocument()
    expect(button).toBeEnabled()
    await userEvent.click(button)

    expect(mockProps.handleConfirm).toBeCalledTimes(1)
  })
})
