import { Buttons, BaseModal, Inputs } from '@mz-codes/design-system'
import { DOCUMENT_TYPES } from 'types/shareholders'
import { i18n } from 'translate'
import { TNewShareholderModalTemplate } from './new-shareholder-modal.types'

export function NewShareholderModalTemplate(props: TNewShareholderModalTemplate) {
  const {
    show,
    handleOnClose,
    title,
    nameInput,
    handleNameInputChange,
    handleOptionChange,
    selectedDocument,
    documentInput,
    handleDocumentInputChange,
    isPrimaryButtonDisabled,
    handleConfirm,
  } = props
  return (
    <BaseModal show={show} onClose={handleOnClose} width="500px" data-testid="new-shareholder-modal">
      <BaseModal.Header data-testid="new-shareholder-modal-header">
        <BaseModal.Title data-testid="new-shareholder-modal-header-title">{title}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body data-testid="new-shareholder-modal-body">
        <Inputs.Label>
          <Inputs.Text>{i18n.t('name')}</Inputs.Text>
          <Inputs.BaseLined
            name="name"
            value={nameInput}
            onChange={handleNameInputChange}
            data-testid="new-shareholder-modal-name-input"
          />
        </Inputs.Label>
        <Inputs.Group style={{ marginTop: '15px', gap: '6px' }}>
          <Inputs.Label $horizontalAlignment="center">
            <Inputs.Label $horizontalAlignment="center">
              <Inputs.Radio
                name="cnpj"
                handleChange={handleOptionChange}
                value={DOCUMENT_TYPES.FUND.toString()}
                checked={selectedDocument === DOCUMENT_TYPES.FUND}
                data-testid="new-shareholder-modal-fund-radio"
              />
              <Inputs.Text>{i18n.t('cnpj')}</Inputs.Text>
            </Inputs.Label>
            <Inputs.Label $horizontalAlignment="center">
              <Inputs.Radio
                name="cpf"
                handleChange={handleOptionChange}
                value={DOCUMENT_TYPES.INDIVIDUAL.toString()}
                checked={selectedDocument === DOCUMENT_TYPES.INDIVIDUAL}
                data-testid="new-shareholder-modal-individual-radio"
              />
              <Inputs.Text>{i18n.t('cpf')}</Inputs.Text>
            </Inputs.Label>
          </Inputs.Label>
          <Inputs.Label>
            <Inputs.BaseLined
              name="document"
              value={documentInput}
              onChange={handleDocumentInputChange}
              data-testid="new-shareholder-modal-document-input"
            />
          </Inputs.Label>
        </Inputs.Group>
      </BaseModal.Body>
      <BaseModal.Footer data-testid="new-shareholder-modal-footer">
        <BaseModal.ButtonWrapper>
          <Buttons.Cancel data-testid="new-shareholder-modal-footer-cancel-button" onClick={handleOnClose}>
            {i18n.t('globals.cancel')}
          </Buttons.Cancel>
          <Buttons.Primary
            data-testid="new-shareholder-modal-footer-primary-button"
            disabled={isPrimaryButtonDisabled}
            onClick={handleConfirm}
          >
            {i18n.t('save')}
          </Buttons.Primary>
        </BaseModal.ButtonWrapper>
      </BaseModal.Footer>
    </BaseModal>
  )
}
