import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { UploadModalProgressBar } from './upload-modal-dropzone-progress-bar.template'

describe('Upload Modal Dropzone Input', () => {
  it('should be able to render correctly', () => {
    const { getByTestId } = customRender(
      <UploadModalProgressBar progress={0} data-testid="upload-modal-dropzone-progress-bar" />
    )

    expect(getByTestId('upload-modal-dropzone-progress-bar')).toBeInTheDocument()
  })

  it('should be able apply correct width depending on progress', () => {
    const { getByTestId } = customRender(
      <UploadModalProgressBar progress={80} data-testid="upload-modal-dropzone-progress-bar" />
    )
    const progressBar = getByTestId('upload-modal-dropzone-progress-bar')
    expect(progressBar).toBeInTheDocument()
    expect(progressBar).toHaveStyleRule('width', '80%')
  })
})
