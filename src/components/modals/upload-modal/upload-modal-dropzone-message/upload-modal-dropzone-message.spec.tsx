import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { UploadModalDropzoneMessage } from './upload-modal-dropzone-message.template'

describe('Upload Modal Dropzone Input', () => {
  it('should be able to render correctly', () => {
    const { getByTestId } = customRender(<UploadModalDropzoneMessage data-testid="upload-modal-dropzone-message" />)

    expect(getByTestId('upload-modal-dropzone-message')).toBeInTheDocument()
  })
})
