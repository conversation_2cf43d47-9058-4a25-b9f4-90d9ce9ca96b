import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { UploadModalDropzoneImage } from './upload-modal-dropzone-image.template'

describe('Upload Modal Dropzone Image', () => {
  it('should be able to render correctly', () => {
    const { getByTestId } = customRender(<UploadModalDropzoneImage data-testid="upload-modal-dropzone-image" />)
    expect(getByTestId('upload-modal-dropzone-image')).toBeInTheDocument()
  })
})
