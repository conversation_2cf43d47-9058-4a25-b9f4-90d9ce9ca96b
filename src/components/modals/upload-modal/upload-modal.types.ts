export type TUploadModal = {
  show: boolean
  onClose(): void
  onConfirm(): void
  title: string
  message?: string
  width?: string
  selectedFile?: File
  onSelectedFile(file: File): void
  progress?: number
  acceptedFiles: string
  confirmButtonLabel?: string
  templateLabel?: string
  onTemplateClick?(): void
  children?: React.ReactNode
}

export type TUploadModalTemplate = {
  show: boolean
  closeModal(): void
  onConfirm(): void
  title: string
  message?: string
  width?: string
  selectedFile?: File
  acceptedFiles: string
  onDrop(accepted: File[]): void
  progress?: number
  confirmButtonLabel?: string
  templateLabel?: string
  onTemplateClick?(): void
  children?: React.ReactNode
}
