import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { UploadModalProgressBarWrapper } from './upload-modal-dropzone-progress-bar-wrapper.template'

describe('Upload Modal Dropzone Input', () => {
  it('should be able to render correctly', () => {
    const { getByTestId } = customRender(
      <UploadModalProgressBarWrapper data-testid="upload-modal-dropzone-progress-bar-wrapper" />
    )

    expect(getByTestId('upload-modal-dropzone-progress-bar-wrapper')).toBeInTheDocument()
  })
})
