import { userEvent } from '@testing-library/user-event'
import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import { UploadModal } from './upload-modal.component'

function generateMockData() {
  const onClose = vi.fn()
  const onConfirm = vi.fn()
  const onSelectedFile = vi.fn()
  const onTemplateClick = vi.fn()
  const mockProps = {
    show: false,
    onClose,
    onConfirm,
    onTemplateClick,
    onSelectedFile,
    title: 'Upload Modal',
    message: 'Please upload a file',
    selectedFile: undefined,
    progress: 0,
    acceptedFiles: '.png',
    confirmButtonLabel: 'Confirm',
    templateLabel: 'Template',
  }
  return { mockProps }
}

describe('Upload Modal Component', () => {
  it('should render correctly', () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<UploadModal {...mockProps} show />)

    expect(getByTestId('upload-modal')).toBeInTheDocument()
  })

  it('should not render if show is false', () => {
    const { mockProps } = generateMockData()
    const { queryByTestId } = customRender(<UploadModal {...mockProps} />)

    expect(queryByTestId('upload-modal')).not.toBeInTheDocument()
  })

  it('should not render if show is false', () => {
    const { mockProps } = generateMockData()
    const { queryByTestId } = customRender(<UploadModal {...mockProps} />)

    expect(queryByTestId('upload-modal')).not.toBeInTheDocument()
  })

  it('should call onClose when closeModal is triggered', async () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<UploadModal {...mockProps} show />)

    await userEvent.click(getByTestId('close-button'))
    expect(mockProps.onClose).toHaveBeenCalled()
  })

  it('should call onSelectedFile when a file is dropped', async () => {
    const { mockProps } = generateMockData()
    const { getByRole } = customRender(<UploadModal {...mockProps} show />)

    const file = new File(['test-file'], 'test-file.png', { type: 'image/png' })

    const dropzone = getByRole('button', { name: /drag or click here/i })

    const input = dropzone.querySelector('input[type="file"]') as HTMLInputElement

    Object.defineProperty(input, 'style', { value: { display: 'block' }, writable: true })
    await userEvent.upload(input, file)

    if (input.files) {
      expect(input.files[0]).toStrictEqual(file)
      expect(mockProps.onSelectedFile).toHaveBeenCalledWith(file)
    } else {
      throw new Error('Input files is null')
    }
  })

  it('should not call onSelectedFile when file is not accepted', async () => {
    const { mockProps } = generateMockData()
    const { getByRole } = customRender(<UploadModal {...mockProps} show />)

    const dropzone = getByRole('button', { name: /drag or click here/i })
    const file = new File(['test-file'], 'test-file.txt', { type: 'plain/text' })
    const input = dropzone.querySelector('input[type="file"]') as HTMLInputElement
    Object.defineProperty(input, 'style', { value: { display: 'block' }, writable: true })

    await userEvent.upload(input, file)

    expect(mockProps.onSelectedFile).not.toHaveBeenCalled()
  })

  it('should not call onSelectedFile when has no file', async () => {
    const { mockProps } = generateMockData()
    const { getByRole } = customRender(<UploadModal {...mockProps} show />)

    const dropzone = getByRole('button', { name: /drag or click here/i })
    const input = dropzone.querySelector('input[type="file"]') as HTMLInputElement
    Object.defineProperty(input, 'style', { value: { display: 'block' }, writable: true })

    await userEvent.upload(input, [])
    expect(mockProps.onSelectedFile).not.toHaveBeenCalled()
  })
})
