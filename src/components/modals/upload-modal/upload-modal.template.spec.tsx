import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import { UploadModalTemplate } from './upload-modal.template'
import { translations } from './upload-modal.translations'

function generateMockData() {
  const closeModal = vi.fn()
  const onConfirm = vi.fn()
  const onDrop = vi.fn()
  const onTemplateClick = vi.fn()

  const mockProps = {
    show: true,
    closeModal,
    onConfirm,
    title: 'Upload Modal Template',
    message: 'please upload a file',
    width: '520px',
    acceptedFiles: '.png, .jpg',
    onDrop,
    progress: 0,
    onTemplateClick,
  }

  return { mockProps }
}

describe('Upload Modal Template', () => {
  it('should be able to render Upload Modal Template', () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<UploadModalTemplate {...mockProps} data-testid="upload-modal-template" />)

    expect(getByTestId('upload-modal-template')).toBeInTheDocument()
  })

  it('should render Upload Modal Template Header correctly', () => {
    const { mockProps } = generateMockData()
    const { getByText, getByTestId } = customRender(<UploadModalTemplate {...mockProps} />)

    expect(getByTestId('upload-modal-header')).toBeInTheDocument()
    expect(getByText(mockProps.title)).toBeInTheDocument()
  })

  it('should render Upload Modal Template Body correctly', () => {
    const { mockProps } = generateMockData()
    const { getByText, getByTestId } = customRender(<UploadModalTemplate {...mockProps} />)

    expect(getByTestId('upload-modal-body')).toBeInTheDocument()
    expect(getByText(mockProps.message)).toBeInTheDocument()
  })

  it('should render Upload Modal Template Dropzone correctly', () => {
    const { mockProps } = generateMockData()
    const { getByText, getByTestId, queryByTestId } = customRender(<UploadModalTemplate {...mockProps} />)

    expect(getByTestId('upload-modal-dropzone')).toBeInTheDocument()
    expect(getByText(translations.dragFileMessage)).toBeInTheDocument()
    expect(getByText(`${translations.acceptedFiles} ${mockProps.acceptedFiles}`)).toBeInTheDocument()
    expect(queryByTestId('upload-modal-progress-bar')).not.toBeInTheDocument()
  })

  it('should render correct content when has selectedFile', () => {
    const { mockProps } = generateMockData()
    const file = new File(['test-file'], 'test-file.png', { type: 'image/png' })

    const { getByText } = customRender(<UploadModalTemplate {...mockProps} selectedFile={file} />)

    expect(getByText(file.name)).toBeInTheDocument()
    expect(getByText(translations.confirmButtonLabel)).toBeEnabled()
  })

  it('should render progress bar when has progress prop', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<UploadModalTemplate {...mockProps} progress={100} />)
    const progressBar = getByTestId('upload-modal-progress-bar')

    expect(progressBar).toBeInTheDocument()
    expect(progressBar).toHaveStyleRule('width', '100%')
  })

  it('should render Upload Modal Footer correctly', () => {
    const { mockProps } = generateMockData()

    const { getByText, getByTestId, queryByTestId } = customRender(<UploadModalTemplate {...mockProps} />)
    expect(getByTestId('upload-modal-footer')).toBeInTheDocument()
    expect(getByText(translations.confirmButtonLabel)).toBeInTheDocument()
    expect(getByText(translations.confirmButtonLabel)).toBeDisabled()
    expect(queryByTestId('upload-modal-template-button')).not.toBeInTheDocument()
  })

  it('should render download template field if has templateLabel and onTemplateClick prop', () => {
    const { mockProps } = generateMockData()
    const templateLabel = 'Download template'

    const { getByText } = customRender(<UploadModalTemplate {...mockProps} templateLabel={templateLabel} />)

    expect(getByText(templateLabel)).toBeInTheDocument()
  })

  it('should custom confirm button label if has confirmButtonLabel prop', () => {
    const { mockProps } = generateMockData()
    const customLabel = 'Custom Label'
    const { getByText, queryByText } = customRender(
      <UploadModalTemplate {...mockProps} confirmButtonLabel={customLabel} />
    )
    expect(getByText(customLabel)).toBeInTheDocument()
    expect(queryByText(translations.confirmButtonLabel)).not.toBeInTheDocument()
  })
})
