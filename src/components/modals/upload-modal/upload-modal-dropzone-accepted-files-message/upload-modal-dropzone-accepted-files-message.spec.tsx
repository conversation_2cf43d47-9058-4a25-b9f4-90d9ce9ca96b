import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { UploadModalDropzoneAcceptedFilesMessage } from './upload-modal-dropzone-accepted-files-message.template'

describe('Upload Modal Dropzone Accepted Files Message', () => {
  it('should be able to render correctly', () => {
    const { getByTestId } = customRender(
      <UploadModalDropzoneAcceptedFilesMessage data-testid="upload-modal-dropzone-accepted-files-message" />
    )

    expect(getByTestId('upload-modal-dropzone-accepted-files-message')).toBeInTheDocument()
  })
})
