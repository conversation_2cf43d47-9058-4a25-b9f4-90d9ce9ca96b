import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { UploadModalDropzoneInput } from './upload-modal-dropzone-input.template'

describe('Upload Modal Dropzone Input', () => {
  it('should be able to render correctly', () => {
    const { getByTestId } = customRender(<UploadModalDropzoneInput data-testid="upload-modal-dropzone-input" />)

    expect(getByTestId('upload-modal-dropzone-input')).toBeInTheDocument()
  })
})
