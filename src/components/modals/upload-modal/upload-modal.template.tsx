import parser from 'html-react-parser'
import { theme, Buttons, BaseModal } from '@mz-codes/design-system'
import Dropzone from 'react-dropzone'
import { translations } from './upload-modal.translations'
import { TUploadModalTemplate } from './upload-modal.types'
import { UploadModalDropzoneContent } from './upload-modal-dropzone-content'
import { UploadModalDropzoneImage } from './upload-modal-dropzone-image'
import { UploadModalDropzoneInput } from './upload-modal-dropzone-input'
import { UploadModalDropzoneMessage } from './upload-modal-dropzone-message'
import { UploadModalProgressBar } from './upload-modal-dropzone-progress-bar'
import { UploadModalProgressBarWrapper } from './upload-modal-dropzone-progress-bar-wrapper'
import { UploadModalDropzoneAcceptedFilesMessage } from './upload-modal-dropzone-accepted-files-message/upload-modal-dropzone-accepted-files-message.template'

export function UploadModalTemplate(props: TUploadModalTemplate) {
  const {
    show,
    closeModal,
    width,
    title,
    message,
    confirmButtonLabel,
    acceptedFiles,
    onDrop,
    progress,
    selectedFile,
    templateLabel,
    onConfirm,
    onTemplateClick,
    children,
    ...rest
  } = props

  return (
    <BaseModal show={show} onClose={closeModal} width={width} {...rest}>
      <BaseModal.Header data-testid="upload-modal-header">
        <BaseModal.Title>{title}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body data-testid="upload-modal-body">
        {message && <BaseModal.Text>{parser(message)}</BaseModal.Text>}
        {children}
        <UploadModalTemplate.Dropzone multiple={false} accept={acceptedFiles} onDrop={onDrop}>
          {({ getRootProps, getInputProps, isDragActive }) => (
            <UploadModalTemplate.DropzoneContent {...getRootProps({ isDragActive })} $selectedFile={!!selectedFile}>
              <UploadModalTemplate.DropzoneImage size={70} />
              <UploadModalTemplate.DropzoneInput data-testid="upload-modal-dropzone" {...getInputProps()} />

              <UploadModalTemplate.DropzoneMessage>
                {selectedFile ? selectedFile.name : parser(translations.dragFileMessage)}
              </UploadModalTemplate.DropzoneMessage>

              {!selectedFile && !!acceptedFiles && (
                <UploadModalTemplate.DropzoneAcceptedFilesMessage>
                  {translations.acceptedFiles} {acceptedFiles}
                </UploadModalTemplate.DropzoneAcceptedFilesMessage>
              )}
            </UploadModalTemplate.DropzoneContent>
          )}
        </UploadModalTemplate.Dropzone>
        {!!progress && (
          <UploadModalTemplate.ProgressBarWrapper data-testid="upload-modal-progress-bar">
            <UploadModalTemplate.ProgressBar progress={progress} />
          </UploadModalTemplate.ProgressBarWrapper>
        )}
      </BaseModal.Body>
      <BaseModal.Footer data-testid="upload-modal-footer">
        <BaseModal.ButtonWrapper>
          {templateLabel && onTemplateClick && (
            <Buttons.Base
              $bgColor="transparent"
              loadingText={translations.downloading}
              $color={theme.legacy.colors.primary.primary}
              $padding="0"
              $margin="0"
              onClick={onTemplateClick}
              data-testid="upload-modal-template-button"
            >
              {templateLabel}
            </Buttons.Base>
          )}

          <Buttons.Primary onClick={onConfirm} $margin="0 0 0 auto" disabled={!selectedFile}>
            {confirmButtonLabel || translations.confirmButtonLabel}
          </Buttons.Primary>
        </BaseModal.ButtonWrapper>
      </BaseModal.Footer>
    </BaseModal>
  )
}

UploadModalTemplate.Dropzone = Dropzone
UploadModalTemplate.DropzoneContent = UploadModalDropzoneContent
UploadModalTemplate.DropzoneImage = UploadModalDropzoneImage
UploadModalTemplate.DropzoneInput = UploadModalDropzoneInput
UploadModalTemplate.DropzoneMessage = UploadModalDropzoneMessage
UploadModalTemplate.DropzoneAcceptedFilesMessage = UploadModalDropzoneAcceptedFilesMessage
UploadModalTemplate.ProgressBar = UploadModalProgressBar
UploadModalTemplate.ProgressBarWrapper = UploadModalProgressBarWrapper
