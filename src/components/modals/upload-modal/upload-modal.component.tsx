import { UploadModalTemplate } from './upload-modal.template'
import { TUploadModal } from './upload-modal.types'

function UploadModal(props: TUploadModal) {
  const {
    show,
    title,
    message,
    onClose,
    onConfirm,
    width = '560px',
    selectedFile,
    onSelectedFile,
    progress,
    acceptedFiles,
    confirmButtonLabel,
    templateLabel,
    onTemplateClick,
    children,
  } = props

  const closeModal = () => {
    onClose()
  }

  const onDrop = (accepted: File[]) => {
    const [file] = accepted

    onSelectedFile(file)
  }

  return (
    <UploadModalTemplate
      show={show}
      closeModal={closeModal}
      onConfirm={onConfirm}
      title={title}
      message={message}
      width={width}
      selectedFile={selectedFile}
      acceptedFiles={acceptedFiles}
      onDrop={onDrop}
      progress={progress}
      templateLabel={templateLabel}
      onTemplateClick={onTemplateClick}
      confirmButtonLabel={confirmButtonLabel}
      data-testid="upload-modal"
    >
      {children}
    </UploadModalTemplate>
  )
}

export { UploadModal }
