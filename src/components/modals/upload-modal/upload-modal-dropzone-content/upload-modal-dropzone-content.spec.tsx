import { theme } from '@mz-codes/design-system'
import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { UploadModalDropzoneContent } from './upload-modal-dropzone-content.template'

function generateMockData() {
  const mockProps = {
    isDragActive: false,
    $selectedFile: false,
  }

  return { mockProps }
}

describe('Upload Modal Dropzone Content', () => {
  it('should be able to render a Dropzone Content', () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(
      <UploadModalDropzoneContent {...mockProps} data-testid="upload-modal-dropzone-content" />
    )

    expect(getByTestId('upload-modal-dropzone-content')).toBeInTheDocument()
  })

  it('should be able to render correct styles when drag is active', () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(
      <UploadModalDropzoneContent {...mockProps} isDragActive data-testid="upload-modal-dropzone-content" />
    )

    expect(getByTestId('upload-modal-dropzone-content')).toHaveStyleRule(
      'border-color',
      theme.legacy.colors.primary.primary
    )
  })

  it('should be able to render correct styles when has selected file', () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(
      <UploadModalDropzoneContent {...mockProps} $selectedFile data-testid="upload-modal-dropzone-content" />
    )

    expect(getByTestId('upload-modal-dropzone-content')).toHaveStyleRule(
      'border-color',
      theme.legacy.colors.primary.primary
    )
  })
})
