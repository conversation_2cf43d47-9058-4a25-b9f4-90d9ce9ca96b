import { styled } from 'styled-components'
import { UploadModalDropzoneImage } from '../upload-modal-dropzone-image'
import { UploadModalDropzoneMessage } from '../upload-modal-dropzone-message'
import { TUploadmodalDropzoneContent, TUploadModalDropzoneContentResponse } from './upload-modal-dropzone-content.types'

export const UploadModalDropzoneContent = styled.div.attrs<TUploadmodalDropzoneContent>((props) => ({
  $isDragActive: props.isDragActive,
  isDragActive: undefined,
}))<TUploadModalDropzoneContentResponse>`
  display: flex;
  padding: 20px 16px;
  width: 100%;
  border: 2px dashed;
  align-items: stretch;
  flex-direction: row;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;

  cursor: pointer;
  &:hover {
    border-color: ${({ theme }) => theme.legacy.colors.primary.primary};
  }
  ${({ $isDragActive, $selectedFile, theme }) =>
    $isDragActive || $selectedFile ? `border-color: ${theme.legacy.colors.primary.primary}` : ''};
  ${UploadModalDropzoneImage} {
    ${({ $selectedFile, theme }) => ($selectedFile ? `color: ${theme.legacy.colors.primary.primary}` : '')}
  }
  ${UploadModalDropzoneMessage} {
    ${({ $selectedFile }) => ($selectedFile ? `border-right: 0; padding-right: 0;` : '')}
  }
`
