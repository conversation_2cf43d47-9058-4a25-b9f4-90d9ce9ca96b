import { customRender } from 'test'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import userEvent from '@testing-library/user-event'
import { waitFor } from '@testing-library/react'
import { VinculateShareholderModal } from '.'
import { TVinculateShareholderModal } from './vinculate-shareholder-modal.types'

function generateMockData() {
  const searchShareholderByNameMock = vi.hoisted(() => vi.fn())
  searchShareholderByNameMock.mockImplementation(() => {
    return {
      data: [
        { displayName: 'Display Name 1', document: '99167613616', shareholderId: '01', shareholderType: 'Individual' },
        { displayName: 'Display Name 2', document: '24923079053', shareholderId: '02', shareholderType: 'Individual' },
        { displayName: 'Display Name 3', document: '78242328000101', shareholderId: '03', shareholderType: 'Fund' },
      ],
    }
  })
  const vinculateShareholderWithGroupMock = vi.hoisted(() => vi.fn())
  vinculateShareholderWithGroupMock.mockResolvedValue({})
  const cpfCnpj = vi.fn()
  const onClose = vi.fn()
  const getShareholderType = vi.fn()
  const shareholderFundsReload = vi.fn()

  const mockProps: TVinculateShareholderModal = {
    visibility: true,
    title: 'Vinculate Shareholder Modal',
    placeholder: 'placeholder',
    cpfCnpj,
    onClose,
    getShareholderType,
    shareholderFundsReload,
    shareholderGroupId: '123456',
  }

  return { mockProps, searchShareholderByNameMock, vinculateShareholderWithGroupMock }
}

describe('Vinculate Shareholder Modal Template', () => {
  beforeEach(() => {
    const { searchShareholderByNameMock, vinculateShareholderWithGroupMock } = generateMockData()

    vi.clearAllMocks()

    vi.mock('utils', () => ({
      localInformation: {
        getCompanyId: vi.fn().mockReturnValue('mocked-company-id'),
      },
    }))

    vi.mock('client', () => ({
      searchShareholderByName: searchShareholderByNameMock,
      vinculateShareholderWithGroup: vinculateShareholderWithGroupMock,
    }))

    vi.mock('hooks', () => {
      const useDebounce = vi.fn().mockImplementation((value) => value)
      return {
        useDebounce,
      }
    })

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn((key) => {
          if (key === 'addButton') return 'Add'
          if (key === 'pageGroupedTearSheet.noShareHolder') return 'No shareholder found'
          return key
        }),
      },
    }))
  })

  it('should call onClose when the close button is clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<VinculateShareholderModal {...mockProps} />)

    const closeButton = getByTestId('close-button')

    await userEvent.click(closeButton)

    expect(mockProps.onClose).toHaveBeenCalledTimes(1)
  })

  it('should update fundSearch when handleInputChange is called', async () => {
    const { mockProps } = generateMockData()
    const user = userEvent.setup()

    const { getByPlaceholderText } = customRender(<VinculateShareholderModal {...mockProps} />)

    const input = getByPlaceholderText('placeholder')
    await user.type(input, 'test search')

    expect(input).toHaveValue('test search')
  })

  it('should update fundsList when searchFunds returns data', async () => {
    const { mockProps, searchShareholderByNameMock } = generateMockData()
    const user = userEvent.setup()

    const { getByPlaceholderText, findByText } = customRender(<VinculateShareholderModal {...mockProps} />)

    const input = getByPlaceholderText('placeholder')
    await user.type(input, 'fund search')

    await waitFor(() => {
      expect(searchShareholderByNameMock).toHaveBeenCalledWith('mocked-company-id', 'fund search')
    })

    expect(await findByText('Display Name 1')).toBeInTheDocument()
    expect(await findByText('Display Name 2')).toBeInTheDocument()
    expect(await findByText('Display Name 3')).toBeInTheDocument()
  })

  it('should call vinculateShareholderWithGroup when the Add button is clicked', async () => {
    const { mockProps, vinculateShareholderWithGroupMock } = generateMockData()
    const user = userEvent.setup()

    const { getByPlaceholderText, getAllByText } = customRender(<VinculateShareholderModal {...mockProps} />)

    const input = getByPlaceholderText('placeholder')
    await user.type(input, 'fund search')

    const addButtons = getAllByText('Add')
    await user.click(addButtons[0])

    expect(vinculateShareholderWithGroupMock).toHaveBeenCalledWith('mocked-company-id', '123456', '01')
  })

  it('should render a empty list when input is empty', async () => {
    const { mockProps } = generateMockData()
    const user = userEvent.setup()

    const { getByPlaceholderText, queryByTestId } = customRender(<VinculateShareholderModal {...mockProps} />)

    const input = getByPlaceholderText('placeholder')
    await user.clear(input)

    await waitFor(() => {
      expect(queryByTestId('loading')).not.toBeInTheDocument()
      expect(queryByTestId('list-header')).not.toBeInTheDocument()
    })
  })

  it('should render not found when searchFunds returns empty', async () => {
    const { mockProps, searchShareholderByNameMock } = generateMockData()
    const user = userEvent.setup()
    searchShareholderByNameMock.mockImplementation(() => ({ data: [] }))

    const { getByPlaceholderText, findByText } = customRender(<VinculateShareholderModal {...mockProps} />)

    const input = getByPlaceholderText('placeholder')
    await user.type(input, 'non existent fund')

    const notFoundText = await findByText('No shareholder found')
    expect(notFoundText).toBeInTheDocument()
    expect(searchShareholderByNameMock).toHaveBeenCalledWith('mocked-company-id', 'non existent fund')
  })
})
