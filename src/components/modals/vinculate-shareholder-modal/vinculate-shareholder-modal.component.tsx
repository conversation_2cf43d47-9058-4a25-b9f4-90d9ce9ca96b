import { searchShareholderByName, vinculateShareholderWithGroup } from 'client'
import { localInformation } from 'utils'
import { useState, useEffect, useCallback } from 'react'
import { useDebounce } from 'hooks'
import { TFundsList, TVinculateShareholderModal } from './vinculate-shareholder-modal.types'
import { VinculateShareholderModalTemplate } from './vinculate-shareholder-modal.template'

export function VinculateShareholderModal(props: TVinculateShareholderModal) {
  const {
    title,
    placeholder,
    visibility,
    onClose,
    cpfCnpj,
    shareholderGroupId,
    getShareholderType,
    shareholderFundsReload,
  } = props

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [notFound, setNotFound] = useState<boolean>(false)
  const [fundSearch, setFundSearch] = useState<string>('')
  const [fundsList, setFundsList] = useState<TFundsList[]>([])

  const companyId = localInformation.getCompanyId()

  const debouncedSearch = useDebounce(fundSearch)

  const searchFunds = useCallback(
    async (searchTerm: string) => {
      if (searchTerm.trim().length === 0) {
        setFundsList([])
        setNotFound(false)
        return
      }

      setIsLoading(true)

      const res = await searchShareholderByName(companyId, searchTerm)

      if (res.data.length === 0) {
        setNotFound(true)
        setFundsList([])
      } else {
        setNotFound(false)
        setFundsList(res.data)
      }

      setIsLoading(false)
    },
    [companyId]
  )

  useEffect(() => {
    searchFunds(debouncedSearch as string)
  }, [debouncedSearch, searchFunds])

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFundSearch(event.target.value)
  }

  const handleVinculateFund = (shareholderId: string) => {
    vinculateShareholderWithGroup(companyId, shareholderGroupId, shareholderId).then(() => {
      shareholderFundsReload()
      setFundSearch('')
    })
  }

  return (
    <VinculateShareholderModalTemplate
      handleInputChange={handleInputChange}
      handleOnClose={onClose}
      inputValue={fundSearch}
      title={title}
      visibility={visibility}
      placeholder={placeholder}
      cpfCnpj={cpfCnpj}
      getShareholderType={getShareholderType}
      vinculateFund={handleVinculateFund}
      fundsList={fundsList}
      isLoading={isLoading}
      notFound={notFound}
    />
  )
}
