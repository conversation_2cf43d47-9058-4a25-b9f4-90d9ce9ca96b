import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { VinculateShareholderModalWrapper } from './vinculate-shareholder-modal-wrapper.template'

describe('Vinculate Shareholder Modal Wrapper', () => {
  it('should be able to render a wrapper correctly', () => {
    const { getByTestId } = customRender(<VinculateShareholderModalWrapper data-testid="wrapper" />)

    const wrapper = getByTestId('wrapper')

    expect(wrapper).toBeInTheDocument()
  })
})
