import { i18n } from 'translate'
import { BaseModal, Buttons, Inputs, Loading, Table } from '@mz-codes/design-system'

import { DataNotFound } from 'components/data-not-found'

import { TVinculateShareholderModalTemplate } from './vinculate-shareholder-modal.types'
import { VinculateShareholderModalWrapper } from './vinculate-shareholder-modal-wrapper'

export function VinculateShareholderModalTemplate(props: TVinculateShareholderModalTemplate) {
  const {
    handleOnClose,
    visibility,
    title,
    placeholder,
    handleInputChange,
    inputValue,
    fundsList,
    getShareholderType,
    cpfCnpj,
    vinculateFund,
    isLoading,
    notFound,
  } = props

  return (
    <BaseModal onClose={handleOnClose} show={visibility} width="740px" data-testid="vinculate-shareholder-modal">
      <BaseModal.Header>
        <BaseModal.Title>{title}</BaseModal.Title>
      </BaseModal.Header>

      <BaseModal.Body>
        <Inputs.Label $horizontalAlignment="center">
          <Inputs.BaseLined
            name="vinculateShareholderInput"
            value={inputValue}
            type="text"
            onChange={handleInputChange}
            placeholder={placeholder}
            autoComplete="off"
          />
        </Inputs.Label>

        <VinculateShareholderModalWrapper>
          {isLoading && <Loading data-testid="loading" />}

          {fundsList.length > 0 && !isLoading ? (
            <>
              <Table.THead style={{ boxShadow: 'none' }} data-testid="list-header">
                <Table.TR>
                  <Table.TH>{i18n.t('name')}</Table.TH>
                  <Table.TH>{i18n.t('shareholderType')}</Table.TH>
                  <Table.TH>{i18n.t('document')}</Table.TH>
                  <Table.TH>{i18n.t('action')}</Table.TH>
                </Table.TR>
              </Table.THead>

              <Table.TBody>
                {fundsList.map((fund) => (
                  <Table.TR key={fund.shareholderId}>
                    <Table.TD>{fund.displayName}</Table.TD>
                    <Table.TD>{getShareholderType(fund.shareholderType)}</Table.TD>
                    <Table.TD>{cpfCnpj(fund.document)}</Table.TD>
                    <Table.TD>
                      <Buttons.Primary
                        onClick={() => vinculateFund(fund.shareholderId)}
                        $minWidth="50px"
                        $padding="0"
                        $margin="6px 0 0 0"
                      >
                        {i18n.t('addButton')}
                      </Buttons.Primary>
                    </Table.TD>
                  </Table.TR>
                ))}
              </Table.TBody>
            </>
          ) : (
            notFound && (
              <DataNotFound $noBorderBottom $fontSize="20px" $height="100%">
                {i18n.t('pageGroupedTearSheet.noShareHolder')}
              </DataNotFound>
            )
          )}
        </VinculateShareholderModalWrapper>
      </BaseModal.Body>
    </BaseModal>
  )
}
