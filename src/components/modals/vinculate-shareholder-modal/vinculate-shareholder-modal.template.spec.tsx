import { customRender } from 'test'
import { describe, expect, it, vi, beforeEach } from 'vitest'

import { VinculateShareholderModalTemplate } from './vinculate-shareholder-modal.template'
import { TFundsList } from './vinculate-shareholder-modal.types'

let mockProps: ReturnType<typeof generateMockData>['mockProps']

function generateMockData() {
  const cpfCnpj = vi.fn()
  const handleOnClose = vi.fn()
  const getShareholderType = vi.fn()
  const vinculateFund = vi.fn()
  const handleInputChange = vi.fn()

  const funds: TFundsList[] = [
    { displayName: 'Display Name 1', document: '99167613616', shareholderId: '01', shareholderType: 'Individual' },
    { displayName: 'Display Name 2', document: '24923079053', shareholderId: '02', shareholderType: 'Individual' },
    { displayName: 'Display Name 3', document: '78242328000101', shareholderId: '03', shareholderType: 'Fund' },
  ]

  return {
    mockProps: {
      visibility: true,
      title: 'Vinculate Shareholder Modal',
      placeholder: 'placeholder',
      inputValue: 'disp',
      fundsList: funds,
      cpfCnpj,
      handleOnClose,
      getShareholderType,
      vinculateFund,
      handleInputChange,
      isLoading: false,
      notFound: false,
    },
  }
}

describe('Vinculate Shareholder Modal Template', () => {
  beforeEach(() => {
    mockProps = generateMockData().mockProps
  })

  it('should be able to render a modal with funds correctly', () => {
    const { getByTestId, getByText, getByDisplayValue, getAllByText } = customRender(
      <VinculateShareholderModalTemplate {...mockProps} />
    )

    const container = getByTestId('vinculate-shareholder-modal')
    const title = getByText('Vinculate Shareholder Modal')
    const inputValue = getByDisplayValue('disp')
    const listHeader = getByTestId('list-header')
    const item01 = getByText('Display Name 1')
    const item02 = getByText('Display Name 2')
    const item03 = getByText('Display Name 3')
    const addButtons = getAllByText('Add')

    expect(container).toBeInTheDocument()
    expect(title).toBeInTheDocument()
    expect(inputValue).toBeInTheDocument()
    expect(listHeader).toBeInTheDocument()
    expect(item01).toBeInTheDocument()
    expect(item02).toBeInTheDocument()
    expect(item03).toBeInTheDocument()
    expect(addButtons).toHaveLength(3)
  })

  it('should be able to render "No shareholder found" if fundsList is empty', () => {
    const { getByText } = customRender(<VinculateShareholderModalTemplate {...mockProps} fundsList={[]} notFound />)

    const text = getByText('No shareholder found')

    expect(text).toBeInTheDocument()
  })

  it('should be able to render a Loading when isLoading is true', () => {
    const { getByTestId } = customRender(
      <VinculateShareholderModalTemplate {...mockProps} inputValue="" fundsList={[]} isLoading />
    )

    const text = getByTestId('loading')

    expect(text).toBeInTheDocument()
  })
})
