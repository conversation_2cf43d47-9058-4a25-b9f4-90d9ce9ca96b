import React from 'react'

export type TFundsList = {
  shareholderId: string
  displayName: string
  shareholderType: string
  document: string
}

export type TVinculateShareholderModal = {
  title: string
  placeholder?: string
  visibility: boolean
  onClose(): void
  cpfCnpj(value: string): string
  getShareholderType(value: string): string
  shareholderGroupId: string
  shareholderFundsReload(): void
}

export type TVinculateShareholderModalTemplate = {
  title: string
  placeholder?: string
  visibility: boolean
  handleOnClose(): void
  handleInputChange(event: React.ChangeEvent<HTMLInputElement>): void
  inputValue: string
  fundsList: TFundsList[]
  getShareholderType(value: string): string
  cpfCnpj(value: string): string
  vinculateFund(shareholderId: string): void
  isLoading: boolean
  notFound: boolean
}
