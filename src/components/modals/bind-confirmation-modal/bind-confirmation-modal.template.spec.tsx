import { describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'

import { BindConfirmationModalTemplate } from './bind-confirmation-modal.template'

describe('Bind Confirmation Modal Template', () => {
  it('should render the bind confirmation modal content correctly', () => {
    const onClose = vi.fn()
    const onConfirm = vi.fn()
    const setIgnore = vi.fn()

    const { getByTestId } = customRender(
      <BindConfirmationModalTemplate
        ignore={false}
        onClose={onClose}
        onConfirm={onConfirm}
        setIgnore={setIgnore}
        show
      />
    )

    expect(getByTestId('bind-confirmation-content')).toBeInTheDocument()
  })

  it('should render the bind confirmation modal title correctly', () => {
    const onClose = vi.fn()
    const onConfirm = vi.fn()
    const setIgnore = vi.fn()

    const { getByTestId } = customRender(
      <BindConfirmationModalTemplate
        ignore={false}
        onClose={onClose}
        onConfirm={onConfirm}
        setIgnore={setIgnore}
        show
      />
    )

    expect(getByTestId('bind-confirmation-title')).toBeInTheDocument()
  })

  it('should render the bind confirmation modal body message correctly', () => {
    const onClose = vi.fn()
    const onConfirm = vi.fn()
    const setIgnore = vi.fn()

    const { getByTestId } = customRender(
      <BindConfirmationModalTemplate
        ignore={false}
        onClose={onClose}
        onConfirm={onConfirm}
        setIgnore={setIgnore}
        show
      />
    )

    expect(getByTestId('bind-confirmation-body')).toBeInTheDocument()
  })

  it('should render the bind confirmation modal buttons correctly', () => {
    const onClose = vi.fn()
    const onConfirm = vi.fn()
    const setIgnore = vi.fn()

    const { getByTestId } = customRender(
      <BindConfirmationModalTemplate
        ignore={false}
        onClose={onClose}
        onConfirm={onConfirm}
        setIgnore={setIgnore}
        show
      />
    )

    expect(getByTestId('bind-confirmation-buttons')).toBeInTheDocument()
  })

  it('should render the bind confirmation modal checkbox correctly', () => {
    const onClose = vi.fn()
    const onConfirm = vi.fn()
    const setIgnore = vi.fn()

    const { getByTestId } = customRender(
      <BindConfirmationModalTemplate
        ignore={false}
        onClose={onClose}
        onConfirm={onConfirm}
        setIgnore={setIgnore}
        show
      />
    )

    expect(getByTestId('checkbox')).toBeInTheDocument()
  })
})
