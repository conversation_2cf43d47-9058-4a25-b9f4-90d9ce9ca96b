import { beforeEach, describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import { localInformation } from 'utils'

import userEvent from '@testing-library/user-event'
import { BindConfirmationModal } from './bind-confirmation-modal.component'

function generateMockData() {
  const onClose = vi.fn()
  const onConfirm = vi.fn()

  return { onClose, onConfirm }
}

describe('Bind Confirmation Modal Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn((key: string) => key),
      },
    }))
  })

  it('should call onClose when user click on modal close button ', async () => {
    const { onClose, onConfirm } = generateMockData()

    vi.spyOn(localInformation, 'pushIgnoredGroupsToBindInstitution')

    const { getByTestId } = customRender(
      <BindConfirmationModal group="exampleGroup" show onClose={onClose} onConfirm={onConfirm} />
    )

    await userEvent.click(getByTestId('close-button'))
    expect(onClose).toBeCalledTimes(1)
  })

  it('should call onClose when user click on ignore button', async () => {
    const { onClose, onConfirm } = generateMockData()

    const { getByText } = customRender(
      <BindConfirmationModal group="exampleGroup" show onClose={onClose} onConfirm={onConfirm} />
    )

    await userEvent.click(getByText('bindConfirmationModal.ignore'))
    expect(onClose).toBeCalledTimes(1)
  })

  it('should not call pushIgnoredGroupsToBindInstitution when ignore is false', async () => {
    const { onClose, onConfirm } = generateMockData()

    vi.spyOn(localInformation, 'pushIgnoredGroupsToBindInstitution')

    const { getByText } = customRender(
      <BindConfirmationModal group="exampleGroup" show onClose={onClose} onConfirm={onConfirm} />
    )

    await userEvent.click(getByText('bindConfirmationModal.ignore'))
    expect(onClose).toBeCalledTimes(1)
    expect(localInformation.pushIgnoredGroupsToBindInstitution).not.toBeCalled()
  })

  it('should call pushIgnoredGroupsToBindInstitution when ignore is true', async () => {
    const { onClose, onConfirm } = generateMockData()

    vi.spyOn(localInformation, 'pushIgnoredGroupsToBindInstitution')

    const { getByText, getByTestId } = customRender(
      <BindConfirmationModal group="exampleGroup" show onClose={onClose} onConfirm={onConfirm} />
    )

    await userEvent.click(getByTestId('checkbox'))
    await userEvent.click(getByText('bindConfirmationModal.ignore'))

    expect(onClose).toBeCalledTimes(1)
    expect(localInformation.pushIgnoredGroupsToBindInstitution).toBeCalledTimes(1)
  })

  it('should call onConfirm when user click on confirm', async () => {
    const { onClose, onConfirm } = generateMockData()

    vi.spyOn(localInformation, 'pushIgnoredGroupsToBindInstitution')

    const { getByText, getByTestId } = customRender(
      <BindConfirmationModal group="exampleGroup" show onClose={onClose} onConfirm={onConfirm} />
    )

    await userEvent.click(getByTestId('checkbox'))
    await userEvent.click(getByText('bindConfirmationModal.confirm'))

    expect(onConfirm).toBeCalledTimes(1)
  })
})
