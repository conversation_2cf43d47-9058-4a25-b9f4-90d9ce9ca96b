import { useCallback, useState } from 'react'
import { localInformation } from 'utils'

import { TBindConfirmationModalComponent } from './bind-confirmation-modal.types'
import { BindConfirmationModalTemplate } from './bind-confirmation-modal.template'

export function BindConfirmationModal({ group, show, onClose, onConfirm }: TBindConfirmationModalComponent) {
  const [ignore, setIgnore] = useState(false)

  const closeModal = useCallback(() => {
    if (ignore) {
      localInformation.pushIgnoredGroupsToBindInstitution(group)
    }

    onClose()
  }, [group, ignore, onClose])

  return (
    <BindConfirmationModalTemplate
      data-testid="bind-confirmation-content"
      ignore={ignore}
      setIgnore={setIgnore}
      onClose={closeModal}
      onConfirm={onConfirm}
      show={show}
    />
  )
}
