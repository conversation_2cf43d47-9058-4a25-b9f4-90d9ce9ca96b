import { i18n } from 'translate'
import { theme, BaseModal, Buttons, Inputs } from '@mz-codes/design-system'

import { TBindConfirmationModalTemplate } from './bind-confirmation-modal.types'

export function BindConfirmationModalTemplate({
  show,
  onClose,
  onConfirm,
  ignore,
  setIgnore,
}: TBindConfirmationModalTemplate) {
  return (
    <BaseModal data-testid="bind-confirmation-content" show={show} onClose={onClose} width="450px">
      <BaseModal.Header>
        <BaseModal.Title data-testid="bind-confirmation-title">{i18n.t('bindConfirmationModal.title')}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body data-testid="bind-confirmation-body">
        {i18n.t('bindConfirmationModal.messageFirst')} <br />
        {i18n.t('bindConfirmationModal.messageSecond')}
      </BaseModal.Body>
      <BaseModal.Footer>
        <BaseModal.ButtonWrapper data-testid="bind-confirmation-buttons">
          <Buttons.Cancel onClick={onClose}>{i18n.t('bindConfirmationModal.ignore')}</Buttons.Cancel>
          <Buttons.Primary onClick={onConfirm}>{i18n.t('bindConfirmationModal.confirm')}</Buttons.Primary>
        </BaseModal.ButtonWrapper>
        <Inputs.Label $horizontalAlignment="center">
          <Inputs.Checkbox
            handleChange={(e) => setIgnore(e.target.checked)}
            checked={ignore}
            name={i18n.t('bindConfirmationModal.seeAgain')}
            value="value"
          />
          <Inputs.Text
            style={{ paddingTop: '16px', paddingBottom: '16px', color: theme.legacy.colors.grayScale.texts }}
          >
            {i18n.t('bindConfirmationModal.seeAgain')}
          </Inputs.Text>
        </Inputs.Label>
      </BaseModal.Footer>
    </BaseModal>
  )
}
