import React from 'react'

export type TNewGroupModal = {
  title: string
  placeholder?: string
  visibility: boolean
  onClose(): void
  onCreateGroup(groupName?: string): Promise<void> | void
  groupExists: boolean
}

export type TNewGroupModalTemplate = {
  title: string
  placeholder?: string
  visibility: boolean
  handleOnClose(): void
  handleConfirm(): void
  onCreateGroup(groupName?: string): Promise<void> | void
  groupExists: boolean
  inputValue: string
  handleInputChange(event: React.ChangeEvent<HTMLInputElement>): void
}
