import { useState } from 'react'

import { TNewGroupModal } from './new-group-modal.types'
import { NewGroupModalTemplate } from './new-group-modal.template'

export function NewGroupModal(props: TNewGroupModal) {
  const [inputValue, setInputValue] = useState<string>('')
  const { title, placeholder, visibility, onClose, onCreateGroup, groupExists } = props

  const handleOnClose = () => {
    setInputValue('')
    onClose()
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value)
  }

  const handleConfirm = () => {
    if (inputValue) {
      onCreateGroup(inputValue)
    }
    setInputValue('')
  }

  return (
    <NewGroupModalTemplate
      groupExists={groupExists}
      handleConfirm={handleConfirm}
      handleInputChange={handleInputChange}
      handleOnClose={handleOnClose}
      inputValue={inputValue}
      title={title}
      onCreateGroup={onCreateGroup}
      visibility={visibility}
      placeholder={placeholder}
    />
  )
}
