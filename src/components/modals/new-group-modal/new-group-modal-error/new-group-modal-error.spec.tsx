import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { NewGroupModalError } from './new-group-modal-error.template'

describe('New Group Modal Error Template', () => {
  it('should render the error correctly', () => {
    const { getByTestId } = customRender(<NewGroupModalError data-testid="new-group-modal-error" />)

    expect(getByTestId('new-group-modal-error')).toBeInTheDocument()
  })
})
