import { i18n } from 'translate'

import { Buttons, BaseModal, Inputs } from '@mz-codes/design-system'
import { NewGroupModalError } from './new-group-modal-error'
import { TNewGroupModalTemplate } from './new-group-modal.types'

export function NewGroupModalTemplate(props: TNewGroupModalTemplate) {
  const { title, placeholder, visibility, groupExists, handleOnClose, inputValue, handleInputChange, handleConfirm } =
    props

  return (
    <BaseModal data-testid="new-group-content" show={visibility} onClose={handleOnClose} width="505px" closeButton>
      <BaseModal.Header>
        <BaseModal.Title data-testid="new-group-group-title">{title}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body>
        <Inputs.Label>
          <Inputs.Text>{i18n.t('groupName')}</Inputs.Text>
          <Inputs.BaseLined
            name="newGroupInput"
            value={inputValue}
            onChange={handleInputChange}
            placeholder={placeholder}
          />
          {groupExists && (
            <NewGroupModalError data-testid="new-group-group-exist">{i18n.t('hasGroup')}</NewGroupModalError>
          )}
        </Inputs.Label>
      </BaseModal.Body>
      <BaseModal.Footer>
        <BaseModal.ButtonWrapper data-testid="new-group-buttons">
          <Buttons.Cancel onClick={handleOnClose}>{i18n.t('globals.cancel')}</Buttons.Cancel>
          <Buttons.Primary disabled={!inputValue} onClick={handleConfirm}>
            {i18n.t('save')}
          </Buttons.Primary>
        </BaseModal.ButtonWrapper>
      </BaseModal.Footer>
    </BaseModal>
  )
}
