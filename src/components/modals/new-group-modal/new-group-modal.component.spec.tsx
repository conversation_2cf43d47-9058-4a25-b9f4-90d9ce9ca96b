import { beforeEach, describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import userEvent from '@testing-library/user-event'
import { NewGroupModal } from './new-group-modal.component'

function generateMockData() {
  const onClose = vi.fn()
  const onCreateGroup = vi.fn()

  return { onClose, onCreateGroup }
}

describe('New Group Modal', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    vi.mock('translate', () => ({
      i18n: {
        t: vi.fn((key: string) => key),
      },
    }))
  })

  it('should render the modal correctly', () => {
    const { onClose, onCreateGroup } = generateMockData()

    const { getByTestId } = customRender(
      <NewGroupModal
        title="Test Title"
        placeholder="Test Placeholder"
        visibility
        onClose={onClose}
        onCreateGroup={onCreateGroup}
        groupExists={false}
      />
    )

    expect(getByTestId('new-group-content')).toBeInTheDocument()
  })

  it('should call onClose when user clicks on close button', async () => {
    const { onClose, onCreateGroup } = generateMockData()

    const { getByTestId } = customRender(
      <NewGroupModal
        title="Test Title"
        placeholder="Test Placeholder"
        visibility
        onClose={onClose}
        onCreateGroup={onCreateGroup}
        groupExists={false}
      />
    )

    await userEvent.click(getByTestId('close-button'))

    expect(onClose).toBeCalledTimes(1)
  })

  it('should call onClose when user clicks on cancel button', async () => {
    const { onClose, onCreateGroup } = generateMockData()

    const { getByText } = customRender(
      <NewGroupModal
        title="Test Title"
        placeholder="Test Placeholder"
        visibility
        onClose={onClose}
        onCreateGroup={onCreateGroup}
        groupExists={false}
      />
    )

    await userEvent.click(getByText('globals.cancel'))

    expect(onClose).toBeCalledTimes(1)
  })

  it('should call onCreateGroup when user clicks on save button', async () => {
    const { onClose, onCreateGroup } = generateMockData()

    const { getByText, getByPlaceholderText } = customRender(
      <NewGroupModal
        title="Test Title"
        placeholder="Test Placeholder"
        visibility
        onClose={onClose}
        onCreateGroup={onCreateGroup}
        groupExists={false}
      />
    )

    await userEvent.type(getByPlaceholderText('Test Placeholder'), 'Input value')
    await userEvent.click(getByText('save'))

    expect(onCreateGroup).toBeCalledTimes(1)
  })

  it('should not call onCreateGroup when user click on save button and input is not filled', async () => {
    const { onClose, onCreateGroup } = generateMockData()

    const { getByText, getByPlaceholderText } = customRender(
      <NewGroupModal
        title="Test Title"
        placeholder="Test Placeholder"
        visibility
        onClose={onClose}
        onCreateGroup={onCreateGroup}
        groupExists={false}
      />
    )

    await userEvent.click(getByText('save'))

    expect(onCreateGroup).not.toBeCalled()

    const placeholder = getByPlaceholderText('Test Placeholder') as HTMLInputElement

    expect(placeholder.value).toBe('')
  })
})
