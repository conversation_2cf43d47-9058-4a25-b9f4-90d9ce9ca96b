import { describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'

import { NewGroupModalTemplate } from './new-group-modal.template'

function generateMockData() {
  const handleOnClose = vi.fn()
  const handleConfirm = vi.fn()
  const handleInputChange = vi.fn()
  const onCreateGroup = vi.fn()

  return { handleOnClose, handleConfirm, handleInputChange, onCreateGroup }
}

describe('Bind Confirmation Modal Template', () => {
  it('should render the new group modal content correctly', () => {
    const { handleOnClose, handleConfirm, handleInputChange, onCreateGroup } = generateMockData()

    const { getByTestId } = customRender(
      <NewGroupModalTemplate
        groupExists
        handleConfirm={handleConfirm}
        handleInputChange={handleInputChange}
        handleOnClose={handleOnClose}
        inputValue="Input Value Test"
        onCreateGroup={onCreateGroup}
        title="Title Test"
        visibility
      />
    )

    expect(getByTestId('new-group-content')).toBeInTheDocument()
  })

  it('should render the title correctly', () => {
    const { handleOnClose, handleConfirm, handleInputChange, onCreateGroup } = generateMockData()
    const inputValueMock = 'Input Value Mock'

    const { getByText } = customRender(
      <NewGroupModalTemplate
        groupExists={false}
        handleConfirm={handleConfirm}
        handleInputChange={handleInputChange}
        handleOnClose={handleOnClose}
        inputValue={inputValueMock}
        onCreateGroup={onCreateGroup}
        title="Title Test"
        visibility
      />
    )

    expect(getByText('Title Test')).toBeInTheDocument()
  })

  it('should render input value correctly', () => {
    const { handleOnClose, handleConfirm, handleInputChange, onCreateGroup } = generateMockData()

    const placeholderMock = 'Placeholder Mock'
    const inputValueMock = 'Input Value Mock'

    const { getByPlaceholderText } = customRender(
      <NewGroupModalTemplate
        groupExists={false}
        handleConfirm={handleConfirm}
        handleInputChange={handleInputChange}
        handleOnClose={handleOnClose}
        inputValue={inputValueMock}
        placeholder={placeholderMock}
        onCreateGroup={onCreateGroup}
        title="Title Test"
        visibility
      />
    )

    const placeholder = getByPlaceholderText(placeholderMock) as HTMLInputElement

    expect(placeholder.value).toBe(inputValueMock)
  })

  it('should show am error label when the group exists', () => {
    const { handleOnClose, handleConfirm, handleInputChange, onCreateGroup } = generateMockData()

    const { getByTestId } = customRender(
      <NewGroupModalTemplate
        groupExists
        handleConfirm={handleConfirm}
        handleInputChange={handleInputChange}
        handleOnClose={handleOnClose}
        inputValue="Input Value Test"
        onCreateGroup={onCreateGroup}
        title="Title Test"
        visibility
      />
    )

    expect(getByTestId('new-group-group-exist')).toBeInTheDocument()
  })

  it('should not show an error label when the group does not exist', () => {
    const { handleOnClose, handleConfirm, handleInputChange, onCreateGroup } = generateMockData()

    const { queryByTestId } = customRender(
      <NewGroupModalTemplate
        groupExists={false}
        handleConfirm={handleConfirm}
        handleInputChange={handleInputChange}
        handleOnClose={handleOnClose}
        inputValue="Input Value Test"
        onCreateGroup={onCreateGroup}
        title="Title Test"
        visibility
      />
    )

    expect(queryByTestId('new-group-group-exist')).not.toBeInTheDocument()
  })

  it('should render buttons wrapper correctly', () => {
    const { handleOnClose, handleConfirm, handleInputChange, onCreateGroup } = generateMockData()

    const inputValueMock = 'Input Value Mock'

    const { getByTestId } = customRender(
      <NewGroupModalTemplate
        groupExists={false}
        handleConfirm={handleConfirm}
        handleInputChange={handleInputChange}
        handleOnClose={handleOnClose}
        inputValue={inputValueMock}
        onCreateGroup={onCreateGroup}
        title="Title Test"
        visibility
      />
    )

    expect(getByTestId('new-group-buttons')).toBeInTheDocument()
  })
})
