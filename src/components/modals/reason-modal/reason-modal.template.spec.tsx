import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import { ReasonModalTemplate } from './reason-modal.template'
import { translate } from './reason-modal.translate'

function generateMockData() {
  const onClose = vi.fn()
  const onConfirm = vi.fn()
  const onChange = vi.fn()

  const mockProps = {
    show: true,
    textareaDisabled: true,
    buttonDisabled: true,
    title: 'Test Modal',
    label: 'Label Modal',
    value: 'Input Value',
    maxLength: 100,
    minLength: 10,
    onClose,
    onConfirm,
    onChange,
  }

  return { mockProps }
}

describe('Reason Modal Template', () => {
  it('should be able to render Reason Modal', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<ReasonModalTemplate {...mockProps} />)

    expect(getByTestId('reason-modal')).toBeInTheDocument()
  })

  it('should be able to render Reason Modal header with correct title', () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByText } = customRender(<ReasonModalTemplate {...mockProps} />)

    const header = getByTestId('reason-modal-header')
    const title = getByText(mockProps.title)

    expect(header).toBeInTheDocument()
    expect(title).toBeInTheDocument()
    expect(header.querySelector('h3')?.textContent).toBe(mockProps.title)
  })

  it('should be able to render Reason Modal body', () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByDisplayValue } = customRender(<ReasonModalTemplate {...mockProps} />)

    const body = getByTestId('group-modal-body')
    const input = getByDisplayValue(mockProps.value)

    expect(body).toBeInTheDocument()
    expect(input).toBeInTheDocument()
  })

  it('should be able to render Reason Modal footer with correct buttons', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<ReasonModalTemplate {...mockProps} />)

    const footer = getByTestId('reason-modal-footer')
    const cancelButton = getByTestId('reason-modal-cancel-button')
    const primaryButton = getByTestId('reason-modal-primary-button')

    expect(footer).toBeInTheDocument()
    expect(cancelButton).toBeInTheDocument()
    expect(primaryButton).toBeInTheDocument()
  })

  it('should render Reason Modal with correct label', () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(<ReasonModalTemplate {...mockProps} />)

    const label = getByText(mockProps.label)

    expect(label).toBeInTheDocument()
  })

  it('should render Reason Modal with correct placeholder', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<ReasonModalTemplate {...mockProps} placeholder="Input Value" />)

    const input = getByTestId('reason-modal-input')

    expect(input).toHaveAttribute('placeholder', 'Input Value')
  })

  it('should render Reason Modal with length text', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<ReasonModalTemplate {...mockProps} />)

    const lengthText = getByTestId('reason-modal-length')

    expect(lengthText).toHaveTextContent(
      `** ${translate.reasonLength({ minLength: mockProps.minLength, maxLength: mockProps.maxLength })}`
    )
  })

  it('should render Reason Modal with length title in the save button', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<ReasonModalTemplate {...mockProps} />)

    const primaryButton = getByTestId('reason-modal-primary-button')

    expect(primaryButton).toHaveAttribute(
      'title',
      translate.reasonLength({ minLength: mockProps.minLength, maxLength: mockProps.maxLength })
    )
  })
})
