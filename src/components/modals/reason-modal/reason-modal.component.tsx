import { useEffect, useState } from 'react'
import { allowedCharactersRegex, repeatedSpecialCharactersRegex, repeatedTwiceCharactersRegex } from 'utils'
import { ReasonModalTemplate } from './reason-modal.template'
import { TReasonModal } from './reason-modal.types'

export function ReasonModal(props: TReasonModal) {
  const { show, title, label, placeholder, maxLength, minLength, inputName, width, onClose, onConfirm } = props

  const [value, setValue] = useState('')
  const [disabled, setDisabled] = useState(false)

  const valueSanitized = value
    .replaceAll(allowedCharactersRegex, ' ')
    .replaceAll(repeatedSpecialCharactersRegex, '$1')
    .replaceAll(repeatedTwiceCharactersRegex, '$1$1')
    .trim()
  const buttonDisabled = !valueSanitized || valueSanitized.length < minLength || valueSanitized.length > maxLength

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setValue(event.target.value)
  }

  const handleConfirm = () => {
    setDisabled(true)
    return onConfirm(valueSanitized)
  }

  useEffect(() => {
    setValue('')
    setDisabled(false)
  }, [show])

  return (
    <ReasonModalTemplate
      show={show}
      buttonDisabled={buttonDisabled || disabled}
      textareaDisabled={disabled}
      title={title}
      label={label}
      placeholder={placeholder}
      inputName={inputName}
      width={width}
      onClose={onClose}
      onConfirm={handleConfirm}
      onChange={handleChange}
      value={value}
      maxLength={maxLength}
      minLength={minLength}
    />
  )
}
