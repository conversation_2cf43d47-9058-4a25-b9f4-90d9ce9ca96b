import { userEvent } from '@testing-library/user-event'
import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import { ReasonModal } from './reason-modal.component'

function generateMockData() {
  const onClose = vi.fn()
  const onConfirm = vi.fn()

  const mockProps = {
    show: true,
    disabled: true,
    title: 'Test Modal',
    label: 'Label Modal',
    maxLength: 100,
    minLength: 10,
    onClose,
    onConfirm,
  }

  return { mockProps }
}

describe('Edit Modal', () => {
  it('should click confirm button passing input value greater than minLength', async () => {
    const { mockProps } = generateMockData()
    const { getByTestId, getByRole } = customRender(<ReasonModal {...mockProps} minLength={0} />)
    const confirmButton = getByTestId('reason-modal-primary-button')

    await userEvent.type(getByRole('textbox'), 'new-value')

    expect(confirmButton).toBeEnabled()

    await userEvent.click(confirmButton)

    expect(mockProps.onConfirm).toBeCalledTimes(1)
    expect(mockProps.onConfirm).toBeCalledWith('new-value')
  })

  it('should disable confirm button if input value is less than minLength', async () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<ReasonModal {...mockProps} minLength={10} />)
    const confirmButton = getByTestId('reason-modal-primary-button')

    expect(confirmButton).toBeDisabled()
  })

  it('should disable confirm button if input value is greater than maxLength', async () => {
    const { mockProps } = generateMockData()
    const { getByTestId, getByRole } = customRender(<ReasonModal {...mockProps} maxLength={10} />)
    const confirmButton = getByTestId('reason-modal-primary-button')

    await userEvent.type(getByRole('textbox'), 'new-value')

    expect(confirmButton).toBeDisabled()
  })

  it("should disable confirm button if input doesn't have value", async () => {
    const { mockProps } = generateMockData()
    const { getByTestId } = customRender(<ReasonModal {...mockProps} />)
    const confirmButton = getByTestId('reason-modal-primary-button')

    expect(confirmButton).toBeDisabled()

    await userEvent.click(confirmButton)

    expect(mockProps.onConfirm).toBeCalledTimes(0)
  })

  it('should be able to close a modal when user click on cancel button', async () => {
    const { mockProps } = generateMockData()

    const { getByRole } = customRender(<ReasonModal {...mockProps} />)

    await userEvent.click(getByRole('button', { name: 'Cancel' }))

    expect(mockProps.onClose).toBeCalledTimes(1)
  })

  it('should be able to close a modal when user click on close button', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<ReasonModal {...mockProps} />)

    await userEvent.click(getByTestId('close-button'))

    expect(mockProps.onClose).toBeCalledTimes(1)
  })
})
