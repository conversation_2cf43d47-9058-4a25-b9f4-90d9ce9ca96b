import { theme, BaseModal, Buttons, Inputs } from '@mz-codes/design-system'

import { TReasonModalTemplate } from './reason-modal.types'
import { translate } from './reason-modal.translate'

export function ReasonModalTemplate(props: TReasonModalTemplate) {
  const {
    show,
    buttonDisabled,
    textareaDisabled,
    title,
    label,
    placeholder,
    inputName,
    onClose,
    onConfirm,
    onChange,
    value,
    maxLength,
    minLength,
    width = '600px',
  } = props
  return (
    <BaseModal show={show} onClose={onClose} width={width} data-testid="reason-modal">
      <BaseModal.Header data-testid="reason-modal-header">
        <BaseModal.Title>{title}</BaseModal.Title>
      </BaseModal.Header>
      <BaseModal.Body data-testid="group-modal-body">
        <Inputs.Label>
          <Inputs.Text>{label}</Inputs.Text>
          <Inputs.TextArea
            data-testid="reason-modal-input"
            name={inputName ?? 'reason-modal-name-input'}
            value={value}
            placeholder={placeholder}
            onChange={onChange}
            maxLength={maxLength}
            minLength={minLength}
            disabled={textareaDisabled}
            style={{
              height: 'unset',
              borderTop: 'unset',
              borderLeft: 'unset',
              borderRight: 'unset',
            }}
          />
          <Inputs.Text
            style={{
              color: theme.legacy.colors.grayScale.texts,
              padding: '16px 0',
            }}
            data-testid="reason-modal-length"
          >
            ** {translate.reasonLength({ minLength, maxLength })}
          </Inputs.Text>
        </Inputs.Label>
      </BaseModal.Body>
      <BaseModal.Footer data-testid="reason-modal-footer">
        <BaseModal.ButtonWrapper>
          <Buttons.Cancel data-testid="reason-modal-cancel-button" onClick={onClose}>
            {translate.cancelButton}
          </Buttons.Cancel>
          <Buttons.Primary
            data-testid="reason-modal-primary-button"
            onClick={onConfirm}
            disabled={buttonDisabled}
            title={buttonDisabled ? translate.reasonLength({ minLength, maxLength }) : ''}
          >
            {translate.saveButton}
          </Buttons.Primary>
        </BaseModal.ButtonWrapper>
      </BaseModal.Footer>
    </BaseModal>
  )
}
