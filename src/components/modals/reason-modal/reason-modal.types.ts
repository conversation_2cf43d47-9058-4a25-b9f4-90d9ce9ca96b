export type TReasonModal = {
  show: boolean
  title: string
  label: string
  minLength: number
  maxLength: number
  placeholder?: string
  inputName?: string
  width?: string
  onClose(): void
  onConfirm(reason: string): void
}

export type TReasonModalTemplate = {
  show: boolean
  buttonDisabled: boolean
  textareaDisabled: boolean
  title: string
  label: string
  minLength: number
  maxLength: number
  placeholder?: string
  inputName?: string
  width?: string
  onClose(): void
  onConfirm(): void
  value: string
  onChange(event: React.ChangeEvent<HTMLTextAreaElement>): void
}
