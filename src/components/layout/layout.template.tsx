import { i18n } from 'translate'
import { PageWrapper, SideMenu } from 'components'
import { Outlet } from 'react-router-dom'
import { lazy, Suspense } from 'react'
import { Loading } from '@mz-codes/design-system'

const NavBar = lazy(async () => import('@mz-mfe-navbar/app'))

export function Layout() {
  return (
    <>
      <Suspense fallback={<Loading />}>
        <NavBar isSubDomain />
      </Suspense>
      <PageWrapper>
        <SideMenu title={i18n.t('sideMenu.shareholders')} />
        <PageWrapper.Screen id="shareholdersUI">
          <Outlet />
        </PageWrapper.Screen>
      </PageWrapper>
    </>
  )
}
