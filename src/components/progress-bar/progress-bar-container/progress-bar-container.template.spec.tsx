import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { ProgressBarContainer } from './progress-bar-container.template'

describe('Progress Bar Container Component', () => {
  it('should render progress bar container correctly', () => {
    const { getByTestId } = customRender(<ProgressBarContainer data-testid="container" />)

    const container = getByTestId('container')

    expect(container).toBeInTheDocument()
  })

  it('should render progress bar container correctly on hover', () => {
    const { getByTestId } = customRender(<ProgressBarContainer data-testid="container" />)

    const container = getByTestId('container')

    expect(container).toHaveStyleRule('opacity', '0.8', { modifier: ':hover' })
  })
})
