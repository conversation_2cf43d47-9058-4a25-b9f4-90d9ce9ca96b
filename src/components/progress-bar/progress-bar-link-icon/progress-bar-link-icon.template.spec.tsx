import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { ProgressBarLinkIcon } from './progress-bar-link-icon.template'

describe('Progress Bar Link Icon', () => {
  it('should be able to render link icon correctly', () => {
    const { getByTestId } = customRender(<ProgressBarLinkIcon data-testid="link-icon" />)

    const linkIcon = getByTestId('link-icon')

    expect(linkIcon).toBeInTheDocument()
  })
})
