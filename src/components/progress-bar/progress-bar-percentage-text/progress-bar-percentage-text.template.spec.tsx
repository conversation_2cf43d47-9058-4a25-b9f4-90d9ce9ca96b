import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { ProgressBarPercentageText } from './progress-bar-percentage-text.template'

describe('Progress Bar Percentage Text Component', () => {
  it('should render progress bar percentage text correctly', () => {
    const { getByTestId } = customRender(<ProgressBarPercentageText data-testid="percentage-text" />)

    const percentageText = getByTestId('percentage-text')

    expect(percentageText).toBeInTheDocument()
  })
})
