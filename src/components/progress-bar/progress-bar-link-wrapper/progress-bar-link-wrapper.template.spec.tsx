import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { ProgressBarLinkWrapper } from './progress-bar-link-wrapper.template'

describe('Progress Bar Link Wrapper Component', () => {
  it('should render progress bar link wrapper correctly', () => {
    const { getByTestId } = customRender(<ProgressBarLinkWrapper data-testid="link-wrapper" />)

    const linkWrapper = getByTestId('link-wrapper')

    expect(linkWrapper).toBeInTheDocument()
  })
})
