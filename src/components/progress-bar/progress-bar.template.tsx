import { Link } from 'react-router-dom'
import { i18n } from 'translate'

import { generateUniqueId } from 'utils'

import { ProgressBarContainer } from './progress-bar-container'
import { ProgressBarLinkWrapper } from './progress-bar-link-wrapper'
import { ProgressBarPercentageText } from './progress-bar-percentage-text'
import { ProgressBarProgressPoint } from './progress-bar-progress-point'
import { ProgressBarProgressWrapper } from './progress-bar-progress-wrapper'

import { TProgressBarTemplate } from './progress-bar.types'
import { ProgressBarLinkIcon } from './progress-bar-link-icon'

export function ProgressBarTemplate(props: TProgressBarTemplate) {
  const { path, progress, barPointers, barProgress } = props

  return (
    <Link to={path}>
      <ProgressBarTemplate.Container>
        <ProgressBarTemplate.PercentageText>{`${progress}% ${i18n.t('pageSmartGrouping.grouped')}`}</ProgressBarTemplate.PercentageText>
        <ProgressBarTemplate.ProgressWrapper>
          {barPointers.map((_, index) =>
            index < barProgress ? (
              <ProgressBarTemplate.ProgressPoint
                key={generateUniqueId()}
                $indexOpacity={index / barProgress}
                data-testid={`filled-pointer-${index}`}
              />
            ) : (
              <ProgressBarTemplate.ProgressPoint
                key={generateUniqueId()}
                $indexOpacity={1}
                $darkPointer
                data-testid={`empty-pointer-${index}`}
              />
            )
          )}
          <ProgressBarTemplate.LinkWrapper data-testid="link-wrapper">
            <ProgressBarTemplate.LinkIcon />
          </ProgressBarTemplate.LinkWrapper>
        </ProgressBarTemplate.ProgressWrapper>
      </ProgressBarTemplate.Container>
    </Link>
  )
}

ProgressBarTemplate.Container = ProgressBarContainer
ProgressBarTemplate.LinkWrapper = ProgressBarLinkWrapper
ProgressBarTemplate.LinkIcon = ProgressBarLinkIcon
ProgressBarTemplate.ProgressWrapper = ProgressBarProgressWrapper
ProgressBarTemplate.PercentageText = ProgressBarPercentageText
ProgressBarTemplate.ProgressPoint = ProgressBarProgressPoint
