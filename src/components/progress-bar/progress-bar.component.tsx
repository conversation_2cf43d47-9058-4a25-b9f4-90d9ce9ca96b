import { ProgressBarTemplate } from './progress-bar.template'

import { TProgressBar } from './progress-bar.types'

export function ProgressBar(props: TProgressBar) {
  const { progress, barWidth, screenWidth, path } = props

  const progressBarWidth = screenWidth / (100 / barWidth)
  const pointsOnProgressBar = Math.floor(progressBarWidth / 9)
  const barProgress = (pointsOnProgressBar / 100) * progress
  const barPointers = Array(pointsOnProgressBar).fill(null)

  return (
    <ProgressBarTemplate
      data-testid="progress-bar"
      barPointers={barPointers}
      barProgress={barProgress}
      path={path}
      progress={progress}
    />
  )
}
