import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { ProgressBarProgressWrapper } from './progress-bar-progress-wrapper.template'

describe('Progress Bar Progress Wrapper Component', () => {
  it('should render progress bar progress wrapper correctly', () => {
    const { getByTestId } = customRender(<ProgressBarProgressWrapper data-testid="progress-wrapper" />)

    const progressWrapper = getByTestId('progress-wrapper')

    expect(progressWrapper).toBeInTheDocument()
  })
})
