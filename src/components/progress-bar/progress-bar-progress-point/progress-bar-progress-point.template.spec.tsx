import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { theme } from '@mz-codes/design-system'
import { ProgressBarProgressPoint } from './progress-bar-progress-point.template'

describe('Progress Bar Progress Point Component', () => {
  it('should render progress bar progress point correctly', () => {
    const { getByTestId } = customRender(<ProgressBarProgressPoint data-testid="progress-point" $indexOpacity={1} />)

    const percentageText = getByTestId('progress-point')

    expect(percentageText).toBeInTheDocument()
    expect(percentageText).toHaveStyleRule('opacity', '1')
    expect(percentageText).toHaveStyleRule('background-color', theme.legacy.colors.primary.primary)
  })

  it('should render progress bar progress point correctly', () => {
    const { getByTestId } = customRender(
      <ProgressBarProgressPoint data-testid="progress-point" $indexOpacity={0.8} $darkPointer />
    )

    const percentageText = getByTestId('progress-point')

    expect(percentageText).toBeInTheDocument()
    expect(percentageText).toHaveStyleRule('opacity', '0.8')
    expect(percentageText).toHaveStyleRule('background-color', theme.legacy.colors.neutral.contentBackground)
  })
})
