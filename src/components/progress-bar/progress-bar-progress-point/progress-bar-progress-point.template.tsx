import { theme } from '@mz-codes/design-system'
import styled from 'styled-components'

export const ProgressBarProgressPoint = styled.div<{ $indexOpacity: number; $darkPointer?: boolean }>`
  background-color: ${({ $darkPointer }) =>
    $darkPointer ? theme.legacy.colors.neutral.contentBackground : theme.legacy.colors.primary.primary};
  width: 5px;
  height: 23px;

  margin-right: 2px;
  border-radius: 5px;

  opacity: ${({ $indexOpacity }) => $indexOpacity};
`
