import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { theme } from '@mz-codes/design-system'
import { ProgressBarTemplate } from './progress-bar.template'

function generateMockData() {
  const mockProps = {
    progress: 58,
    barPointers: [null, null, null, null, null, null, null, null, null, null, null, null],
    barProgress: 6,
    path: 'summary/smartGrouping',
  }

  return { mockProps }
}

describe('Progress Bar Progress Template', () => {
  it('should render progress bar progress correctly', () => {
    const { mockProps } = generateMockData()

    const { getByRole, getByText, getByTestId } = customRender(
      <BrowserRouter>
        <ProgressBarTemplate {...mockProps} />
      </BrowserRouter>
    )

    const path = getByRole('link')
    const label = getByText('58% grouped')
    const linkWrapper = getByTestId('link-wrapper')

    expect(path).toBeInTheDocument()
    expect(label).toBeInTheDocument()
    expect(linkWrapper).toBeInTheDocument()
  })

  it('should render the styles for filled and empty pointers correctly', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(
      <BrowserRouter>
        <ProgressBarTemplate {...mockProps} />
      </BrowserRouter>
    )

    const filledPointer = getByTestId('filled-pointer-3')
    const emptyPointer = getByTestId('empty-pointer-8')

    expect(filledPointer).toHaveStyleRule('opacity', '0.5')
    expect(filledPointer).toHaveStyleRule('background-color', theme.legacy.colors.primary.primary)
    expect(emptyPointer).toHaveStyleRule('opacity', '1')
    expect(emptyPointer).toHaveStyleRule('background-color', theme.legacy.colors.neutral.contentBackground)
  })
})
