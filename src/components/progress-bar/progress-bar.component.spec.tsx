import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { MemoryRouter, Routes, Route } from 'react-router-dom'
import userEvent from '@testing-library/user-event'
import { ProgressBar } from './progress-bar.component'

function generateMockData() {
  const mockProps = {
    progress: 58,
    barWidth: 400,
    screenWidth: 600,
    path: '/summary/smartGrouping',
  }

  return { mockProps }
}

describe('Progress Bar Progress Component', () => {
  it('should render progress bar progress correctly', () => {
    const { mockProps } = generateMockData()

    const { getByRole, getByText, getByTestId } = customRender(
      <MemoryRouter>
        <ProgressBar {...mockProps} />
      </MemoryRouter>
    )

    const path = getByRole('link')
    const label = getByText('58% grouped')
    const linkWrapper = getByTestId('link-wrapper')

    expect(path).toHaveAttribute('href', mockProps.path)
    expect(label).toBeInTheDocument()
    expect(linkWrapper).toBeInTheDocument()
  })

  it('should navigate to correct path when clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId, getByText } = customRender(
      <MemoryRouter initialEntries={['/']}>
        <Routes>
          <Route path={mockProps.path} element={<div>Target Page</div>} />
          <Route path="*" element={<ProgressBar {...mockProps} />} />
        </Routes>
      </MemoryRouter>
    )

    const linkWrapper = getByTestId('link-wrapper')
    await userEvent.click(linkWrapper)
    expect(getByText('Target Page')).toBeInTheDocument()
  })
})
