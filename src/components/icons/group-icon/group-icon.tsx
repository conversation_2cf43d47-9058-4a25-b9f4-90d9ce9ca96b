import styled from 'styled-components'
import icoAglutinar from 'assets/png/ico_aglutinar.png'
import { TGroupIcon } from './group-icon.types'

export const GroupIcon = styled.img.attrs<TGroupIcon>((props) => ({
  src: icoAglutinar,
  alt: 'Agrupar',
  width: props.size || 16,
  height: props.size || 16,
}))<TGroupIcon>`
  opacity: ${({ opacity = 0.8 }) => opacity};
  transition: opacity 0.2s ease;
  filter: brightness(0) invert(1);
  cursor: ${({ onClick }) => (onClick ? 'pointer' : 'default')};

  &:hover {
    opacity: ${({ onClick }) => (onClick ? 1 : 'inherit')};
  }
`
