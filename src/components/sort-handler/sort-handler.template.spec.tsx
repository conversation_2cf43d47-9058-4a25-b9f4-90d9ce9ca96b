import { describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import { SortHandlerTemplate } from './sort-handler.template'

describe('Sort Handler Template', () => {
  it('should render children and SortCaretDownIcon with correct props', () => {
    const mockOnSort = vi.fn()
    const mockChildren = 'Button Text'
    const mockDisabled = false
    const mockSortedDirection = 'desc'

    const { getByText, container } = customRender(
      <SortHandlerTemplate onSort={mockOnSort} disabled={mockDisabled} sortedDirection={mockSortedDirection}>
        {mockChildren}
      </SortHandlerTemplate>
    )

    const sortHandlerTemplate = getByText(mockChildren)
    expect(sortHandlerTemplate).toBeInTheDocument()

    const caretDownIcon = container.lastChild
    expect(caretDownIcon).toBeInTheDocument()

    expect(sortHandlerTemplate).not.toHaveAttribute('disabled')
  })

  it('should render disabled button when disabled prop is true', () => {
    const mockOnSort = vi.fn()
    const mockChildren = 'Button Text'
    const mockDisabled = true
    const mockSortedDirection = 'desc'

    const { getByText } = customRender(
      <SortHandlerTemplate onSort={mockOnSort} disabled={mockDisabled} sortedDirection={mockSortedDirection}>
        {mockChildren}
      </SortHandlerTemplate>
    )

    const sortHandlerTemplate = getByText(mockChildren)
    expect(sortHandlerTemplate).toHaveAttribute('disabled')
  })
})
