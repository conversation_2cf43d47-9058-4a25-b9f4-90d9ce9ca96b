import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { SortCaretDownIcon } from './sort-handler-caret-down.template'

describe('Sort Caret Down Icon Styled Component', () => {
  it('should apply transition and transform properties based on $sortedDirection prop', () => {
    const { container, rerender } = customRender(<SortCaretDownIcon $sortedDirection="asc" />)
    expect(container.firstChild).toHaveStyleRule('transition: .25s')
    expect(container.firstChild).toHaveStyleRule('transform: rotate(180deg)')

    rerender(<SortCaretDownIcon $sortedDirection="desc" />)
    expect(container.firstChild).toHaveStyleRule('transition: .25s')
    expect(container.firstChild).toHaveStyleRule('transform: none')
  })
})
