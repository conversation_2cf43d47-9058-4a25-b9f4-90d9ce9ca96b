import { ReactNode, RefObject } from 'react'

export type TSortedDirection = 'asc' | 'desc'

export type TSortHandler = {
  sortedDirection?: TSortedDirection
  onClick(): Promise<void> | void
  children?: ReactNode
}

export type TSortHandlerTemplate = {
  onSort(): void
  children?: ReactNode
  ref: RefObject<HTMLDivElement>
  sortedDirection: TSortedDirection
  disabled: boolean
}

export type TSortCaretDownIcon = {
  $sortedDirection: TSortedDirection
}
