import { useState } from 'react'
import { SortHandlerTemplate } from './sort-handler.template'
import { TSortHandler, TSortedDirection } from './sort-handler.types'

export function SortHandler(props: TSortHandler) {
  const { children, onClick, sortedDirection = 'desc' } = props

  const [sortDirection, setSortDirection] = useState<TSortedDirection>(sortedDirection)
  const [disabled, setDisabled] = useState(false)

  const onSort = async () => {
    const newDirection = sortDirection === 'asc' ? 'desc' : 'asc'

    setSortDirection(newDirection)

    const isAPromise = onClick()

    if (isAPromise) {
      setDisabled(true)
      await Promise.resolve(isAPromise)
      setDisabled(false)
    }
  }

  return (
    <SortHandlerTemplate sortedDirection={sortDirection} onSort={onSort} disabled={disabled}>
      {children}
    </SortHandlerTemplate>
  )
}
