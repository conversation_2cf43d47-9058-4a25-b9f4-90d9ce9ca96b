import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { theme } from '@mz-codes/design-system'
import { SortHandlerContainer } from './sort-handler-container.template'

describe('Sort Handler Container Styled Component', () => {
  it('should render with correct styles', () => {
    const { container } = customRender(<SortHandlerContainer />)
    const button = container.firstChild

    expect(button).toBeInTheDocument()
  })

  it('should apply disabled styles when disabled', () => {
    const { container, rerender } = customRender(<SortHandlerContainer />)
    const button = container.firstChild

    rerender(<SortHandlerContainer disabled />)
    expect(button).toHaveStyle(`
      color: ${theme.legacy.colors.grayScale.btnDisabled};
      cursor: not-allowed;
    `)
  })
})
