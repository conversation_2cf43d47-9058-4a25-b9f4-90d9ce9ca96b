import styled, { css } from 'styled-components'

export const SortHandlerContainer = styled.button((props) => {
  const { theme } = props
  return css`
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 8px;
    background: transparent;
    border: 0;
    color: ${theme.legacy.colors.primary.primary};
    font-size: 14px;
    font-weight: 700;
    &:disabled {
      color: ${theme.legacy.colors.grayScale.btnDisabled};
      cursor: not-allowed;
    }
  `
})
