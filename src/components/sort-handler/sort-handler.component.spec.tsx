import { customRender } from 'test'
import { describe, expect, it, vi } from 'vitest'
import userEvent from '@testing-library/user-event'
import { SortHandler } from './sort-handler.component'

describe('Sort Handler Component', () => {
  it('should be able to handle onClick function when button is clicked', async () => {
    const onClick = vi.fn(() => Promise.resolve())
    const initialText = 'Render me'

    const { getByText } = customRender(<SortHandler onClick={onClick}>{initialText}</SortHandler>)

    const sortHandlerComponent = getByText(initialText)

    await userEvent.click(sortHandlerComponent)

    expect(onClick).toHaveBeenCalledOnce()
  })

  it('should set sort direction correctly when the button is clicked', async () => {
    const onClickMock = vi.fn(() => Promise.resolve())
    const initialText = 'Render me'

    const { getByText } = customRender(
      <SortHandler onClick={onClickMock} sortedDirection="desc">
        {initialText}
      </SortHandler>
    )

    const sortHandlerComponent = getByText(initialText)

    await userEvent.click(sortHandlerComponent)

    expect(onClickMock).toHaveBeenCalledOnce()
  })
})
