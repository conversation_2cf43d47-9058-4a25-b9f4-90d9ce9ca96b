import { forwardRef } from 'react'
import { SortHandlerContainer } from './sort-handler-container/sort-handler-container.template'
import { TSortHandlerTemplate } from './sort-handler.types'
import { SortCaretDownIcon } from './sort-handler-caret-down'

export function BaseSortHandlerTemplate(props: TSortHandlerTemplate, ref: React.Ref<HTMLButtonElement>) {
  const { onSort, children, disabled, sortedDirection } = props
  return (
    <BaseSortHandlerTemplate.Container disabled={disabled} ref={ref} onClick={onSort}>
      {children}
      <BaseSortHandlerTemplate.CaretDownIcon size={12} $sortedDirection={sortedDirection} />
    </BaseSortHandlerTemplate.Container>
  )
}

BaseSortHandlerTemplate.Container = SortHandlerContainer
BaseSortHandlerTemplate.CaretDownIcon = SortCaretDownIcon

export const SortHandlerTemplate = forwardRef(BaseSortHandlerTemplate)
