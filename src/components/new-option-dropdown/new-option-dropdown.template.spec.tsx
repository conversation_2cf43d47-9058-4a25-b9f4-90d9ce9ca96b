import { describe, it, expect, vi } from 'vitest'
import { customRender } from 'test'
import { NewOptionDropdownTemplate } from './new-option-dropdown.template'

function generateMockData() {
  const createNewOption = vi.fn()
  const handleSelectChange = vi.fn()
  const handleMenuOpen = vi.fn()
  const handleInputChange = vi.fn()

  const mockProps = {
    selectWrapperRef: { current: null },
    selectRef: { current: null },
    handleSelectChange,
    menuIsOpen: false,
    handleMenuOpen,
    value: null,
    styles: {},
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
    ],
    disabled: false,
    loading: false,
    className: 'new-option-dropdown',
    closeMenuOnSelect: true,
    inputValue: '',
    handleInputChange,
    placeholder: 'Select...',
    defaultValue: null,
    createNewOption,
  }

  return { mockProps }
}

describe('NewOptionDropdownTemplate', () => {
  it('renders the component with placeholder', () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(<NewOptionDropdownTemplate {...mockProps} />)

    expect(getByText('Select...')).toBeInTheDocument()
  })

  it('displays loading indicator when loading', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<NewOptionDropdownTemplate {...mockProps} loading />)

    const loadingIndicator = getByTestId('loading-spinner')

    expect(loadingIndicator).toBeInTheDocument()
  })

  it('should render the options correctly', async () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(<NewOptionDropdownTemplate {...mockProps} menuIsOpen />)

    const text = getByText('Option 1')

    expect(text).toBeInTheDocument()
  })

  it('should render with the disabled class correctly', async () => {
    const { mockProps } = generateMockData()

    const { container } = customRender(<NewOptionDropdownTemplate {...mockProps} disabled />)
    const dropdown = container.querySelector('.select--is-disabled')

    expect(dropdown).toBeInTheDocument()
  })
})
