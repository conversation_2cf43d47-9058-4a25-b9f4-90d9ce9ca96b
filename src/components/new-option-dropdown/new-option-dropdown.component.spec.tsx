import { describe, it, expect, vi } from 'vitest'
import { waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { customRender } from 'test'
import { NewOptionDropdown } from './new-option-dropdown.component'

function generateMockData() {
  const handleInputChange = vi.fn()
  const handleChange = vi.fn()
  const handleNewOption = vi.fn()

  const mockProps = {
    value: null,
    styles: {},
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
    ],
    disabled: false,
    loading: false,
    menuOpen: false,
    className: 'new-option-dropdown',
    closeMenuOnSelect: true,
    inputValue: '',
    handleInputChange,
    handleChange,
    placeholder: 'Select...',
    newOptionLabel: 'Create option',
    handleNewOption,
    defaultValue: null,
  }

  return { mockProps }
}

describe('NewOptionDropdown', () => {
  it('calls handleInputChange when input value changes', async () => {
    const { mockProps } = generateMockData()

    const { getByRole } = customRender(<NewOptionDropdown {...mockProps} />)

    await userEvent.type(getByRole('combobox'), 'new-value')
    expect(mockProps.handleInputChange).toHaveBeenCalled()
  })

  it('calls handleChange when an option is selected', async () => {
    const { mockProps } = generateMockData()

    const { getByRole, getByText } = customRender(<NewOptionDropdown {...mockProps} />)

    await userEvent.click(getByRole('combobox'))
    await userEvent.click(getByText('Option 1'))

    expect(mockProps.handleChange).toHaveBeenCalledWith({ value: 'option1', label: 'Option 1' })
  })

  it('calls handleOnCreateNewOption when new option is created', async () => {
    const { mockProps } = generateMockData()

    const handleNewOption = vi.fn()

    const { getByText } = customRender(
      <NewOptionDropdown {...mockProps} handleNewOption={handleNewOption} inputValue="New Option" menuOpen />
    )
    await userEvent.click(getByText(/New Option/))

    await waitFor(() => expect(handleNewOption).toHaveBeenCalledWith('New Option'))
  })

  it('closes the dropdown when clicking outside', async () => {
    const { mockProps } = generateMockData()

    const { container } = customRender(<NewOptionDropdown {...mockProps} menuOpen />)

    await userEvent.click(container)
    await waitFor(() => expect(mockProps.menuOpen).toBe(false))
  })

  it('closes the dropdown when an option is selected', async () => {
    const { mockProps } = generateMockData()

    const { getByText } = customRender(<NewOptionDropdown {...mockProps} menuOpen />)

    await userEvent.click(getByText('Option 1'))
    await waitFor(() => expect(mockProps.menuOpen).toBe(false))
  })

  it('does nothing when clicking outside if dropdown is closed', async () => {
    const { mockProps } = generateMockData()

    const { container } = customRender(<NewOptionDropdown {...mockProps} />)

    await userEvent.click(container)
    expect(mockProps.menuOpen).toBe(false)
  })

  it('opens the dropdown when clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByRole, getByText } = customRender(<NewOptionDropdown {...mockProps} />)

    await userEvent.click(getByRole('combobox'))

    const option1 = getByText('Option 1')
    expect(option1).toBeInTheDocument()
  })

  it('does not open the dropdown when clicked if disabled', async () => {
    const { mockProps } = generateMockData()

    const { container } = customRender(<NewOptionDropdown {...mockProps} disabled />)
    const dropdown = container.querySelector('.select--is-disabled')

    expect(dropdown).toBeInTheDocument()

    expect(mockProps.menuOpen).toBe(false)
  })
})
