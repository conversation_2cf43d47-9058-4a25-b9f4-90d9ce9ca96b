import Select from 'react-select'

import { TNewOptionDropdownTemplate } from './new-option-dropdown.types'
import { NewOptionDropdownContainer } from './new-option-dropdown-container'
import { LoadingIndicator } from './new-option-dropdown-loading-indicator'

export function NewOptionDropdownTemplate(props: TNewOptionDropdownTemplate) {
  const {
    selectWrapperRef,
    selectRef,
    handleSelectChange,
    menuIsOpen,
    handleMenuOpen,
    value,
    styles,
    options,
    disabled,
    loading,
    className = 'new-option-dropdown',
    closeMenuOnSelect,
    inputValue,
    handleInputChange,
    placeholder,
    defaultValue,
    createNewOption,
    ...rest
  } = props

  return (
    <NewOptionDropdownContainer ref={selectWrapperRef} {...rest}>
      <Select
        data-testid="new-option-dropdown-container"
        ref={selectRef}
        value={value}
        styles={styles}
        options={options}
        isSearchable
        isDisabled={disabled}
        isLoading={loading}
        className={className}
        classNamePrefix="select"
        closeMenuOnSelect={closeMenuOnSelect}
        inputValue={inputValue}
        blurInputOnSelect
        onInputChange={handleInputChange}
        onChange={handleSelectChange}
        placeholder={placeholder}
        isClearable
        noOptionsMessage={createNewOption}
        defaultValue={defaultValue}
        menuIsOpen={menuIsOpen}
        onMenuOpen={handleMenuOpen}
        components={{ LoadingIndicator }}
      />
    </NewOptionDropdownContainer>
  )
}
