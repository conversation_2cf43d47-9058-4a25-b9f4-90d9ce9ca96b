import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { LoadingIndicator } from './new-option-dropdown-loading-indicator.template'

describe('New Option Loading Indicator', () => {
  it('should be render a loading indicator', () => {
    const { getByTestId } = customRender(<LoadingIndicator />)

    expect(getByTestId('loading')).toBeInTheDocument()
  })

  it('should be render a spinner inside the loading indicator', () => {
    const { getByTestId } = customRender(<LoadingIndicator />)

    expect(getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
