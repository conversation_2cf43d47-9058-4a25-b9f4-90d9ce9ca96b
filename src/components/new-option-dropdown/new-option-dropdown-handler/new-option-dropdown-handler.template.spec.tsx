import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { NewOptionDropdownHandler } from './new-option-dropdown-handler.template'

describe('New Option Dropdown Handler Container', () => {
  it('should be render a handler container correctly', () => {
    const { getByTestId } = customRender(<NewOptionDropdownHandler data-testid="handler" />)

    const handler = getByTestId('handler')

    expect(handler).toBeInTheDocument()
  })

  it('should be render a handler container correctly if is disabled', () => {
    const { getByTestId } = customRender(<NewOptionDropdownHandler data-testid="handler" />)

    const handler = getByTestId('handler')

    expect(handler).toHaveStyleRule('cursor', 'not-allowed', { modifier: ':disabled' })
  })
})
