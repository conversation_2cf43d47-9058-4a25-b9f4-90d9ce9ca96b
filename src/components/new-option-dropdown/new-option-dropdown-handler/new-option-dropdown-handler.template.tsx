import styled from 'styled-components'

import { NewOptionDropdownHandlerText } from '../new-option-dropdown-handler-text'
import { NewOptionDropdownHandlerPlusIcon } from '../new-option-dropdown-handler-plus-icon'

export const NewOptionDropdownHandler = styled.button`
  cursor: pointer;
  font-weight: 400;
  border: 0;
  background-color: transparent;
  padding: 0;
  width: 100%;
  padding: 8px;
  overflow: hidden;
  font-size: 12px;
  line-height: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &--is-focused {
    color: ${({ theme }) => theme.legacy.colors.primary.primary};
    background-color: transparent;
  }

  &:not(:disabled)&:hover {
    ${NewOptionDropdownHandlerText},
    ${NewOptionDropdownHandlerPlusIcon} {
      color: ${({ theme }) => theme.legacy.colors.primary.primary};
    }
  }

  &:disabled {
    cursor: not-allowed;
  }
`
