import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { NewOptionDropdownHandlerText } from './new-option-dropdown-handler-text.template'

describe('New Option Dropdown Handler Text', () => {
  it('should be render a handler text correctly', () => {
    const { getByTestId } = customRender(<NewOptionDropdownHandlerText data-testid="handler-text" />)

    const text = getByTestId('handler-text')

    expect(text).toBeInTheDocument()
  })
})
