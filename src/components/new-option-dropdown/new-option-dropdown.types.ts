import { TOption } from '@mz-codes/design-system'
import { GroupBase, PropsValue, SelectInstance, SingleValue, StylesConfig } from 'react-select'

export type TNewOptionDropdown = {
  value: SingleValue<TOption>
  styles?: StylesConfig<TOption, false, GroupBase<TOption>>
  options?: TOption[]
  disabled?: boolean
  loading?: boolean
  menuOpen?: boolean
  className?: string
  closeMenuOnSelect: boolean
  inputValue: string
  handleInputChange(newValue: string): void
  handleChange(selectedOption: SingleValue<TOption> | null): void
  placeholder: string
  newOptionLabel: string
  handleNewOption(inputValue: string): void
  defaultValue?: PropsValue<TOption>
}

export type TNewOptionDropdownTemplate = {
  selectWrapperRef: React.RefObject<HTMLDivElement>
  selectRef: React.RefObject<SelectInstance<TOption<string | number>>>
  handleSelectChange: (newValue: SingleValue<TOption>) => void
  menuIsOpen: boolean
  handleMenuOpen: () => void
  value: SingleValue<TOption>
  styles?: StylesConfig<TOption, false, GroupBase<TOption>>
  options?: TOption[]
  disabled?: boolean
  loading?: boolean
  className?: string
  closeMenuOnSelect: boolean
  inputValue: string
  handleInputChange(newValue: string): void
  placeholder: string
  defaultValue?: PropsValue<TOption>
  createNewOption: () => '' | JSX.Element
}
