import { useCallback, useRef, useState } from 'react'
import { SelectInstance, SingleValue } from 'react-select'
import { TOption } from '@mz-codes/design-system'

import { useOutsideClick } from 'hooks/useOutsideClick'
import { TNewOptionDropdown } from './new-option-dropdown.types'
import { NewOptionDropdownTemplate } from './new-option-dropdown.template'
import { NewOptionDropdownHandler } from './new-option-dropdown-handler'
import { NewOptionDropdownHandlerLabel } from './new-option-dropdown-handler-label'
import { NewOptionDropdownHandlerPlusIcon } from './new-option-dropdown-handler-plus-icon'
import { NewOptionDropdownHandlerText } from './new-option-dropdown-handler-text'

export function NewOptionDropdown(props: TNewOptionDropdown) {
  const {
    value,
    styles,
    options,
    disabled,
    loading,
    menuOpen = false,
    className = 'new-option-dropdown',
    closeMenuOnSelect,
    inputValue,
    handleInputChange,
    handleChange,
    placeholder,
    newOptionLabel,
    handleNewOption,
    defaultValue,
    ...rest
  } = props

  const [menuIsOpen, setMenuIsOpen] = useState<boolean>(menuOpen)
  const selectWrapperRef = useRef<HTMLDivElement>(null)
  const selectRef = useRef<SelectInstance<TOption<string | number>>>(null)

  const createNewOption = () => {
    return (
      inputValue && (
        <NewOptionDropdownHandler onClick={() => handleOnCreateNewOption(inputValue)}>
          <NewOptionDropdownHandlerText>
            {inputValue}
            <NewOptionDropdownHandlerLabel>{newOptionLabel}</NewOptionDropdownHandlerLabel>
          </NewOptionDropdownHandlerText>
          <NewOptionDropdownHandlerPlusIcon>+</NewOptionDropdownHandlerPlusIcon>
        </NewOptionDropdownHandler>
      )
    )
  }

  const handleOnCreateNewOption = async (label: string) => {
    handleSelectChange({ value: '', label })
    handleCloseDropdown()

    await Promise.resolve(handleNewOption(label))

    if (selectRef.current) {
      selectRef.current.blur()
    }
  }

  const handleMenuOpen = useCallback(() => {
    setMenuIsOpen(true)
  }, [])

  const handleCloseDropdown = useCallback(() => {
    setMenuIsOpen(false)
  }, [])

  const handleSelectChange = useCallback(
    (newValue: SingleValue<TOption>) => {
      handleChange(newValue)
      handleCloseDropdown()
    },
    [handleChange, handleCloseDropdown]
  )

  useOutsideClick({
    ref: selectWrapperRef,
    callback: handleCloseDropdown,
  })

  return (
    <NewOptionDropdownTemplate
      data-testid="new-option-dropdown"
      createNewOption={createNewOption}
      closeMenuOnSelect={closeMenuOnSelect}
      handleInputChange={handleInputChange}
      handleMenuOpen={handleMenuOpen}
      handleSelectChange={handleSelectChange}
      inputValue={inputValue}
      menuIsOpen={menuIsOpen}
      placeholder={placeholder}
      selectRef={selectRef}
      selectWrapperRef={selectWrapperRef}
      value={value}
      className={className}
      defaultValue={defaultValue}
      disabled={disabled}
      loading={loading}
      options={options}
      styles={styles}
      {...rest}
    />
  )
}
