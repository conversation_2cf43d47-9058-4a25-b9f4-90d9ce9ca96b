import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { NewOptionDropdownHandlerPlusIcon } from './new-option-dropdown-handler-plus-icon.template'

describe('New Option Dropdown Handler Plus Icon', () => {
  it('should be render a handler plus icon correctly', () => {
    const { getByTestId } = customRender(<NewOptionDropdownHandlerPlusIcon data-testid="handler-plus-icon" />)

    const icon = getByTestId('handler-plus-icon')

    expect(icon).toBeInTheDocument()
  })
})
