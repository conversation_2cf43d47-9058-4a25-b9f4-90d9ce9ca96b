import { customRender } from 'test'
import { describe, expect, it } from 'vitest'
import { NewOptionDropdownHandlerLabel } from './new-option-dropdown-handler-label.template'

describe('New Option Dropdown Handler Label', () => {
  it('should be render a handler label correctly', () => {
    const { getByTestId } = customRender(<NewOptionDropdownHandlerLabel data-testid="handler-label" />)

    const label = getByTestId('handler-label')

    expect(label).toBeInTheDocument()
  })
})
