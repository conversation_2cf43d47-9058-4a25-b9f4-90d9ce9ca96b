import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { NewOptionDropdownContainer } from './new-option-dropdown-container.template'

describe('New Option Dropdown Container Template', () => {
  it('should be render a container correctly', () => {
    const { getByTestId } = customRender(<NewOptionDropdownContainer data-testid="container" />)

    const container = getByTestId('container')

    expect(container).toBeInTheDocument()
  })
})
