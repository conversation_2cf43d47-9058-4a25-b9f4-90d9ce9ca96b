import { describe, expect, it, vi } from 'vitest'
import userEvent from '@testing-library/user-event'

import { customRender } from 'test'

import { theme } from '@mz-codes/design-system'
import { Card } from './card.template'

function generateMockData() {
  const handleClick = vi.fn()

  const mockProps = {
    handleClick,
    title: 'Title',
    subtitle: 'Subtitle',
    loading: false,
    disabled: false,
    isActive: false,
  }

  return { mockProps }
}

describe('Card Component', () => {
  it('should render the loading component and disable the card when loading is true', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId, queryByTestId } = customRender(<Card {...mockProps} loading />)

    await userEvent.click(getByTestId('card-container'))

    expect(getByTestId('loading-spinner')).toBeInTheDocument()
    expect(queryByTestId('card-title')).not.toBeInTheDocument()
    expect(queryByTestId('card-subtitle')).not.toBeInTheDocument()
    expect(getByTestId('card-container')).toBeDisabled()
    expect(getByTestId('card-container')).toBeDisabled()
    expect(mockProps.handleClick).not.toHaveBeenCalled()
  })

  it('should render title and subtitle when loading is false', () => {
    const { mockProps } = generateMockData()

    const { getByText, getByTestId } = customRender(<Card {...mockProps} />)

    expect(getByText('Title')).toBeInTheDocument()
    expect(getByText('Subtitle')).toBeInTheDocument()
    expect(getByTestId('card-container')).toHaveAttribute('aria-busy', 'false')
  })

  it('should disable the card when disabled is true', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<Card {...mockProps} disabled />)

    await userEvent.click(getByTestId('card-container'))

    expect(getByTestId('card-container')).toBeDisabled()
    expect(mockProps.handleClick).toBeCalledTimes(0)
  })

  it('should apply active styles when isActive is true and not disabled', () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<Card {...mockProps} isActive />)

    const cardContainer = getByTestId('card-container')

    expect(cardContainer).toHaveStyleRule('border', `1px solid ${theme.legacy.colors.primary.primary}`)
    expect(cardContainer).not.toBeDisabled()
  })

  it('should call handleClick when the card is clicked', async () => {
    const { mockProps } = generateMockData()

    const { getByTestId } = customRender(<Card {...mockProps} />)

    await userEvent.click(getByTestId('card-container'))

    expect(mockProps.handleClick).toBeCalledTimes(1)
  })
})
