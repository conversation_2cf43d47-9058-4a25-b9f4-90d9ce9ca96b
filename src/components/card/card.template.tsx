import { Loading } from '@mz-codes/design-system'

import { CardContainer } from './card-container'
import { CardTitle } from './card-title'
import { CardSubtitle } from './card-subtitle'
import { TCard } from './card.types'

export function Card({ title, subtitle, isActive = false, loading, handleClick, disabled }: TCard) {
  const isBusy = loading ? 'true' : 'false'

  return (
    <CardContainer
      disabled={disabled || loading}
      $isActive={isActive || false}
      onClick={handleClick}
      aria-busy={isBusy}
      data-testid="card-container"
    >
      {loading && <Loading />}
      {!loading && (
        <>
          <CardTitle data-testid="card-title">{title}</CardTitle>
          <CardSubtitle data-testid="card-subtitle">{subtitle}</CardSubtitle>
        </>
      )}
    </CardContainer>
  )
}
