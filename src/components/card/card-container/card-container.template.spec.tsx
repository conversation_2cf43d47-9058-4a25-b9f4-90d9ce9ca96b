import { describe, expect, it } from 'vitest'

import { customRender } from 'test'

import { theme } from '@mz-codes/design-system'
import { CardContainer } from './card-container.template'

describe('Card Container Template', () => {
  it('should render the card container correctly when isActive is true and disabled is true', () => {
    const { getByTestId } = customRender(<CardContainer $isActive disabled data-testid="card-container" />)

    const cardContainer = getByTestId('card-container')

    expect(cardContainer).toBeInTheDocument()
    expect(cardContainer).toHaveStyleRule('cursor', 'default')
  })

  it('should render the card container correctly when isActive is true and disabled is false', () => {
    const { getByTestId } = customRender(<CardContainer $isActive disabled={false} data-testid="card-container" />)

    const cardContainer = getByTestId('card-container')

    expect(cardContainer).toBeInTheDocument()
    expect(cardContainer).toHaveStyleRule('cursor', 'pointer')
    expect(cardContainer).toHaveStyleRule('border', `1px solid ${theme.legacy.colors.primary.primary}`)
  })

  it('should render the card container correctly when isActive is false and disabled is true', () => {
    const { getByTestId } = customRender(<CardContainer $isActive={false} disabled data-testid="card-container" />)

    const cardContainer = getByTestId('card-container')

    expect(cardContainer).toBeInTheDocument()
    expect(cardContainer).toHaveStyleRule('cursor', 'default')
  })

  it('should render the card container correctly when isActive is false and disabled is false', () => {
    const { getByTestId } = customRender(
      <CardContainer $isActive={false} disabled={false} data-testid="card-container" />
    )

    const cardContainer = getByTestId('card-container')

    expect(cardContainer).toBeInTheDocument()
    expect(cardContainer).toHaveStyleRule('cursor', 'pointer')
  })
})
