import styled, { css } from 'styled-components'

import { theme } from '@mz-codes/design-system'

const defaultBackground = css`
  background-color: ${theme.legacy.colors.neutral.contentBackground};
`

const selectedBorder = css`
  border: 1px solid ${theme.legacy.colors.primary.primary};
`

export const CardContainer = styled.button<{ $isActive: boolean; disabled: boolean }>`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  width: inherit;
  height: 120px;
  cursor: pointer;
  border: none;
  border-radius: 6px;

  &:hover {
    background-color: ${({ disabled }) =>
      disabled ? theme.legacy.colors.neutral.contentBackground : theme.legacy.colors.neutral.cardHover};
  }

  ${({ $isActive, disabled }) => $isActive && !disabled && selectedBorder}
  cursor: ${({ disabled }) => (disabled ? 'default' : 'pointer')};

  ${defaultBackground};

  &:active {
    ${defaultBackground};
    ${({ disabled }) => !disabled && selectedBorder}
  }
`
