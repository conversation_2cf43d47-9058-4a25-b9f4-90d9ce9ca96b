import { i18n } from 'translate'

// NOTE: Don't change keys names, it's report types from backend
export const reportTranslations: { [key: string]: string } = {
  baseSummary: i18n.t('exportHistory.type.baseSummary'),
  closePriceExcel: i18n.t('exportHistory.type.closePriceExcel'),
  dailyPositions: i18n.t('exportHistory.type.dailyPositions'),
  contact_tearsheet_private: i18n.t('exportHistory.type.contactTearsheetPrivate'),
  firm_tearsheet_private: i18n.t('exportHistory.type.firmTearsheetPrivate'),
  monitoredContactsBackup: i18n.t('exportHistory.type.monitoredContactsBackup'),
  monitoredDailyPositionExcel: i18n.t('exportHistory.type.monitoredDailyPositionExcel'),
  monitoredMovementReportExcel: i18n.t('exportHistory.type.monitoredMovementReportExcel'),
  monitoredReportEmail: i18n.t('exportHistory.type.monitoredReportEmail'),
  monitoredReportExcel: i18n.t('exportHistory.type.monitoredReportExcel'),
  NINETY_DAY_POSITIONS: i18n.t('exportHistory.type.ninetyDayPositionsExcel'),
  ownershipBeta2: i18n.t('exportHistory.type.ownershipBeta2'),
  'SHAREHOLDER BASE ALBERT': i18n.t('exportHistory.type.shareholderBaseAlbert'),
  'SHAREHOLDER BASE': i18n.t('exportHistory.type.shareholderBase'),
  SHAREHOLDER_DAILY_POSITION_HISTORIC_REPORT: i18n.t('exportHistory.type.shareholderDailyPositionHistoricReport'),
  shareholderHistoricPositionGrouped: i18n.t('exportHistory.type.shareholderHistoricPositionGrouped'),
  SHAREHOLDER_GROUP_POSITION_REPORT: i18n.t('exportHistory.type.shareholderGroupPositionReport'),
  SHAREHOLDER_POSITION_OVERVIEW_REPORT: i18n.t('exportHistory.type.shareholderPositionOverviewReport'),
  'Shareholders Charts': i18n.t('exportHistory.type.shareholdersCharts'),
  shareholders: i18n.t('exportHistory.type.shareholders'),
  shareholdersGrouping: i18n.t('exportHistory.type.shareholdersGrouping'),
  shareholdersGroups: i18n.t('exportHistory.type.shareholdersGroups'),
  spreadsheetGroupingBackup: i18n.t('exportHistory.type.spreadsheetGroupingBackup'),
  summaryReportEmail: i18n.t('exportHistory.type.summaryReportEmail'),
  TopBuyers: i18n.t('exportHistory.type.topBuyers'),
  TopHolders: i18n.t('exportHistory.type.topHolders'),
  TopHoldersMovement: i18n.t('exportHistory.type.topHoldersMovement'),
  TopNewHolders: i18n.t('exportHistory.type.topNewHolders'),
  TopSellers: i18n.t('exportHistory.type.topSellers'),
  TopVariation: i18n.t('exportHistory.type.topVariation'),
  TopZeroedPositions: i18n.t('exportHistory.type.topZeroedPositions'),
  VinculatedShareholders: i18n.t('exportHistory.type.vinculatedShareholders'),
}
export type TExportHistoryReportTypes = keyof typeof reportTranslations

export const getReportTypeTranslations = (reportName: string) => {
  return reportTranslations[reportName] || reportName
}

export const reportStatusTranslations: { [key: string]: string } = {
  requested: i18n.t('exportHistory.Status.Requested'),
  uploading: i18n.t('exportHistory.Status.Uploading'),
  processing: i18n.t('exportHistory.Status.Processing'),
  finished: i18n.t('exportHistory.Status.Finished'),
  failed: i18n.t('exportHistory.Status.Failed'),
}
