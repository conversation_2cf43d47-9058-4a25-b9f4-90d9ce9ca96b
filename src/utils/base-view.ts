import { i18n } from 'translate'

export const baseView = [
  {
    value: '0',
    label: i18n.t('simple'),
  },
  {
    value: '1',
    label: i18n.t('grouped'),
  },
  {
    value: '2',
    label: 'Beta',
  },
]

export const baseViewOwnership = [
  ...baseView,
  {
    value: '3',
    label: 'Beta 2',
  },
]

export const baseViewReports = [
  {
    value: 'grouped',
    label: i18n.t('grouped'),
  },
  {
    value: 'simple',
    label: i18n.t('simple'),
  },
  {
    value: 'albert',
    label: 'Beta',
  },
]
