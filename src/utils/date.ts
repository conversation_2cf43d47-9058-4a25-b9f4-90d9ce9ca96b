import {
  format as dateFnsFormat,
  subDays,
  startOfDay as dateFnsStartOfDay,
  endOfDay as dateFnsEndOfDay,
} from 'date-fns'
import { i18n } from 'translate'
import { TOption } from '@mz-codes/design-system'

// Helper type for date or string input
type DateInput = Date | string | number

// Converts milliseconds to days
const msToDays = (dateInMilliseconds: number): number => {
  return dateInMilliseconds / (1000 * 60 * 60 * 24)
}

// Calculates the number of days between two dates
const daysBetweenDates = (firstDate: DateInput, lastDate: DateInput): number => {
  const diffInMs = new Date(lastDate).getTime() - new Date(firstDate).getTime()
  return msToDays(Math.abs(diffInMs))
}

// Formats a date using the specified format
const formatDate = (date: Date, format: string = i18n.t('globals.DatePickerDateFormat')): string => {
  return dateFnsFormat(date, format)
}

// Subtracts a specified number of days from a date
const subtractDays = (date: Date, days: number): Date => {
  return subDays(date, days)
}

// Gets the start of the day for a given date
const startOfDay = (date: Date): Date => {
  return dateFnsStartOfDay(date)
}

// Gets the end of the day for a given date
const endOfDay = (date: Date): Date => {
  return dateFnsEndOfDay(date)
}

// Formats a date to a string using the specified format
const formatDateToString = (date: Date = new Date(), format: string = 'yyyy-MM-dd'): string => {
  return dateFnsFormat(date, format)
}

// Converts a date string in UTC format to a Date object
const dateFromUTC = (date: string): Date => {
  return new Date(`${date.substring(0, 10)}T00:00:00.000`)
}

// Converts a date-time string in UTC format to a Date object
const dateTimeFromUTC = (date: string): Date => {
  const [dateStr, timeStr] = date.split(' ')
  const [hours, minutes, seconds] = timeStr.split(':').map(Number)
  const [year, month, day] = dateStr.split('-').map(Number)
  return new Date(Date.UTC(year, month - 1, day, hours, minutes, seconds))
}

type MonthRange = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11

export function getTranslatedMonth(month: MonthRange) {
  const months = {
    0: i18n.t('january'),
    1: i18n.t('february'),
    2: i18n.t('march'),
    3: i18n.t('april'),
    4: i18n.t('may'),
    5: i18n.t('june'),
    6: i18n.t('july'),
    7: i18n.t('august'),
    8: i18n.t('september'),
    9: i18n.t('october'),
    10: i18n.t('november'),
    11: i18n.t('december'),
  }

  return months[month]
}

const monthsList = Array.from(Array(12)).map((_, month) => {
  return {
    value: month,
    label: getTranslatedMonth(month as MonthRange),
  }
})

const yearsList = (availableDates: Date[]): TOption[] => {
  const yearToObject = (year: number) => {
    return { value: year, label: year.toString() }
  }

  if (availableDates && availableDates.length > 0) {
    const firstDate = availableDates[0]
    const lastDate = availableDates[availableDates.length - 1]

    const firstYear = firstDate.getFullYear()
    const lastYear = lastDate.getFullYear()

    const availableYears = Array.from(Array(lastYear - firstYear + 1)).map((_, idx) => yearToObject(+firstYear + idx))

    return availableYears
  }

  const currentYear = new Date().getFullYear()

  const predecessors = Array.from(Array(40))
    .map((_, idx) => yearToObject(currentYear - 1 - idx))
    .sort((a, b) => (a.value > b.value ? 1 : -1))

  const successors = Array.from(Array(40))
    .map((_, idx) => yearToObject(currentYear + 1 + idx))
    .sort((a, b) => (a.value > b.value ? 1 : -1))

  return [...predecessors, yearToObject(currentYear), ...successors]
}

export {
  daysBetweenDates,
  msToDays,
  subtractDays,
  startOfDay,
  endOfDay,
  dateFromUTC,
  dateTimeFromUTC,
  formatDate,
  formatDateToString,
  monthsList,
  yearsList,
}
