/* eslint-disable @typescript-eslint/no-explicit-any */
import { getFileName } from './getFileName'

export type Response = {
  data: BlobPart
  headers: { [key: string]: any }
}

interface WinNavigator extends Navigator {
  msSaveOrOpenBlob: (blob: Blob, fileName: string) => void
}

export const doDownload = (response: Response, customFileName?: string) => {
  const { data, headers } = response

  const blobFile = new Blob([data], { type: headers['content-type'] })
  const fileName = customFileName || getFileName(headers['content-disposition'])

  const windowNavigator = window.navigator as WinNavigator
  if (windowNavigator && windowNavigator.msSaveOrOpenBlob) {
    windowNavigator.msSaveOrOpenBlob(blobFile, fileName)
    return
  }

  const a = document.createElement('a')
  const URL = window.URL || window.webkitURL
  const url = URL.createObjectURL(blobFile)
  a.href = url
  a.download = fileName
  document.body.append(a)
  a.click()
  a.remove()
  setTimeout(() => {
    URL.revokeObjectURL(url)
  }, 100)
}
