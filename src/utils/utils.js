import { i18n } from 'translate'

const formatLocale = (val) => {
  return `${Number(val).toLocaleString(i18n.language, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`
}

const formatThousand = (n, idiom) => {
  if (!n || n === 0) return 0
  const language = idiom === 0 ? 'en-US' : 'pt-BR'
  return n.toLocaleString(language)
}

// AUM or MARKETVALUE
const formatPrice = (price, idiom) => {
  if (!price || price === 0) return 0
  const language = idiom === 0 ? 'en-US' : 'pt-BR'
  return price.toLocaleString(language, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}

const round = (num, places) => {
  if (!`${num}`.includes('e')) {
    return +`${Math.round(`${num}e+${places}`)}e-${places}`
  }
  const arr = `${num}`.split('e')
  let sig = ''
  if (+arr[1] + places > 0) {
    sig = '+'
  }

  return +`${Math.round(`${+arr[0]}e${sig}${+arr[1] + places}`)}e-${places}`
}

const formatRepresentativeness = (r, idiom) => {
  if (!r || r === 0) return 0
  let parsedRepresentativeness = 0
  parsedRepresentativeness = parseFloat(r)
  parsedRepresentativeness = round(parsedRepresentativeness, 2)
  const language = idiom === 0 ? 'en-US' : 'pt-BR'
  return parsedRepresentativeness.toLocaleString(language, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}

const stringHasVal = (t) => t && typeof t === 'string' && t.trim().length > 0

const isNumber = (value) => {
  return Number.isNaN(Number(value))
}

const convertPxToNumber = (param) => {
  if (typeof param === 'string') {
    if (param.endsWith('px')) {
      return Number(param.split('px')[0])
    } else if (param.endsWith('rem')) {
      const remValue = parseFloat(param.split('rem')[0])
      const fontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)
      return remValue * fontSize
    }
  }
  return NaN
}

export default {
  formatLocale,
  formatThousand,
  formatPrice,
  formatRepresentativeness,
  round,
  stringHasVal,
  isNumber,
  convertPxToNumber,
}
