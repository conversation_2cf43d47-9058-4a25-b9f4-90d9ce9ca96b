/**
 * Util for handling input changes.
 * This is used for updating text inputs and textareas
 * Created on version 1.0.0
 */

import { normalizeText, validation } from '.'

const inputChangedHandler = (event, inputId, state, options) => {
  const inputForm = { ...state }
  const currentInput = { ...state[inputId] }

  let updatedFormElement = {
    ...currentInput,
    value: event,
    isValid: validation(event, currentInput.validationParams),
    wasTouched: true,
  }

  if (options != null) {
    const filteredOptions = options.map((option) => {
      const normalizedOption = normalizeText(option)
      const normalizedEvent = normalizeText(event)
      return normalizedOption === normalizedEvent
    })

    const isOptionsValid = filteredOptions.includes(true) && updatedFormElement.isValid
    updatedFormElement = {
      ...updatedFormElement,
      isValid: isOptionsValid,
    }
  }

  const tempForm = {
    ...inputForm,
    [inputId]: updatedFormElement,
  }

  let isFormValid = true

  Object.keys(tempForm).forEach((id) => {
    if (tempForm[id].isValid != null) {
      isFormValid = tempForm[id].isValid && isFormValid
    }
  })

  const updatedForm = {
    ...inputForm,
    [inputId]: updatedFormElement,
    isFormValid,
  }

  return updatedForm
}

export default inputChangedHandler
