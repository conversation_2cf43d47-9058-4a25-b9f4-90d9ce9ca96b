export const numberHelper = (val, idiom, pref, suf, decimalNumbers) => {
  if (!val && val !== '0') return ''
  let negativeSymbol = ''
  let convertedNumber = Number(val)
  const parsedIdiom = Number(idiom)

  // formating for currency symbol shows after negative symbol
  if (convertedNumber < 0) negativeSymbol = '-'

  convertedNumber = parseFloat(convertedNumber.toFixed(decimalNumbers))

  // First we must be in american format to work with float
  convertedNumber = convertedNumber.toLocaleString('en-US', {
    minimumFractionDigits: decimalNumbers,
  })

  convertedNumber = convertedNumber.toString().replace(/,/g, '')
  if (decimalNumbers) {
    convertedNumber = parseFloat(convertedNumber).toFixed(decimalNumbers)
  }

  const language = parsedIdiom === 0 ? 'en-US' : 'pt-BR'

  convertedNumber = Number(convertedNumber).toLocaleString(language, {
    minimumFractionDigits: decimalNumbers,
  })
  convertedNumber = convertedNumber.replace(/-/g, '')

  return `${negativeSymbol || ''}${pref || ''}${convertedNumber}${suf || ''}`
}
