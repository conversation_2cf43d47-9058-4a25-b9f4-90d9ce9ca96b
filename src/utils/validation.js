/**
 * Util for checking input validation.
 * Created on version 1.0.0
 */
const validation = (value, rules) => {
  // Set valid to true, until it breaks a rule.
  let isValid = true
  // If an object doesn't have any validation we simply return true.
  if (!rules) {
    return true
  }

  // If it is required we check that the field isn't blank.
  // For minLength and maxLength we check if the string is within bounds.
  if (rules.required) {
    isValid = value.trim() !== '' && isValid
  }
  if (rules.minLength) {
    isValid = value.length >= rules.minLength && isValid
  }
  if (rules.maxLength) {
    isValid = value.length <= rules.maxLength && isValid
  }

  // For more complex validation we pass through regex commands.
  let pattern = ''
  switch (true) {
    case rules.isEmail:
      // eslint-disable-next-line max-len
      pattern =
        /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9]){0,9}?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9]){1,3}?/g
      isValid = pattern.test(value) && isValid
      break
    case rules.isNumeric:
      pattern = /^\d+$/
      isValid = pattern.test(value) && isValid
      break
    case rules.isURL:
      pattern = /[(http(s)?)://(www.)?a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/gi
      isValid = pattern.test(value) && isValid
      break
    default:
      break
  }
  return isValid
}
// eslint-disable-next-line max-len
// [a-zA-Z0-9]+(?:(\.|_)[A-Za-z0-9!#$%&'*+/=?^`{|}~-]+)*@(?:[a-zA-Z0-9-](?:[a-zA-Z0-9-]*[a-zA-Z0-9-])?\.)+[a-zA-Z0-9-](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?
export default validation
