import { localInformation } from 'utils'
import { APPLICATIONS } from 'consts'

export const hasProductPermission = (requiredProduct) => {
  const userApplicationIds = localInformation.getCore2SelectedCustomerUserApplicationIds()

  return Array.isArray(requiredProduct)
    ? requiredProduct.every((prod) => userApplicationIds.includes(APPLICATIONS[prod]))
    : userApplicationIds.includes(APPLICATIONS[requiredProduct])
}

export const hasScopePermission = (requiredPermission) => {
  const permissions = localInformation.getCurrentPermissions()

  if (!permissions) {
    return false
  }

  return Array.isArray(requiredPermission)
    ? requiredPermission.every((permission) => permissions.includes(permission))
    : permissions.includes(requiredPermission)
}

const adminScopes = [
  'mzcore:customers:users:create',
  'mzcore:customers:users:update',
  'mzcore:customers:users:deactivate',
]

export const hasAdminPermission = () => hasProductPermission('companyAdmin') && hasScopePermission(adminScopes)
