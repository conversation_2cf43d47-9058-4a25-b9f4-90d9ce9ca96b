import { getCompany } from 'globals/storages/locals'
import storeInformation from 'utils/storeInformation'
import utils from './utils'

const LOCAL_STORAGE_IRM_PAGE_ITEMS_AMOUNT = 'mziq-storage-irm-page-items-amount'
const LOCAL_STORAGE_THEME = 'mziq-web-theme'
const LOCAL_STORAGE_IGNORE_BIND_INSTITUTION = 'mziq-web-ignore-bind-institution'

export default class LocalInformation {
  // & THEME-HELPER
  static getTheme() {
    return localStorage.getItem(LOCAL_STORAGE_THEME) || 'DARK'
  }

  static getCustomerId = () => {
    return storeInformation.getCore2CustomerId() || ''
  }

  // & OLD IRM
  static getUserId() {
    return storeInformation.getUserId()
  }

  // & MENU-REDUCER & OLD SIDE MENU/HEADER
  static getCompanyId() {
    const company = getCompany()
    return company?.id ?? null
  }

  // & MENU-REDUCER  & OLD SIDE MENU
  static getCurrentPermissions() {
    return storeInformation.getCurrentPermissions() as string[] | null
  }

  // COMMON REDUCER
  static getDealogicInformation() {
    return storeInformation.getDealogicInformation()
  }

  static getDealogicCurrentInformation() {
    return storeInformation.getDealogicCurrentInformation()
  }

  // MENU-REDUCER
  static getCore2SelectedCustomerUserApplications() {
    return Object.keys(storeInformation.getCore2SelectedCustomerUserApplications())
  }

  static getCore2SelectedCustomerUserApplicationIds() {
    return Object.values(storeInformation.getCore2SelectedCustomerUserApplications())
  }

  // OWNERSHIP & SHAREHOLDER
  static getPageItemsAmount() {
    const quantity = localStorage.getItem(LOCAL_STORAGE_IRM_PAGE_ITEMS_AMOUNT)
    return utils.isNumber(quantity) ? 5 : quantity
  }

  static setPageItemsAmount(itemsAmount?: number | string) {
    if (!itemsAmount) return

    const itemsAmountSerialized = typeof itemsAmount === 'number' ? itemsAmount.toString() : itemsAmount
    localStorage.setItem(LOCAL_STORAGE_IRM_PAGE_ITEMS_AMOUNT, itemsAmountSerialized)
  }

  static getIgnoredGroupsToBindInstitution() {
    return localStorage.getItem(LOCAL_STORAGE_IGNORE_BIND_INSTITUTION)?.split(',') || []
  }

  static pushIgnoredGroupsToBindInstitution(groupId: string) {
    const previous = this.getIgnoredGroupsToBindInstitution()
    const groupIds = JSON.stringify([...previous, groupId])
    localStorage.setItem(LOCAL_STORAGE_IGNORE_BIND_INSTITUTION, groupIds)
  }
}
