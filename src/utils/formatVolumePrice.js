export const formatVolumePriceNumber = (volume, price, idiom = 1) =>
  Number(Number(volume) * Number(price)).toLocaleString(Number(idiom) === 1 ? 'pt-BR' : 'en-US', {
    minimumIntegerDigits: 1,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })

export const formatVolumePrice = (volume, price, idiom = 1) =>
  volume && price ? `R$ ${formatVolumePriceNumber(volume, price, idiom)}` : 'R$ 0,00'
