const SESSION_STORAGE_SHAREHOLDER_LISTING_VIEW_TYPE = 'mziq-storage-shareholder-listing-view-type'

class SessionInformation {
  static setShareholderListingViewType(state) {
    if (state) {
      sessionStorage.setItem(SESSION_STORAGE_SHAREHOLDER_LISTING_VIEW_TYPE, state)
    }
  }

  static getShareholderListingViewType() {
    const state = sessionStorage.getItem(SESSION_STORAGE_SHAREHOLDER_LISTING_VIEW_TYPE)
    if (state) return state
    return null
  }
}

export default SessionInformation
