export const formatNumber = (
  idiom: number,
  val: number | string | undefined,
  fractionDigits: number | undefined = undefined,
  pref = '',
  suf = ''
) => {
  if (!val) return '--'
  const language = idiom === 1 ? 'pt-BR' : 'en-US'
  return `${pref || ''}${Number(val).toLocaleString(language, {
    minimumFractionDigits: fractionDigits,
    maximumFractionDigits: fractionDigits,
  })}${suf || ''}`
}
