import { i18n } from 'translate'

export const irmReportOptions = [
  {
    value: 1,
    label: i18n.t('topHolders'),
  },
  {
    value: 2,
    label: i18n.t('topBuyers'),
  },
  {
    value: 3,
    label: i18n.t('topSellers'),
  },
  {
    value: 4,
    label: i18n.t('zeroedPositions'),
  },
  {
    value: 5,
    label: i18n.t('newHolders'),
  },
  {
    value: 6,
    label: i18n.t('exportHistory.type.topVariation'),
  },
  {
    value: 7,
    label: i18n.t('topHoldersMovement'),
  },
]
