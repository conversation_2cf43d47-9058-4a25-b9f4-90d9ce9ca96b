const getFileName = (disposition = 'file'): string => {
  const utf8FilenameRegex = /filename\*=UTF-8''([\w%\-.]+)(?:; ?|$)/i
  const asciiFilenameRegex = /^filename=(["']?)(.*?[^\\])\1(?:; ?|$)/i

  if (utf8FilenameRegex.test(disposition)) {
    const regexResult = utf8FilenameRegex.exec(disposition)
    const fileName = regexResult ? regexResult[1] : ''
    return decodeURIComponent(fileName)
  }

  const filenameStart = disposition.toLowerCase().indexOf('filename=')
  if (filenameStart) {
    const partialDisposition = disposition.slice(filenameStart)
    const matches = asciiFilenameRegex.exec(partialDisposition)
    if (matches != null && matches[2]) {
      return matches[2]
    }
  }

  return disposition
}

export { getFileName }
