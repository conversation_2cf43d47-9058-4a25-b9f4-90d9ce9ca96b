import { DOCUMENT_TYPES, DocumentType } from 'types/shareholders'

const formatDocument = (document: string) => {
  const baseDocument = document.replace(/\D/g, '')

  if (baseDocument.length <= 11) {
    // CPF
    return baseDocument
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d{1,2})$/, '$1-$2')
  }

  // CNPJ
  return baseDocument
    .replace(/^(\d{2})(\d)/, '$1.$2')
    .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
    .replace(/\.(\d{3})(\d)/, '.$1/$2')
    .replace(/(\d{4})(\d)/, '$1-$2')
}

function formatDocumentInputMask(input: string, type: DocumentType) {
  const unmaskedValue = input.replace(/\D/g, '').slice(0, type === DOCUMENT_TYPES.INDIVIDUAL ? 11 : 14)

  if (type === DOCUMENT_TYPES.INDIVIDUAL) {
    return unmaskedValue.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4')
  }
  if (type === DOCUMENT_TYPES.FUND) {
    return unmaskedValue.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5')
  }
  return unmaskedValue
}

export { formatDocument, formatDocumentInputMask }
