import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON>rowserRouter } from 'react-router-dom'
import Modal from 'react-modal'
import App from './App'

Modal.setAppElement('#root')

const loading = document.getElementById('loading')
if (loading) {
  loading.remove()
}

const container = document.getElementById('root')
if (!container) throw new Error('Failed to find the root element')
const root = createRoot(container)

root.render(
  <StrictMode>
    <BrowserRouter future={{ v7_relativeSplatPath: true, v7_startTransition: true }}>
      <App />
    </BrowserRouter>
  </StrictMode>
)
