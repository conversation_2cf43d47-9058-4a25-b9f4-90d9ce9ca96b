import { createEnv } from '@t3-oss/env-core'
import { z } from 'zod'

const clientEnvs = createEnv({
  clientPrefix: 'VITE',
  client: {
    VITE_APP_NAME: z.string(),

    VITE_MFE_CONTAINER_URL: z.string(),

    VITE_PORTAL_URL: z.string().optional().default('https://mziq.com'),
    VITE_PORTAL_STG_URL: z.string().optional().default('https://portal-stg.mziq.com'),

    VITE_API_BASE_URL: z.string().url(),

    VITE_LOGTO_ENDPOINT: z.string().url(),
    VITE_LOGTO_APP_ID: z.string(),

    VITE_DATADOG_ENABLED: z
      .string()
      .optional()
      .default('false')
      .transform((val) => val === 'true'),
    VITE_DATADOG_APPLICATION_ID: z.string().optional(),
    VITE_DATADOG_CLIENT_TOKEN: z.string().optional(),
    VITE_DATADOG_ENV: z.string().optional().default('production'),
    VITE_DATADOG_SAMPLE_RATE: z.number().optional().default(100),
    VITE_CLARITY_ENABLED: z
      .string()
      .optional()
      .default('false')
      .transform((val) => val === 'true'),
    VITE_CLARITY_PROJECT_ID: z.string().optional(),
  },
  runtimeEnv: import.meta.env,
})

export const env = {
  APP_NAME: clientEnvs.VITE_APP_NAME,

  MFE_CONTAINER_URL: clientEnvs.VITE_MFE_CONTAINER_URL,

  PORTAL_URL: clientEnvs.VITE_PORTAL_URL,
  PORTAL_STG_URL: clientEnvs.VITE_PORTAL_STG_URL,

  API_BASE_URL: clientEnvs.VITE_API_BASE_URL,

  LOGTO_ENDPOINT: clientEnvs.VITE_LOGTO_ENDPOINT,
  LOGTO_APP_ID: clientEnvs.VITE_LOGTO_APP_ID,

  IS_DEV: import.meta.env.DEV || import.meta.env.MODE === 'development',

  BASE_URL: import.meta.env.BASE_URL,

  DATADOG_ENABLED: clientEnvs.VITE_DATADOG_ENABLED,
  DATADOG_APPLICATION_ID: clientEnvs.VITE_DATADOG_APPLICATION_ID,
  DATADOG_CLIENT_TOKEN: clientEnvs.VITE_DATADOG_CLIENT_TOKEN,
  DATADOG_ENV: clientEnvs.VITE_DATADOG_ENV,
  DATADOG_SAMPLE_RATE: clientEnvs.VITE_DATADOG_SAMPLE_RATE,

  CLARITY_ENABLED: clientEnvs.VITE_CLARITY_ENABLED,
  CLARITY_PROJECT_ID: clientEnvs.VITE_CLARITY_PROJECT_ID,
}
