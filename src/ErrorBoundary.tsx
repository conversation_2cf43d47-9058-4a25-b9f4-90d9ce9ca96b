import { type ReactNode, type ErrorInfo } from 'react'
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary'

interface ErrorBoundaryProps {
  children: ReactNode
  fallback: ReactNode
  onFallback?: (error: Error, errorInfo: ErrorInfo) => void
}

function ErrorBoundary({ children, fallback, onFallback }: ErrorBoundaryProps) {
  const errorHandler = (error: Error, errorInfo: ErrorInfo) => {
    if (onFallback) {
      onFallback(error, errorInfo)
    }
  }

  return (
    <ReactErrorBoundary FallbackComponent={() => fallback} onError={errorHandler}>
      {children}
    </ReactErrorBoundary>
  )
}

export default ErrorBoundary
