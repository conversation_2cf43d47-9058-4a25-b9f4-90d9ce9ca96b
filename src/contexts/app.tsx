import React, { PropsWithChildren, useMemo } from 'react'
import { api, MZ_CORE } from 'globals/api'
import { ThemeProvider } from 'styled-components'
import { LogtoProvider, type LogtoConfig } from '@logto/react'
import { Loading, ToastProvider, TourProvider, theme } from '@mz-codes/design-system'
import { i18n } from 'translate'
import { StoreProvider } from './store'
import ErrorBoundary from '../ErrorBoundary'
import { env } from '../env'

const config: LogtoConfig = {
  endpoint: env.LOGTO_ENDPOINT,
  appId: env.LOGTO_APP_ID,
  resources: [env.API_BASE_URL],
}

function AppProvider({ children }: PropsWithChildren<undefined>) {
  const locale = useMemo(
    () => ({
      back: i18n.t('components.tour.back'),
      close: i18n.t('components.tour.close'),
      last: i18n.t('components.tour.last'),
      next: i18n.t('components.tour.next'),
      skip: i18n.t('components.tour.skip'),
    }),
    []
  )

  return (
    <LogtoProvider config={config}>
      <ThemeProvider theme={theme}>
        <ErrorBoundary fallback={<Loading />}>
          <StoreProvider>
            <ToastProvider>
              <TourProvider PRODUCT_NAME={env.APP_NAME} MZ_CORE={MZ_CORE} clientApi={api} locale={locale}>
                {children}
              </TourProvider>
            </ToastProvider>
          </StoreProvider>
        </ErrorBoundary>
      </ThemeProvider>
    </LogtoProvider>
  )
}

export { AppProvider }
