import { SVGProps } from 'react'

export function ArrowDownIcon(props: SVGProps<SVGSVGElement>) {
  const { width = 15, height = 15, fill = '#fff' } = props

  return (
    <svg width={`${width}`} height={`${height}`} viewBox={`0 0 ${width} ${height}`}>
      <polygon
        fill={`${fill}`}
        width={`${width}`}
        height={`${height}`}
        points="10.95 4.5 7.5 8.23 4.05 4.5 3 5.63 7.5 10.5 12 5.63 10.95 4.5"
      />
    </svg>
  )
}
