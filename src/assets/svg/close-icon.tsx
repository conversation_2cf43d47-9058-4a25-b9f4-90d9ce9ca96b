import { SVGProps } from 'react'

export function CloseIcon(props: SVGProps<SVGSVGElement>) {
  const { width = 15, height = 15, fill = '#fff' } = props

  return (
    <svg width={`${width}`} height={`${height}`} viewBox={`0 0 ${width} ${height}`}>
      <polygon
        fill={`${fill}`}
        width={`${width}`}
        height={`${height}`}
        points="21.59 7.5 15 14.09 8.41 7.5 7.5 8.41 14.09 15 7.5 21.59 8.41 22.5 15 15.91 21.59 22.5 22.5 21.59 15.91 15 22.5 8.41 21.59 7.5"
      />
    </svg>
  )
}
