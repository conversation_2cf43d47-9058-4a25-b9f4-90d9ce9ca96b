//utils
@use 'sass:meta';
@use 'utils/variables';
@use 'utils/mixins';

// base
@use 'base/reset';
@use 'base/typography';
@use 'base/base';

#shareholdersUI {
  // layout
  @include meta.load-css('layout/modals');
  @include meta.load-css('layout/pageContent');
  @include meta.load-css('layout/baseList');

  // components
  @include meta.load-css('components/button');
  @include meta.load-css('components/lds-dual-ring');
  @include meta.load-css('components/taskInnerModal');
  @include meta.load-css('components/pagination');
}

@include meta.load-css('components/select');
