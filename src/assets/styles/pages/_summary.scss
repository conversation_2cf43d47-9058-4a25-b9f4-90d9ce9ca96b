@use '../utils/variables';

#shareholdersUI {
  .summary {
    &-content {
      height: auto;
      display: grid;
      grid-template-areas:
        'a a b c'
        'd d e e'
        'f f f g'
        'h h h i'
        'j j j k';
      grid-row-gap: variables.$units-md;
      grid-column-gap: variables.$units-md;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      background-color: variables.$page-bg;

      > div {
        background-color: variables.$page-header-bg;
        min-height: 337px;
        border-radius: variables.$units-md;

        display: flex;
        flex-direction: column;

        > .scroll {
          display: flex;
          flex-direction: column;
          border-radius: variables.$units-md;
        }

        ul {
          flex-grow: 1;
          display: flex;
          flex-direction: column;

          li {
            &:last-of-type {
              &:not(.sub-item) {
                border-radius: 0 0 variables.$units-md variables.$units-md;
              }
            }
          }
        }

        .title {
          height: 49px;
          border-bottom: 1px solid variables.$simpleListBorderColor;
          padding-left: 20px;

          h6 {
            color: #fff;
            font-weight: 700;
            line-height: 48px;
            font-size: 14px;
            font-size: 0.875rem;
          }
        }

        .scroll {
          height: calc(100% - 49px);
          max-height: calc(340px - 49px);
          position: relative;
        }

        &.shareholder-base {
          grid-area: a;
        }

        &.new-holders,
        &.zeroed-positions,
        &.new-holder {
          li {
            grid-template-columns: 40px 1fr 55px 120px;

            span {
              &.num,
              &.name {
                z-index: 1;
              }
            }
          }
        }

        &.top-buyers,
        &.top-sellers,
        &.top-holders {
          .summary-list {
            li {
              grid-template-columns: 40px 1fr 55px repeat(5, 80px) 90px;

              span {
                &.quantity {
                  padding-right: 0;
                }
              }

              .ReactCollapse {
                &--collapse {
                  grid-column-start: 1;
                  grid-column-end: 10;
                }
              }

              .sub-item {
                grid-template-columns: 1fr 55px repeat(5, 80px) 90px;
              }
            }
          }
        }

        &.new-holders {
          grid-area: d;
        }

        &.zeroed-positions {
          grid-area: e;
        }

        &.top-buyers {
          grid-area: f;
        }

        &.top-buyers-chart {
          grid-area: g;
        }

        &.top-sellers {
          grid-area: h;
        }

        &.top-sellers-chart {
          grid-area: i;
        }

        &.top-holders {
          grid-area: j;
        }

        &.top-holders-chart {
          grid-area: k;
        }

        &.see-more {
          background-color: transparent;
          min-height: 10px;
          grid-column-start: 1;
          grid-column-end: 5;

          .bt-view-more {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
            min-width: 120px;
            height: 30px;
            padding-left: 20px;
            padding-right: 20px;
            cursor: pointer;
            font-family: Lato, sans-serif;
            font-weight: 700;
            letter-spacing: 0.25px;
            color: #fff;
            margin-top: 0;
            margin-bottom: 0;
            font-size: 14px;
            font-size: 0.875rem;
            background-color: rgba(0, 207, 255, 0.7);
            border: 0;
            transition: background-color 0.3s ease-in-out;
            margin: 0 auto;
          }
        }

        &.shareholder-type-chart,
        &.stock-chart,
        &.top-buyers-chart,
        &.top-sellers-chart,
        &.top-holders-chart {
          padding: variables.$units-xs 0;
        }
      }

      .summary-list {
        max-width: 3500px;
        padding-top: 0;

        li {
          span {
            &.num {
              padding-left: 20px;
              position: sticky;
              left: 0;

              span {
                display: inline-block;
                height: 17px;
                text-indent: -9999px;
                vertical-align: middle;
                width: 17px;
                cursor: pointer;

                &.open {
                  background: url(../../../assets/png/open-detail-dark.png) no-repeat 0 0;
                }

                &.close {
                  background: url(../../../assets/png/close-detail-dark.png) no-repeat 0 0;
                }
              }

              i {
                background: url(../../../assets/png/ico_aglutinar.png) no-repeat 0 0;
                cursor: pointer;
                display: inline-block;
                height: 18px;
                margin-left: -1px;
                opacity: 0.3;
                vertical-align: middle;
                width: 18px;
                visibility: visible;
              }
            }

            &.quantity {
              text-align: right;
              padding-right: 20px;
            }

            &.pointerEvents {
              pointer-events: none;
              opacity: 0.5;

              .lds-dual-ring {
                display: inline-block;
                height: 17px;
                vertical-align: middle;
                width: 17px;

                div {
                  top: 0;
                }
              }
            }

            &.up {
              color: variables.$postive-value-color;
            }

            &.down {
              color: variables.$negative-value-color;
            }

            &.gap {
              padding-left: 45px !important;
            }
          }

          .ReactCollapse {
            &--collapse {
              grid-column-start: 1;
              grid-column-end: 5;
            }
          }

          .sub-item {
            grid-template-columns: 1fr 55px 120px;

            span {
              &.name {
                width: auto;
                padding-left: 80px;
                margin-left: 0;
                left: 0;
              }
            }

            // +.sub-item {
            //   border-top: 1px solid #344b69;
            // }
          }
        }

        &.shareholder-base {
          li {
            grid-template-columns: 1.5fr 1fr;
            border-top: 0;

            span {
              text-align: right;
              padding-right: 20px;

              &:first-child {
                padding-left: 20px;
                padding-right: 0;
                text-align: left;
              }
            }

            &.header-title {
              grid-template-columns: 1fr;

              span {
                color: #fff;
                font-weight: 400;

                &.name {
                  position: sticky;
                  left: 60px;
                }
              }
            }

            &:nth-child(n + 2) {
              border-top: 1px solid variables.$simpleListBorderColor;
            }
          }
        }
      }

      &.top-10 {
        > div {
          .scroll {
            height: calc(100% - 49px);
            max-height: calc(577px - 49px);
          }
        }
      }
    }
  }

  .shareholder-summary {
    .no-data {
      background-color: #232838;
      color: #fff;
      padding: 20px;
      text-align: center;
      height: 100%;
    }
  }
}
