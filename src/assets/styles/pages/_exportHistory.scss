@use '../utils/variables';

#shareholdersUI {
  .shareholder-export-history {
    background-color: variables.$base-list-bg;
    height: calc(100% - 85px);
    margin-top: 10px;
    border-radius: variables.$units-md;
  }

  .history-base-list {
    width: 100%;
    background-color: variables.$base-list-bg;
    display: inline-block;
    max-width: 3500px;

    li {
      display: grid;
      position: relative;
      grid-column-gap: 20px;
      align-items: center;
      border-bottom: 1px solid variables.$simpleListBorderColor;
      height: 48px;
      grid-template-columns: 160px 146px 1fr 100px 200px 140px;
      padding-left: 0;

      span {
        font-size: 14px;

        &.date {
          background: transparent;
          padding-left: 20px;
          left: 0;
        }

        &.status {
          &.green {
            color: variables.$postive-value-color;
          }

          &.yellow {
            color: #f5e518;
          }

          &.error {
            color: variables.$negative-value-color;
          }
        }

        &.actions {
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 16px;

          .download {
            cursor: pointer;
            width: 20px;
            height: 20px;
            background-image: url(@assets/png/download-icon.png);
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
          }

          .lds-dual-ring {
            div {
              top: 0;
            }
          }
        }

        &.log {
          display: grid;
          grid-column-start: 1;
          grid-column-end: 7;
          grid-template-columns: 160px 146px 1fr 100px 200px 140px;
          grid-column-gap: 20px;
        }
      }

      &.header {
        z-index: 1;

        span {
          color: variables.$main-light-blue;
          font-weight: bold;
          line-height: 47px;
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          pointer-events: none;
          z-index: -1;
          height: 100%;
          width: 100%;
        }

        &::after {
          bottom: -30px;
          content: '';
          height: 60px;
          pointer-events: none;
          position: absolute;
          left: 0;
          right: 0;
          z-index: -2;
        }
      }

      &.list-loading {
        grid-template-columns: 1fr;
        border-bottom: 0;
      }
    }

    &:first-child {
      border-bottom: 0;
    }
  }

  .loading-wrapper {
    height: calc(100vh - 340px);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
