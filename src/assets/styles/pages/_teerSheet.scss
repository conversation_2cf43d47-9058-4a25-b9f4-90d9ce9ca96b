@use '../utils/variables';
@use '../utils/mixins';

#shareholdersUI {
  .tearsheet-container {
    background-color: variables.$page-bg;
    height: 100%;
    padding: variables.$units-lg;

    .funds-content {
      height: 100%;
      overflow: hidden;
      padding-top: 0;
      background-color: variables.$page-bg;

      > div {
        border-radius: variables.$units-md;
      }

      .basic-header-content {
        .title {
          color: #fff;
          display: block;
          font-size: 19px;
          font-size: 1.1875rem;
          padding: 17px 0 19px 19px;
          margin-right: 25px;
          font-weight: 700;
        }

        .document {
          &__WarningBtn {
            position: relative;
            display: none;
            justify-content: center;
            align-items: center;
            width: 20px;
            height: 20px;
            margin-left: 5px;
            background-color: #ff9000;
            border-radius: 50%;
            cursor: default;
          }

          &__WarningContent {
            position: absolute;
            width: 160px;
            right: 0;
            bottom: -8px;
            height: 0;
            font-size: 0;
            line-height: 16px;
            text-align: left;
            padding: 0 15px;
            border: 0 solid #656565;
            background-color: rgba(61, 61, 61, 0.7);
            opacity: 0;
            transform: translate(0, 100%);
            transition:
              height 300ms ease-in-out,
              opacity 300ms ease-in-out,
              padding 300ms ease-in-out;
          }
        }
      }

      &.private-tearsheet {
        > div {
          &.basic-header-content {
            background-color: variables.$base-list-bg;
            height: 57px;
            margin-bottom: variables.$units-md;
            padding: 0;
            width: 100%;

            input {
              &.title {
                height: auto;
                width: calc(100% - 40px);
                padding: 15px 0;
                margin: 0 auto 5px;
                background-color: transparent;
                border: 0;
                border-bottom: 1px solid rgba(67, 67, 72, 1);
              }
            }

            &.private-tearsheet-header {
              height: auto;
              display: flex;
              flex-direction: column;

              &.group-header {
                input {
                  &.title {
                    width: 100%;
                    padding: 15px 0;
                    margin: 0 12px 5px 0;
                    background-color: transparent;
                    border: 0;
                    border-bottom: 1px solid rgba(67, 67, 72, 1);
                  }
                }

                .integrationsBind {
                  margin: 15px auto 0 0;
                  display: inline-flex;

                  img {
                    margin-right: 10px;
                  }
                }

                .export-icon,
                .arrow-icon,
                .trash-icon {
                  cursor: pointer;

                  *,
                  & {
                    fill: white;
                  }

                  &:hover {
                    *,
                    & {
                      fill: variables.$main-light-blue;
                    }
                  }
                }

                .btn {
                  display: inline-flex;
                  background-color: variables.$main-light-blue;
                  border: 0;
                }

                .more-info {
                  display: flex;
                  justify-content: left;
                  align-items: center;
                  padding: 5px 20px;

                  li {
                    border-left: 1px solid #303032;
                    padding: 10px 20px;
                    list-style: none;
                    display: flex;
                    font-size: 14px;

                    span {
                      &.value {
                        color: #bababa;
                        padding: 4px 0;
                      }

                      &.mini-title {
                        color: variables.$main-light-blue;
                        font-weight: bold;
                        padding-right: 3px;
                        display: block;
                      }
                    }

                    &:first-child {
                      border-left: none;
                      padding-left: 0;
                    }
                  }
                }
              }
            }

            .activity-date {
              display: inline-block;

              div {
                input {
                  font-size: 14px;
                  font-weight: 600;
                  border: none;
                  color: white;
                  height: 15px;
                  max-width: 85px;
                  padding: 0;
                  cursor: pointer;
                }
              }
            }
          }

          &.private-tearsheet-header {
            .more-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 5px 20px;

              li {
                border-left: 1px solid #303032;
                padding: 10px 20px;
                display: flex;
                flex-direction: column;

                + li {
                  padding: 8px 12px;
                  align-items: flex-start;
                  flex-direction: column;
                }

                .mini-title {
                  font-weight: 400;
                  color: variables.$main-light-blue;
                  font-size: 14px;
                  font-weight: bold;
                  margin-right: 5px;
                  margin-bottom: 5px;

                  @media (max-width: 1726px) {
                    display: block;
                  }
                }

                a {
                  &.group_name {
                    white-space: nowrap;
                    display: inline-block;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    vertical-align: middle;
                    max-width: 200px;
                    width: auto;
                    border-bottom: 1px solid #fff;
                    margin-right: 8px;
                    font-size: 14px;
                  }

                  &:hover {
                    border-bottom: 1px solid variables.$main-light-blue;
                    color: variables.$main-light-blue;
                  }
                }

                &:first-child {
                  border-left: 0;
                  padding-left: 0;
                }
              }
            }
          }
        }
      }
    }

    .basic-filter-content {
      padding: 0;
      margin-bottom: 45px;

      > div {
        display: inline-block;
        height: 100%;
        position: relative;
        vertical-align: top;
        width: auto;
        min-width: 150px;
      }
    }

    &__HeaderTabs {
      width: 100%;
      margin-bottom: variables.$units-md;
      background-color: variables.$base-list-bg;

      .list-tabs {
        position: relative;
      }
    }

    &__tab {
      position: relative;
      display: inline-block;
      font-weight: 700;
      line-height: 57px;
      color: #fff;
      margin: 0 16.5px;
      cursor: pointer;
      font-size: 14px;
      font-size: 0.875rem;
      -moz-transition: all 0.3s;
      -o-transition: all 0.3s;
      -webkit-transition: all 0.3s;
      -ms-transition: all 0.3s;
      transition: all 0.3s;

      &.active {
        color: variables.$main-light-blue;

        &::before {
          width: 40px;
        }
      }

      &::before {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 0;
        width: 0;
        height: 2px;
        background-color: variables.$secondary-light-blue;
        -moz-transform: translate(-50%, 0);
        -o-transform: translate(-50%, 0);
        -ms-transform: translate(-50%, 0);
        -webkit-transform: translate(-50%, 0);
        transform: translate(-50%, 0);
        -moz-transition: all 0.3s;
        -o-transition: all 0.3s;
        -webkit-transition: all 0.3s;
        -ms-transition: all 0.3s;
        transition: all 0.3s;
      }
    }

    &__FlexTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 0 20px;

      .content-title {
        color: #339fff;
        font-weight: 700;
        margin-bottom: 15px;
        font-size: 20px;
        font-size: 1.25rem;
      }

      .action {
        &__group {
          display: flex;
          align-items: center;

          .btn-export {
            margin-left: 10px;
          }
        }
      }
    }

    &__search {
      float: right;
      padding: 0 10px;
      width: 270px;

      .search_fund {
        height: 35px;
        width: 100%;
        background-color: variables.$sideMenu-bg;
        border-radius: variables.$units-xs;
        border: none;
        color: variables.$text-color;
        padding: 1rem;
        font-size: 13px;
        font-size: 0.8125rem;
      }
    }

    &__content {
      border-radius: variables.$units-md;
      position: relative;
      display: flex;
      background-color: variables.$base-list-bg;
      height: calc(100vh - 315px);
      width: 100%;
      padding: 20px 0 0;
      z-index: 0;
      @include mixins.scroll(variables.$base-list-bg, 4px, variables.$main-light-blue, variables.$main-light-blue);

      &.private {
        padding-left: 0;
        padding-bottom: 0;
        padding-right: 0;
        overflow: auto;
      }
    }

    &__contacts {
      display: grid;
      grid-template-columns: calc(33% - 5px) calc(33% - 5px) calc(33% - 5px);
      grid-column-gap: 15px;
      grid-row-gap: 30px;
      width: 100%;
      padding: 0 20px;
    }

    &__contact {
      display: flex;
    }

    .contact__picture {
      flex-shrink: 0;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;

      img {
        width: 100%;
      }
    }

    .contact__info {
      display: flex;
      align-items: center;
      color: #fff;
      line-height: 1.2;
      margin-bottom: 3px;
      font-size: 14px;
      font-size: 0.875rem;
      letter-spacing: 0.4px;
    }

    .contact__delete {
      position: absolute;
      top: 0;
      left: -25px;
      display: inline-block;
      width: 20px;
      height: 20px;
      padding: 0;
      background-color: rgba(255, 255, 255, 0);
      background-position: center 55%;
      background-size: 16px;
      background-repeat: no-repeat;
      border: 0;
      transition: background-color 300ms ease-in-out;
      cursor: pointer;
    }

    &__ContactInfo {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      margin-left: 15px;
    }

    &__listContent {
      display: block;
      margin-left: 0 !important;
      margin-right: 0 !important;
      padding-bottom: 0 !important;
      overflow: auto;
      height: calc(100% - 56px) !important;
      @include mixins.scroll(variables.$base-list-bg, 4px, variables.$main-light-blue, variables.$main-light-blue);

      &-header {
        background-color: variables.$base-list-bg;
        display: inline-block;
        min-width: 100%;
      }

      .list {
        &-group {
          border-bottom: 1px solid variables.$simpleListBorderColor;
          max-height: calc(100% - 49px);

          &.scroll {
            margin: 0;
            overflow-y: auto;
            padding: 0;
            height: auto;
          }
        }

        &__item {
          display: grid;
          grid-template-columns: 86px 1fr 1fr 1fr 0.8fr 145px 0.9fr 0.85fr 0.8fr 0.8fr 125px 125px;
          align-items: center;
          width: 100%;
          padding-left: 20px;
          border-bottom: 1px solid variables.$simpleListBorderColor;
          position: relative;
          z-index: 1;

          span {
            color: #fff;
            display: inline-block;
            height: 100%;
            line-height: 48px;
            overflow: hidden;
            padding: 0 10px;
            -o-text-overflow: ellipsis;
            text-overflow: ellipsis;
            vertical-align: top;
            white-space: nowrap;
            font-size: 14px;

            &.title {
              display: flex;
              align-items: center;
              color: variables.$main-light-blue;
              font-weight: 700;
              font-size: 14px;
            }

            &:first-child {
              padding-left: 0;
            }

            &.actions {
              width: auto !important;
            }
          }

          &-header {
            position: sticky;
            top: 0;
            border-bottom: 0;
            z-index: 1;

            &::before {
              bottom: 0;
              content: '';
              height: 60px;
              left: 0;
              position: absolute;
              right: 0;
              z-index: -1;
              background-color: variables.$base-list-bg;
            }
          }
        }

        &.task-list {
          display: inline-block;
          max-width: 3500px;
          width: 100%;

          .list {
            &__item {
              display: grid;
              align-items: center;
              width: 100%;
              padding-left: 0;
              grid-column-gap: 10px;
              justify-content: start;
              grid-template-columns: 100px 3fr repeat(2, 1fr) 100px 235px 70px 55px;

              &-header {
                padding-right: 4px;
              }

              span {
                padding: 0;

                &.activity,
                &.contact,
                &.executors {
                  display: flex;
                  align-items: center;
                  flex: 1 0;
                  width: auto;
                  max-width: inherit;
                  padding: 0;
                }

                &.date {
                  overflow: unset;
                }

                &.name {
                  position: sticky;
                  left: 110px;
                }

                &.executors {
                  &__ball {
                    display: inline-flex;
                    justify-content: center;
                    align-items: center;
                    padding: 0;
                    width: 35px;
                    height: 35px;
                    line-height: 35px;
                    margin-right: 5px;
                    background-color: variables.$main-light-blue;
                    border-radius: 50%;
                    font-size: 11px;
                  }
                }

                &.actions {
                  width: auto;
                  max-width: inherit;

                  .icon {
                    background-position: center;
                    background-repeat: no-repeat;
                    cursor: pointer;
                    display: inline-block;
                    height: 18px;
                    margin-left: 7.5px;
                    margin-top: -3px;
                    margin-right: 7.5px;
                    text-indent: -9000px;
                    vertical-align: middle;
                    text-align: center;
                    width: 18px;
                    border: 0;

                    &-delete {
                      background-image: url(@assets/png/ico_trash.png);
                    }
                  }
                }
              }
            }
          }
        }
      }

      &--tearsheetOverview {
        .list {
          &__item {
            &-header {
              display: grid;
              grid-template-columns: 86px 1fr 1fr 1fr 0.8fr 145px 0.9fr 0.85fr 0.8fr 0.8fr 125px 125px;
            }
          }
        }
      }

      .vinculated-shareholders {
        &.list-group {
          min-width: 100%;
          max-width: 3000px;
          max-height: inherit;
          display: inline-block;
          border-bottom: 0;
        }

        .list__item {
          display: grid;
          grid-template-columns: 1fr repeat(2, 110px) 95px 145px 75px 70px;
          align-items: center;
          width: 100%;
          gap: 10px;
          border-bottom: 1px solid variables.$simpleListBorderColor;
          padding-left: 0;

          span {
            display: flex;
            align-items: center;
            padding: 0;
            min-width: auto;
            color: white;
            width: auto;
            height: 49px;

            &.name {
              position: sticky;
              left: 0;
              padding-left: 20px;
            }

            &.green {
              color: variables.$postive-value-color;
            }

            &.red {
              color: variables.$negative-value-color;
            }

            &.volume,
            &.variation,
            &.purchase,
            &.sale {
              justify-content: flex-end;
            }

            &.actions {
              display: flex;
              align-items: center;

              .icon {
                background-position: center;
                background-repeat: no-repeat;
                cursor: pointer;
                display: inline-block;
                height: 18px;
                margin-left: 7.5px;
                margin-top: -3px;
                margin-right: 7.5px;
                text-indent: -9000px;
                vertical-align: middle;
                text-align: center;
                width: 18px;
                border: 0;
              }

              .icon-delete {
                background-image: url(@assets/png/ico_trash.png);
              }
            }
          }

          &-header {
            position: sticky;
            top: 0;
            z-index: 5;
            background-color: #1d2027;

            span {
              background: variables.$base-list-bg;

              &.title {
                display: flex;
                align-items: center;
                color: variables.$main-light-blue;
                font-weight: 700;
                font-size: 14px;
                font-size: 0.875rem;
              }
            }

            &::before {
              bottom: 0;
              content: '';
              height: 70px;
              left: 0;
              position: absolute;
              right: 0;
              z-index: -1;
              background-color: variables.$base-list-bg;
            }
          }
        }
      }
    }

    &__ContentItem {
      position: relative;
      width: 100%;
      overflow: auto;

      select {
        border: none;
        background: transparent url(@assets/png/arrow-down.png) no-repeat right 5px center;
        border-bottom: 1px solid variables.$simpleListBorderColor;
        border-radius: 0;
        color: #fff;
        font-size: 14px;
        min-width: 100px;
        width: auto;
        height: 18px;
        margin-left: auto;
        padding: 0;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        position: relative;
        z-index: 4;

        option {
          color: black;
        }
      }

      &.private {
        .basic-filter-content {
          .filters {
            width: 100%;

            .content {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
            }

            .filter-by {
              display: flex;
              justify-content: flex-end;
              padding-top: 0;

              select {
                &.change-view {
                  padding-top: 0;
                  padding-left: 5px;
                  height: 34px;
                  width: 100px;
                  margin-right: 15px;
                }
              }

              .toggle-view-position-overview {
                position: absolute;
                left: 0px;
                top: 50px;

                .toggle-view-link {
                  text-decoration-line: underline;
                  color: variables.$main-light-blue;
                  padding-left: 0px;
                }
              }

              &.refresh {
                float: right;
                height: 32px;
                margin: 0 20px 0 0;
                padding-top: 0;
                flex: 1;
              }
            }

            .history-summary,
            .period-summary {
              padding-left: 20px;
              min-width: 417px;
              max-width: 500px;

              p {
                color: variables.$main-light-blue;
                font-weight: bold;
                padding: 0;
                font-size: 12px;
                font-size: 0.75rem;
              }

              span {
                color: #fff;
                display: inline-block;
                font-size: 12px;
                font-size: 0.75rem;
                width: 50%;

                strong {
                  &.green {
                    color: variables.$postive-value-color;
                  }

                  &.red {
                    color: variables.$negative-value-color;
                  }
                }
              }
            }
          }
        }

        .content-graph {
          width: 100%;

          .content-text {
            color: white;
            line-height: 1.3;
            margin-bottom: 15px;
            white-space: pre-wrap;
            text-align: center;
            margin-bottom: 0;
            font-size: 15px;
            font-size: 0.9375rem;
          }
        }
      }
    }

    &__full {
      width: 100%;
      padding-right: 20px;
      padding-left: 20px;
    }

    &__modal {
      background-color: rgba(0, 0, 0, 0.8);
      height: 100%;
      left: 0;
      overflow: auto;
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 9999;

      .form-config {
        background-color: #fff;
        left: 50%;
        max-width: 550px;
        padding: 40px;
        position: absolute;
        top: 50%;
        width: 100%;
        background-color: variables.$base-list-bg;
        -moz-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);

        .close {
          border: 1px solid variables.$simpleListBorderColor;
          background-color: variables.$base-list-bg;
          background: url(@assets/png/close-white.png) no-repeat center center;
          cursor: pointer;
          height: 42px;
          position: absolute;
          right: 0;
          text-indent: -9999px;
          top: 0;
          width: 42px;
        }

        h3 {
          color: white;
          font-size: 20px;
          font-size: 1.25rem;
          line-height: 30px;
          margin-bottom: 20px;
        }

        .text {
          span {
            color: variables.$main-light-blue;
            display: block;
            font-weight: 700;
            margin-bottom: 4px;
            font-size: 14px;
            font-size: 0.875rem;
          }

          input {
            background-color: transparent;
            border: none;
            border-radius: 0;
            color: #fff;
            border-bottom: 1px solid variables.$simpleListBorderColor;
            font-family: 'Lato';
            height: 27px;
            width: 100%;
            padding: 0;
            font-size: 14px;
            font-size: 0.875rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
          }

          &__search {
            flex: 1;
            display: block;
          }
        }

        .btn {
          margin-left: 15px;
        }

        .steps-actions-control {
          padding: 0;
          display: flex;
          justify-content: space-between;

          .bt {
            cursor: pointer;
            border: 0;
            color: #fff;
            display: inline-block;
            font-size: 13px;
            font-size: 0.8125rem;
            font-weight: 700;
            height: 35px;
            line-height: 35px;
            text-align: center;
            width: 127px;

            &.gray {
              background-color: #878787;
            }

            &:disabled {
              pointer-events: none;
            }
          }
        }
      }
    }

    &__InstitutionSearch {
      height: calc(100% - 85px);
      margin-bottom: 15px;
      padding: 0;
    }

    &__ModalSearch {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  #shareholderOverviewSummary {
    .filter-by {
      min-width: inherit;
      float: none;
      flex: inherit;
    }
  }

  #shareholderOverviewSummary {
    height: auto;
    padding: 0 20px;
    background: transparent;
  }

  .toggle-view-position-classic-simple {
    position: absolute;
    right: 65px;
    top: 50px;

    .toggle-view-link {
      text-decoration-line: underline !important;
      color: variables.$main-light-blue !important;
      padding-left: 0px !important;
    }
  }
}
