@use 'sass:color';
@use '../utils/variables';
@use '../components/intelligenceToolTip';

#shareholdersUI {
  .chart-tooltip {
    tr {
      td {
        display: inline-flex;
        align-items: center;
        vertical-align: bottom;

        &.label {
          margin-right: 3px;
          color: variables.$main-light-blue;
          font-weight: bold;
        }

        &.value {
          font-weight: bold;
        }
      }
    }
  }

  .main-wrapper {
    padding: variables.$units-lg;
    padding-bottom: 0;
    height: 100%;
    overflow: hidden;
    background-color: variables.$page-bg;

    .content-wrapper {
      height: 100%;
      overflow: hidden;
      color: white;
      display: block;

      .grey-wrapper {
        padding: 20px 20px;
        width: 100%;
        height: 60px;
        border-radius: variables.$units-md;
        color: #fff;
        background-color: variables.$base-list-bg;

        .companyData {
          .label {
            margin-right: 5px;
            color: variables.$main-light-blue;
            font-size: 16px;
          }

          label {
            display: inline-block;
          }

          .buttons {
            display: block;
            position: relative;
            float: right;
            top: -5px;

            .button {
              margin-left: 4px;
              padding: 0 10px;
              display: inline-block;
              color: #fff;
              max-height: 35px;
              line-height: 34px;
              min-width: 250px;
              text-align: center;
              background-color: variables.$main-light-blue;
              font-weight: 700;
              cursor: pointer;
              font-size: 17px;
              font-size: 1.0625rem;
            }
          }
        }
      }

      .charts-wrapper {
        display: inline-flex;
        justify-content: space-between;
        flex: 1 1;
        width: 100%;
        column-gap: variables.$units-md;

        .chart-wrapper {
          // padding: 20px 5px 5px 5px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 50%;
          height: 300px;
          border-radius: variables.$units-md;
          background-color: variables.$base-list-bg;
          position: relative;

          .chart-title {
            margin-bottom: 15px;
            width: 100%;
            color: variables.$main-light-blue;
            text-align: center;
            display: inline-block;
            font-size: 17px;
            font-weight: 600;

            + div {
              position: relative;
              top: -4px;
            }
          }

          &:last-child {
            margin-right: 0;
          }
        }

        &.company-tearsheet-irm {
          .chart-wrapper {
            height: calc(100vh - 235px);
          }
        }
      }

      .simple-info {
        &-line-wrapper {
          display: block;
          background-color: variables.$base-list-bg;
          border-radius: variables.$units-md;

          img {
            margin-left: 3px;
            width: 14px;
            height: 14px;
            vertical-align: bottom;
          }

          &.last {
            margin: variables.$units-md 0;
          }
        }

        &-items-wrapper {
          display: flex;
          justify-content: space-between;

          .simple-info {
            padding: 20px 20px;
            margin-right: 20px;
            width: 100%;
            display: flex;
            flex: 1 1;
            justify-content: flex-start;

            span {
              display: block;
              font-size: 16px;

              &.label {
                margin-bottom: 5px;
                color: variables.$main-light-blue;
                font-size: 16px;
                font-weight: 600;
              }

              &.value {
                &.red {
                  color: variables.$negative-value-color;
                }

                &.green {
                  color: variables.$postive-value-color;
                }
              }
            }

            &:first-child {
              border-radius: variables.$units-md 0 0 variables.$units-md;
            }

            &:last-child {
              margin-right: 0;
              border-radius: 0 variables.$units-md variables.$units-md 0;
            }

            &:hover {
              background-color: color.adjust(variables.$sideMenu-bg, $lightness: 7%);
            }
          }
        }
      }
    }
  }
}
