@use '../utils/variables';

#shareholdersUI {
  .shareholders-content {
    .scroll {
      .pie-chart {
        background-color: variables.$base-list-bg;
        margin-top: 0;
        min-height: 100%;
        position: relative;
        border-radius: variables.$units-md;
        padding: variables.$units-xs;

        > p {
          color: white;
          font-size: 18px;
          text-align: center;
          padding-top: 20px;
        }

        &-header {
          display: flex;
          padding: 15px 20px;
          justify-content: end;
          z-index: 2;
          position: relative;
        }

        .table-content {
          .titulo {
            color: white;
            font-size: 15px;
            font-weight: 700;
            margin-top: -10px;
            margin-bottom: 13px;
            padding-left: 20px;
          }

          .header-basic-list {
            position: relative;
            padding: 0;
            z-index: 1;
            border-top: 1px solid variables.$simpleListBorderColor;
          }

          .basic-list {
            overflow: inherit;
            padding-right: 4px;
            position: relative;
            padding: 0;

            li {
              min-height: 48px;
              position: relative;
              border-bottom: 1px solid variables.$simpleListBorderColor;

              span {
                color: white;
                display: inline-block;
                font-size: 14px;
                font-size: 0.875rem;
                height: 100%;
                line-height: 48px;
                overflow: hidden;
                padding-left: 8px;
                text-overflow: ellipsis;
                vertical-align: top;
                white-space: nowrap;

                &.name {
                  width: 50%;
                  padding-left: 20px;
                }

                &.value {
                  width: 50%;
                  padding-left: 8px;
                }
              }

              &.header {
                min-height: 47px;
                border-bottom: 0;

                span {
                  color: variables.$main-light-blue;
                  font-weight: bold;
                  line-height: 47px;

                  &.name {
                    width: 50%;
                    padding-left: 20px;
                  }

                  &.value {
                    width: 50%;
                    padding-left: 8px;
                  }
                }
              }
            }
          }
        }

        &:first-child {
          margin-top: 0;
        }
      }

      .charts-container {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .pie-chart {
          flex: 1;
          margin-top: 0;

          &:first-child {
            margin-right: variables.$units-md;
          }
        }

        &:first-child {
          margin-bottom: variables.$units-md;
        }
      }
    }
  }
}
