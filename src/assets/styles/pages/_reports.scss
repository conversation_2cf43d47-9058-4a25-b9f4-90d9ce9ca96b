@use '../utils/variables';

$gridColumns: 32px 1fr 120px 70px 180px 180px 180px;
$gridColumnsSimple: 32px 1fr 140px 70px 190px 90px;
$gridColumnsSimple2: 32px 1fr 140px 120px 70px 140px 140px 145px;
$gridColumnsSimple3: 32px 1fr 140px 120px 70px 110px 150px;

#shareholdersUI {
  .shareholders-content {
    > div {
      .shareholder-report,
      .report-placeholder {
        border-radius: variables.$units-md;
      }

      .report-placeholder {
        background-color: variables.$base-list-bg;
        margin-top: variables.$units-md;
        height: calc(100% - 95px);

        p {
          color: white;
          font-size: 18px;
          font-size: 1.125rem;
          text-align: center;
          padding-top: 20px;
        }
      }
    }
  }

  .base {
    &-list {
      &.shareholder-report {
        li {
          &:last-of-type {
            border-radius: 0 0 variables.$units-md variables.$units-md;
          }
        }

        li:not(.list-loading) {
          &,
          &.header-title,
          .sub-item {
            grid-template-columns: $gridColumns;
            border-radius: 0;
            border-bottom: 0;
          }

          .ReactCollapse {
            &--collapse {
              grid-column-start: 1;
              grid-column-end: 7;

              &.type-2 {
                grid-column-end: 8;
              }
            }
          }
        }

        &.simple {
          li:not(.list-loading) {
            &,
            &.header-title,
            .sub-item {
              grid-template-columns: $gridColumnsSimple;
              border-radius: 0;
              border-bottom: 0;
            }
          }
        }

        &.simple2 {
          li:not(.list-loading) {
            &,
            &.header-title,
            .sub-item {
              grid-template-columns: $gridColumnsSimple2;
              border-radius: 0;
              border-bottom: 0;
            }
          }
        }

        &.simple.simple2 {
          li:not(.list-loading) {
            &,
            &.header-title,
            .sub-item {
              grid-template-columns: $gridColumnsSimple3;
              border-radius: 0;
              border-bottom: 0;
            }
          }
        }
      }
    }
  }
}
