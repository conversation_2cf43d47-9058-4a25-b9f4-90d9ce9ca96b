@use '../utils/variables';
@use '../utils/mixins';

#shareholdersUI {
  .ContactNew {
    background-color: variables.$page-bg;
    height: 100%;
    padding: 25px 35px;

    &__btn {
      display: inline-block;
      font-weight: 700;
      text-align: center;
      line-height: 32px;
      text-transform: uppercase;
      color: #fff;
      min-width: 160px;
      height: 32px;
      margin-left: 15px;
      padding-left: 20px;
      padding-right: 20px;
      border: 0;
      background-color: variables.$main-light-blue;
      cursor: pointer;
      font-size: 13px;

      &.delete {
        margin-right: 15px;
        background-color: #f54336;
      }

      &:focus,
      &:active {
        outline: 0;
      }
    }

    &__ContentItem {
      position: relative;
      width: 100%;

      &.buttons {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 15px;
        width: auto;

        .btn {
          font-size: 12px;
          min-width: 80px;
        }
      }

      &.center {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;

        .ContactNew__btn,
        .btn__primary {
          margin-top: 15px;
        }
      }

      select {
        background: transparent url(@assets/png/arrow-down.png) no-repeat right 5px center;
        border: none;
        border-bottom: 1px solid #344b69;
        border-radius: 0;
        color: #fff;
        font-size: 14px;
        font-size: 0.875rem;
        width: 200px;
        height: 18px;
        margin-left: auto;
        padding: 0;
        vertical-align: top;
        @include mixins.reset-form-style;

        &:focus {
          outline: none;
        }

        option {
          color: #000;
        }
      }
    }

    &__ContentItem + &__ContentItem {
      margin-top: 30px;
    }

    &__graphs {
      display: grid;
      grid-template-columns: calc(50% - 15px) calc(50% - 15px);
      grid-column-gap: 30px;
      grid-row-gap: 15px;
      margin-top: 30px;
      margin-bottom: 15px;

      @media (max-width: 1280px) {
        grid-template-columns: 1fr;
        grid-column-gap: 0;
      }

      .ContactNew__ContentItem + .ContactNew__ContentItem {
        margin-top: 0;
      }

      .content-text {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        font-weight: bold;
        // text-align: center
      }
    }

    &__PortfolioAnalysis {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .content-title {
        @media (max-width: 1280px) {
          max-width: 500px;
          line-height: 28px;
        }
      }

      > div {
        @media (max-width: 1280px) {
          display: grid;
          grid-template-columns: 1fr;
          grid-row-gap: 10px;
        }

        .ContactNew__btn.delete {
          @media (max-width: 1280px) {
            margin-right: 0;
          }
        }
      }
    }

    &__search {
      margin-bottom: 15px;
      overflow: hidden;
      // height: calc(100% - 95px);
      height: 400px;

      .search {
        &__list {
          display: inline-block;
          height: auto;
          width: 100%;
          margin-top: 20px;
          background-color: #2e3137;
        }

        &__ListItem {
          cursor: pointer;
          display: grid;
          grid-gap: 10px;
          grid-auto-flow: column;
          grid-auto-columns: 1fr;
          grid-template-columns: 2fr;
          padding: 10px;
          border-bottom: 1px solid variables.$simpleListBorderColor;

          &.institutions-search {
            grid-auto-columns: 0.5fr;
          }

          &:hover {
            background-color: rgba(116, 116, 116, 0.1);
          }

          &.active {
            background-color: #33a0ff;
          }

          &.header {
            cursor: default;
            font-weight: 700;
            border-bottom: 0;
            box-shadow: 0 5px 15px 0 rgb(27 31 37 / 50%);
            background-color: #282d39;

            &:hover {
              background-color: #282d39;
            }
          }

          &.NoResults {
            cursor: default;

            .newContact {
              color: #33a0ff;
              cursor: pointer;
            }
          }

          span {
            display: inline;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 18px;
          }
        }

        &__ListResults {
          max-height: calc(100% - 50px);
          min-height: 30px;
          overflow: auto;
          @include mixins.scroll(variables.$base-list-bg, 4px, variables.$main-light-blue, variables.$main-light-blue);
        }
      }
    }

    .basic-filter-content {
      padding: 0;
      margin-bottom: 20px;
    }

    .contact {
      &__info {
        color: #fff;
        line-height: 1.2;
        margin-bottom: 3px;
        font-size: 14px;
        font-size: 0.875rem;
        letter-spacing: 0.4px;
      }

      &__picture {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;

        img {
          width: 100%;
        }
      }
    }

    .content {
      &-title {
        color: #339fff;
        font-weight: 700;
        margin-bottom: 15px;
        @include mixins.font-size(20);
      }

      &-text {
        color: #fff;
        line-height: 1.3;
        margin-bottom: 15px;
        white-space: pre-wrap;
        margin-bottom: 0;
        @include mixins.font-size(15);
      }
    }

    .form-config {
      &.dark {
        .btn {
          &:disabled {
            pointer-events: none;
          }
        }
      }
    }
  }

  .steps-actions-control {
    text-align: right;

    .bt {
      cursor: pointer;
      border: 0;
      color: #fff;
      display: inline-block;
      font-size: 13px;
      font-weight: 700;
      height: 35px;
      line-height: 35px;
      text-align: center;
      width: 127px;

      &.blue {
        background-color: variables.$main-light-blue;
        overflow: hidden;
        position: relative;
        -moz-transition: all 0.4s;
        -o-transition: all 0.4s;
        -webkit-transition: all 0.4s;
        -ms-transition: all 0.4s;
        transition: all 0.4s;
      }
    }
  }
}
