@use '../utils/variables';

#shareholdersUI {
  .shareholders-content {
    .shareholder-monitored {
      .wait-load {
        background-color: variables.$base-list-bg;
        min-height: calc(100% - 50px);
        position: relative;
        p {
          color: white;
          text-align: center;
          padding-top: 20px;
          font-weight: bold;
          font-size: 16px;
        }
      }
      .monitored-list-content {
        background-color: variables.$page-content-bg;
        min-height: 195px;
        margin-top: 20px;
        .title {
          height: 49px;
          border-bottom: 1px solid variables.$simpleListBorderColor;
          padding-left: 20px;
          h6 {
            color: white;
            font-weight: 700;
            line-height: 48px;
            font-size: 14px;
            font-size: 0.875rem;
          }
        }
        .monitored-list {
          li {
            display: grid;
            min-height: 47px;
            grid-column-gap: 20px;
            grid-template-columns: 1fr 160px 125px 130px;
            span {
              &.name {
                left: 0;
                padding-left: 20px;
                width: auto;
              }
              &.mov {
                &.up {
                  color: variables.$postive-value-color;
                }
                &.down {
                  color: variables.$negative-value-color;
                }
              }
            }
            &.no-results {
              grid-template-columns: 1fr;
            }
          }
        }
        &:first-child {
          margin-top: 0;
        }
      }
      .btns-content {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 20px;
        .btn__primary {
          margin-right: 15px;
        }
      }
    }
  }
}
