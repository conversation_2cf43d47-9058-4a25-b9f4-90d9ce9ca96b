@use '../utils/variables';

#shareholdersUI {
  .ownership-base {
    border-radius: variables.$units-md;

    .base-list {
      li {
        &:not(.sub-item) {
          &:last-of-type {
            border-radius: 0 0 variables.$units-md variables.$units-md;
          }
        }
      }

      &.shareholder-base-list {
        li {
          .history-detail {
            grid-column-start: 1;
            grid-column-end: 7;
          }

          .sub-item {
            span {
              &.name {
                padding-left: 52px;
              }
            }
          }
        }

        &.grouped {
          li:not(.list-loading) {
            grid-template-columns: 32px 1fr 140px 55px 150px 160px;
            // border-top: 1px solid variables.$simpleListBorderColor;
            border-bottom: 0;

            &.header-title {
              grid-template-columns: 1fr 140px 55px 150px 160px;
              border-top: 0;

              span {
                &.name {
                  padding-left: 52px;
                  left: 0;
                }

                &.volume-last-value {
                  display: flex;
                  align-items: center;
                }
              }
            }

            .sub-item {
              grid-template-columns: 1fr 139px 57px 150px 160px;
            }

            &.list-loading {
              border-bottom: 0;
              grid-template-columns: 1fr;
              padding: 40px;

              &:hover {
                background: initial;
              }
            }
          }
        }

        &.simple {
          li:not(.list-loading) {
            grid-template-columns: 32px 1fr 120px 130px 120px 55px 150px 160px;

            &.header-title {
              grid-template-columns: 1fr 120px 130px 120px 55px 150px 160px;
            }
          }
        }

        &.factSet {
          li:not(.list-loading) {
            border-top: 1px solid variables.$simpleListBorderColor;
            min-height: 49px;
            padding: 0 10px;
            justify-content: flex-start;
            grid-column-start: 1;
            grid-column-end: 10;
            grid-template-columns: 320px 80px 100px 140px 80px 80px 80px 95px 140px 150px;
            overflow: hidden;

            > span {
              &.has-tooltip {
                &:hover {
                  overflow: initial;
                  font-size: 0;

                  .tooltip {
                    visibility: visible;
                  }
                }
              }
            }

            .tooltip {
              position: absolute;
              background-color: #171717;
              border: 1px solid #0da3cc;
              left: 50%;
              top: 50%;
              visibility: hidden;
              z-index: 2;
              transform: translate(-50%, -50%);
              display: flex;
              align-items: center;
              padding: 15px;
              line-height: 1em;
              font-size: 14px;
            }

            &.header-title {
              grid-template-columns: 320px 80px 100px 140px 80px 80px 80px 95px 140px 150px;

              span {
                &.name {
                  background: variables.$base-list-bg;
                }

                img {
                  padding-left: 5px;
                }

                .tooltip {
                  top: 100%;
                  width: 200px;
                  transform: translate(-50%, 0);
                  padding-left: 15px;

                  &::before {
                    content: '';
                    width: 0;
                    height: 0;
                    border-style: solid;
                    border-width: 0px 5px 5px 5px;
                    border-color: transparent transparent variables.$main-light-blue transparent;
                    left: 50%;
                    position: absolute;
                    transform: translateX(-50%);
                    bottom: 100%;
                  }
                }

                &.mm {
                  position: relative;
                  display: flex;
                  align-items: center;

                  &:hover {
                    overflow: initial;

                    .tooltip {
                      visibility: visible;
                    }
                  }
                }
              }
            }

            &.factSet-item-list {
              span {
                &.name {
                  left: 0;
                  padding-left: 35px;
                  white-space: inherit;
                  background: variables.$base-list-bg;

                  a {
                    line-height: 1.5em;
                    display: block;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }

                  .open-drill-dawn {
                    padding-left: 0;
                    position: absolute;
                    left: 5px;
                    width: 17px;
                    top: 50%;
                    transform: translateY(-50%);
                  }
                }
              }

              .history-detail {
                grid-column-start: 1;
                grid-column-end: 12;
              }

              .sub-item {
                grid-template-columns: 320px 80px 100px 140px 80px 80px 80px 95px 140px 150px;
                padding: 0;
                border-bottom: 0;
              }

              &:hover {
                span {
                  &.name {
                    background: #282d39;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .ownership-scroll {
    max-width: calc(100vw - 265px);
    background-color: variables.$base-list-bg;

    .portfolioError {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 35px 0;

      h2 {
        margin-bottom: 16px;
      }
    }
  }
}
