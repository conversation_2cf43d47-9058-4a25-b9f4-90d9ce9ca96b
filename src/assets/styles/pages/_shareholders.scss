@use '../utils/variables';

#shareholdersUI {
  .shareholders-wrapper {
    &.new {
      .header-base-list {
        height: auto;
      }
    }

    .ShareholderNew {
      &__ListHeader,
      &__ListItem {
        display: grid;
        justify-content: space-around;
        align-items: center;
        grid-template-columns: 2fr 1fr;
        grid-column-gap: 10px;
        padding-left: 20px;

        a,
        span {
          color: white;
          cursor: default;
          font-size: 12px;
          font-size: 0.75rem;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        span {
          &.grouped,
          &.group-type {
            i {
              background: url(../../png/ico_aglutinar.png) no-repeat 0 0;
              cursor: pointer;
              display: inline-block;
              height: 18px;
              margin-left: -1px;
              opacity: 0.3;
              vertical-align: middle;
              width: 18px;
              visibility: visible;

              &:hover {
                opacity: 1;
              }
            }
          }
        }

        &.shareholder-list-page {
          grid-template-columns: 32px 2fr 130px 1fr 2fr;
        }
      }

      &__ListHeader {
        border-radius: variables.$units-md variables.$units-md 0 0;
        background: variables.$base-list-bg;
        width: 100%;
        height: 47px;
        padding-right: 20px;

        span {
          color: variables.$main-light-blue;
          font-weight: 700;
          font-size: 13px;
          font-size: 0.8125rem;
        }

        &::after {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          bottom: -30px;
          height: 60px;
          z-index: -1;
          pointer-events: none;
          background: -moz-radial-gradient(ellipse at 50% 15px, rgba(0, 0, 0, 0.57) 0%, rgba(0, 0, 0, 0) 76%);
          background: -webkit-radial-gradient(ellipse at 50% 15px, rgba(0, 0, 0, 0.57) 0%, rgba(0, 0, 0, 0) 76%);
          background: -o-radial-gradient(ellipse at 50% 15px, rgba(0, 0, 0, 0.57) 0%, rgba(0, 0, 0, 0) 76%);
          background: -ms-radial-gradient(ellipse at 50% 15px, rgba(0, 0, 0, 0.57) 0%, rgba(0, 0, 0, 0) 76%);
          background: radial-gradient(ellipse at 50% 15px, rgba(0, 0, 0, 0.57) 0%, rgba(0, 0, 0, 0) 76%);
          background-color: rgba(0, 0, 0, 0.57);
          background-color: transparent;
        }
      }

      &__ListItem {
        background-color: variables.$base-list-bg;
        grid-template-rows: 45px;
        min-height: 45px;
        border-bottom: 1px solid variables.$simpleListBorderColor;
        padding-right: 20px;

        a {
          cursor: pointer;
        }

        &:hover {
          background-color: variables.$sideMenu-bg;
        }

        span {
          &.group-type {
            align-items: center;
            display: flex;
            line-height: 46px;
            overflow: hidden;
            text-overflow: ellipsis;
            width: auto;
            white-space: nowrap;
          }
        }
      }
    }

    & .classification-list-header-actions {
      margin-left: auto;
    }

    & .classification-list-item-actions {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-left: auto;
      padding-right: 20px;
      cursor: pointer;

      & > svg {
        padding: 4px;
      }
    }

    & > div > .shareholder-listing-content {
      height: calc(100% - 110px);
      border-radius: 0 0 variables.$units-md variables.$units-md;
    }
  }

  .shareholder-summary,
  .charts-container {
    .highcharts-legend-item {
      text {
        font-weight: 400 !important;
        color: white !important;
        fill: white !important;

        &:hover {
          color: white !important;
          fill: white !important;
          transition: 0.3s;
          opacity: 0.8;
        }
      }
    }

    .highcharts-axis-labels {
      text {
        color: white !important;
        fill: white !important;
      }
    }
  }
}
