* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  text-decoration: none;
}

*:focus {
  outline: none;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  font-family: Lato;
  font-size: 14px;
}

#root,
.main-content,
.router-content,
.ui.grid {
  width: 100%;
  height: 100vh;
}

ul li {
  list-style: none;
}

a,
a:focus,
a:hover {
  text-decoration: none;
  color: #fff;
}

.scroll {
  height: calc(100% - 130px);
}

@media (max-width: 1280px) {
  .scroll {
    height: calc(100% - 150px);
  }
}
