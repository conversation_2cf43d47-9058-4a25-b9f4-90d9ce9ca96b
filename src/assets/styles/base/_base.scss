@use '../utils/mixinsLegacy';
@use '../utils/variables';
@use '../utils/variablesLegacy';

body,
html {
  height: 100%;
  overflow: hidden;
}

#root {
  height: 100vh;
}

//efeito para adicionar no content qndo abrir algum modal

.blur {
  filter: blur(15px);
}

// Conteiner and grid style

.order-by {
  cursor: pointer;

  &.pointerEvents {
    pointer-events: none;
  }
}

.container {
  @include mixinsLegacy.container-fixed;

  @media (min-width: variablesLegacy.$screen-sm-min) {
    width: variablesLegacy.$container-sm;
  }

  @media (min-width: variablesLegacy.$screen-md-min) {
    width: variablesLegacy.$container-md;
  }

  @media (min-width: variablesLegacy.$screen-lg-min) {
    width: variablesLegacy.$container-lg;
  }
}

// side voice search

//animeção voice icon
@-webkit-keyframes speaking {
  0% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat 0 0;
  }

  6% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -120px 0;
  }

  12% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -240px 0;
  }

  18% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -360px 0;
  }

  24% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -480px 0;
  }

  30% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -600px 0;
  }

  36% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -720px 0;
  }

  42% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -840px 0;
  }

  48% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -960px 0;
  }

  54% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1080px 0;
  }

  60% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1200px 0;
  }

  66% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1320px 0;
  }

  72% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1440px 0;
  }

  78% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1560px 0;
  }

  84% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1680px 0;
  }

  90% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1800px 0;
  }

  96% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1920px 0;
  }

  100% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -2040px 0;
  }
}

@-ms-keyframes speaking {
  0% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat 0 0;
  }

  6% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -120px 0;
  }

  12% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -240px 0;
  }

  18% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -360px 0;
  }

  24% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -480px 0;
  }

  30% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -600px 0;
  }

  36% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -720px 0;
  }

  42% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -840px 0;
  }

  48% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -960px 0;
  }

  54% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1080px 0;
  }

  60% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1200px 0;
  }

  66% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1320px 0;
  }

  72% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1440px 0;
  }

  78% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1560px 0;
  }

  84% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1680px 0;
  }

  90% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1800px 0;
  }

  96% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1920px 0;
  }

  100% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -2040px 0;
  }
}

@keyframes speaking {
  0% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat 0 0;
  }

  6% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -120px 0;
  }

  12% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -240px 0;
  }

  18% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -360px 0;
  }

  24% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -480px 0;
  }

  30% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -600px 0;
  }

  36% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -720px 0;
  }

  42% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -840px 0;
  }

  48% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -960px 0;
  }

  54% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1080px 0;
  }

  60% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1200px 0;
  }

  66% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1320px 0;
  }

  72% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1440px 0;
  }

  78% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1560px 0;
  }

  84% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1680px 0;
  }

  90% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1800px 0;
  }

  96% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -1920px 0;
  }

  100% {
    background: url(@assets/png/voice-icon-steps-02.png) no-repeat -2040px 0;
  }
}

// Columns
//
// Common styles for small and large grid columns
@include mixinsLegacy.make-grid-columns; // Extra small grid
//
// Columns, offsets, pushes, and pulls for extra small devices like smartphones.
@include mixinsLegacy.make-grid(xs); // Small grid
//
// Columns, offsets, pushes, and pulls for the small device range, from phones to tablets.
@media (min-width: variablesLegacy.$screen-sm-min) {
  @include mixinsLegacy.make-grid(sm);
}

// Medium grid
//
// Columns, offsets, pushes, and pulls for the desktop device range.
@media (min-width: variablesLegacy.$screen-md-min) {
  @include mixinsLegacy.make-grid(md);
}

// Large grid
//
// Columns, offsets, pushes, and pulls for the large desktop device range.
@media (min-width: variablesLegacy.$screen-lg-min) {
  @include mixinsLegacy.make-grid(lg);
}

// Extra large grid
//
// Columns, offsets, pushes, and pulls for the large desktop device range.
@media (min-width: variablesLegacy.$screen-xl-min) {
  @include mixinsLegacy.make-grid(xl);
}

// Others

a {
  color: inherit;
  text-decoration: none;
}

input,
select {
  border: 0;
}

select {
  @include mixinsLegacy.reset-form-style;
}

input {
  @include mixinsLegacy.input-placeholder {
    color: #bababa;
    @include mixinsLegacy.font-size(14);
  }
}

// actions buttons

.actions-control {
  bottom: 20px;
  position: fixed;
  right: 248px;
  width: 44px;
  z-index: 10;
  @include mixinsLegacy.transition(all 0.3s);

  li {
    height: 44px;
    margin-bottom: -55px;
    margin-top: 10px;
    opacity: 0;
    visibility: hidden;
    @include mixinsLegacy.transition(all 0.3s);
  }
}

// loading
@-webkit-keyframes lds-dual-ring {
  0% {
    @include mixinsLegacy.rotate(0);
  }

  100% {
    @include mixinsLegacy.rotate(360);
  }
}

@-ms-keyframes lds-dual-ring {
  0% {
    @include mixinsLegacy.rotate(0);
  }

  100% {
    @include mixinsLegacy.rotate(360);
  }
}

@keyframes lds-dual-ring {
  0% {
    @include mixinsLegacy.rotate(0);
  }

  100% {
    @include mixinsLegacy.rotate(360);
  }
}

.lds-dual-ring {
  height: 15px;
  margin: 0 auto;
  position: relative;
  width: 15px;

  div {
    position: absolute;
    width: 15px;
    height: 15px;
    // top: 10px;
    left: 0;
    border-radius: 50%;
    border: 2px solid #000;
    border-color: variables.$main-light-blue transparent variables.$main-light-blue transparent;
    -webkit-animation: lds-dual-ring 0.5s linear infinite;
    -ms-animation: lds-dual-ring 0.5s linear infinite;
    animation: lds-dual-ring 0.5s linear infinite;

    &.fix-top {
      top: 0;
    }
  }
}

// basic list

.basic-filter-content {
  background-color: #fff;
  height: 57px;
  padding: 0 35px;
  position: relative;
  z-index: 3;

  .filters {
    background-color: #f4f4f4;
    display: inline-block;
    height: 100%;
    vertical-align: top;
    width: calc(100% - 215px);

    .content {
      //display: table;
      height: 100%;
      width: 100%;

      > div {
        display: inline-block;
        height: 100%;
        position: relative;
        vertical-align: top;
        width: auto;
        min-width: 150px;

        .title {
          color: #fff;
          display: inline-block;
          @include mixinsLegacy.font-size(18);
          font-weight: 700;
          height: 100%;
          line-height: 57px;
          padding-left: 20px;
        }

        a,
        p {
          color: #fff;
          @include mixinsLegacy.font-size(15);
          font-weight: 400;
          height: auto;
          line-height: inherit;
          margin-left: 0;
          padding-left: 20px;
          text-align: left;
          text-transform: inherit;
          width: auto;
        }

        > .label {
          color: variables.$main-light-blue;
          @include mixinsLegacy.font-size(14);
          font-weight: 700;
          left: 0;
          padding: 10px 0 0 19px;
          position: absolute;
          top: 0;
        }

        &.search-field {
          float: right;
          padding: 12px 10px 10px;
          width: 270px;

          @media (max-width: 1170px) {
            width: 240px;
          }

          input {
            background-color: #000;
            border: none;
            color: #ffffff;
            @include mixinsLegacy.font-size(13);
            padding: 0 13px;

            @include mixinsLegacy.input-placeholder {
              color: #667;
              @include mixinsLegacy.font-size(13);
            }
          }
        }

        .date-content {
          cursor: pointer;
          display: inline-block;
          //height: 100%;
          //padding: 19px 0 5px;
          position: relative;
          //vertical-align: top;
          width: 50%;

          &.start {
            border-right: none;
            text-align: right;
            width: 91px;

            input {
              padding-left: 0;
              padding-right: 4px;
              text-align: right;
            }
          }

          &.end {
            border-left: none;
            padding-right: 0;
          }

          span {
            color: #fff;
            display: inline-block;
            @include mixinsLegacy.font-size(13);
            line-height: 15px;
            padding-top: 20px;
            vertical-align: middle;
          }

          input {
            background-color: transparent;
            border: none;
            color: #fff;
            display: inline-block;
            @include mixinsLegacy.font-size(13);
            height: auto;
            margin: 0 4px;
            padding: 30px 0 0 15px;
            vertical-align: middle;
            width: calc(100% - 8px);
          }
        }
      }
    }
  }

  > span,
  a {
    color: #fff;
    cursor: pointer;
    display: inline-block;
    @include mixinsLegacy.font-size(16);
    font-weight: 900;
    height: 57px;
    line-height: 57px;
    margin-left: 15px;
    text-align: center;
    text-transform: uppercase;
    width: 200px;

    &.bt-basic-add {
      background-color: variables.$main-light-blue;
    }

    &.bt-basic-remove {
      background-color: #d0021b;
    }

    &.bt-basic-export {
      background-color: #4da178;
    }

    &:focus {
      outline: none;
    }

    &.disabled {
      background-color: #666;
      pointer-events: none;
    }
  }

  .content-bts {
    display: inline-block;
    margin-left: 15px;
    vertical-align: top;
    width: 200px;

    a {
      height: 23px;
      font-weight: 700;
      line-height: 23px;
      margin-left: 0;
      text-transform: capitalize;
      @include mixinsLegacy.font-size(14);

      &:first-child {
        margin-bottom: 11px;
      }
    }
  }

  &.dark {
    background-color: variables.$base-list-bg;

    .filters {
      background-color: variables.$base-list-bg;
    }
  }
}

.header-basic-list {
  padding: 10px 35px 0;

  .basic-list {
    padding-top: 0;

    li {
      border-bottom: none;
    }

    &.dark {
      li {
        border-bottom: none;
      }
    }
  }
}

.basic-list {
  background-color: #fff;
  padding-top: 4px;

  li {
    border-bottom: 1px solid #f4f4f4;
    min-height: 48px;
    position: relative;
    list-style: none;

    &:hover {
      background-color: #282a33;
    }

    &.header {
      border-width: 0 0 1px 0;
      min-height: 47px;

      &:hover {
        background-color: transparent;
      }

      span {
        color: variables.$main-light-blue;
        font-weight: bold;
        line-height: 47px;
      }

      .order-by {
        display: inline-block;
        border: 5px solid #878787;
        height: 0;
        width: 0;
        margin: 0 0 0 4px;
      }

      &[class$='-asc'] {
        .order-by {
          border-color: #878787 transparent transparent;
          vertical-align: -3px;
        }
      }

      &[class$='-desc'] {
        .order-by {
          border-color: transparent transparent #878787;
          vertical-align: 1px;
        }
      }
    }

    span {
      color: #747474;
      display: inline-block;
      @include mixinsLegacy.font-size(14);
      // height: 100%;
      line-height: 48px;
      overflow: hidden;
      padding-left: 8px;
      text-overflow: ellipsis;
      vertical-align: top;
      white-space: nowrap;
    }

    &.no-results {
      color: #565656;
      padding: 20px 0;
      @include mixinsLegacy.font-size(14);
      text-align: center;
    }
  }

  &.dark {
    background-color: variables.$base-list-bg;

    li {
      border-bottom: 1px solid #344b69;

      &.header {
        border-width: 0 0 1px 0;

        span {
          color: variables.$main-light-blue;

          .badge-items-cvm {
            background-color: #fff;
            color: #333;
          }
        }

        &[class$='-asc'] {
          .order-by {
            border-color: transparent transparent variables.$main-light-blue;
            vertical-align: 0px;
          }
        }

        &[class$='-desc'] {
          .order-by {
            border-color: variables.$main-light-blue transparent transparent;
            vertical-align: 0px;
          }
        }
      }

      span {
        color: #fff;
      }

      &.no-results {
        color: #fff;
      }
    }
  }
}

@-webkit-keyframes errorBefore {
  0% {
    left: 10px;
  }

  50% {
    left: 30px;
  }

  0% {
    left: 10px;
  }
}

@-moz-keyframes errorBefore {
  0% {
    left: 10px;
  }

  50% {
    left: 30px;
  }

  0% {
    left: 10px;
  }
}

@-o-keyframes errorBefore {
  0% {
    left: 10px;
  }

  50% {
    left: 30px;
  }

  0% {
    left: 10px;
  }
}

@keyframes errorBefore {
  0% {
    left: 10px;
  }

  50% {
    left: 30px;
  }

  0% {
    left: 10px;
  }
}

@-webkit-keyframes errorAfter {
  0% {
    left: 50px;
  }

  50% {
    left: 70px;
  }

  0% {
    left: 50px;
  }
}

@-moz-keyframes errorAfter {
  0% {
    left: 50px;
  }

  50% {
    left: 70px;
  }

  0% {
    left: 50px;
  }
}

@-o-keyframes errorAfter {
  0% {
    left: 50px;
  }

  50% {
    left: 70px;
  }

  0% {
    left: 50px;
  }
}

@keyframes errorAfter {
  0% {
    left: 50px;
  }

  50% {
    left: 70px;
  }

  0% {
    left: 50px;
  }
}

.align-right {
  text-align: right;
  margin-right: 20px;
}
