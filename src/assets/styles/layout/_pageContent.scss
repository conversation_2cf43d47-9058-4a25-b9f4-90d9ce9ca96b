@use '../utils/variables';
@use '../utils/mixinsLegacy';

.shareholders {
  &-wrapper {
    height: 100%;
  }

  &-content {
    height: calc(100vh - 80px);
    overflow: hidden;
    padding: variables.$units-lg;
    background-color: variables.$page-bg;

    > div {
      height: 100%;
    }

    .scroll {
      height: calc(100% - 90px);
      overflow: auto;
      padding: 0;
      @include mixinsLegacy.scroll(
        variables.$base-list-bg,
        4px,
        variables.$main-light-blue,
        variables.$main-light-blue
      );
      @include mixinsLegacy.scroll-horizontal(
        variables.$base-list-bg,
        4px,
        variables.$main-light-blue,
        variables.$main-light-blue
      );
    }

    .report-scroll {
      @media (max-width: 1920px) {
        padding-bottom: 1.5rem;
      }

      @media (max-width: 1280px) {
        height: calc(100% - 150px);
        padding-bottom: 2rem;
      }
    }

    &-monitoring {
      padding-top: 12px;
    }
  }
}
