@use '../utils/mixinsLegacy';
@use '../utils/variables';

.ReactModalPortal {
  .modal {
    &-overlay {
      background-color: rgba(0, 0, 0, 0.8);
      bottom: 0px;
      left: 0px;
      position: fixed;
      right: 0px;
      top: 0px;
      z-index: 9999;
      overflow: auto;

      &.centered-modal {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.centered-modal {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin-right: 0;
  margin-bottom: 0;
  background-color: transparent;
  transform: translate(0);
}

// Modal
.contact-activity-modal {
  left: 0;
  margin: 0;
  top: 0;
  bottom: auto;
  outline: none;
  position: absolute;
  right: auto;
  width: 100%;
  height: 100%;
  @include mixinsLegacy.translate(0, 0);
  background-color: transparent !important;
}

.centered-modal {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin-right: 0;
  margin-bottom: 0;
  background-color: transparent;
  transform: translate(0);
}

.activity-actions-modal {
  width: 728px;
  left: 50%;
  padding: 40px;
  position: absolute;
  top: 50%;
  @include mixinsLegacy.translate(-50%, -50%);

  &.private-tearsheet {
    width: 900px;
  }

  &.dark {
    background-color: variables.$base-list-bg;

    .close {
      border: 1px solid #344b69;
      background: #fff url(@assets/png/close-white.png) no-repeat center;
      background-color: variables.$base-list-bg;
    }

    .content {
      h1 {
        color: #fff;
        font-size: 1.25rem;
        line-height: 30px;
        margin-bottom: 20px;
        text-align: left;
      }

      .search-field {
        color: #fff;
        border-bottom: 1px solid #344b69;

        @include mixinsLegacy.input-placeholder {
          color: #fff;
        }
      }

      .list-search-result {
        background-color: #2e3136;

        &.scroll {
          @include mixinsLegacy.scroll(#656565, 4px, variables.$main-light-blue, variables.$main-light-blue);
        }

        li {
          border-top: 1px solid #344b69;

          &:hover {
            background-color: rgba(116, 116, 116, 0.1);
          }

          &.header {
            font-weight: 700;
            border-bottom: 0;
            box-shadow: 0 5px 15px 0 rgba(27, 31, 37, 0.5);
            background-color: #282d39;
            color: #fff;

            &:hover {
              background-color: #282d39;
            }
          }

          span {
            color: #fff;

            a,
            span {
              color: variables.$main-light-blue;
            }
          }
        }
      }
    }
  }

  .close {
    background: #fff url(@assets/png/close.png) no-repeat center;
    border: 1px solid #979797;
    height: 42px;
    position: absolute;
    right: 0;
    text-indent: -9999px;
    top: 0;
    width: 42px;
    cursor: pointer;
  }

  .content {
    h1 {
      color: #39393a;
      @include mixinsLegacy.font-size(40);
      line-height: 46px;
      margin-bottom: 6px;
      text-align: center;

      &.modal-title {
        margin-bottom: 30px;
      }
    }

    .search-box {
      margin-top: 30px;

      .search-btn {
        background-color: variables.$main-light-blue;
        border: 0;
        color: #fff;
        display: inline-block;
        @include mixinsLegacy.font-size(14);
        font-weight: 700;
        height: 28px;
        line-height: 28px;
        margin-left: 15px;
        text-align: center;
        width: 135px;
        cursor: pointer;

        &:disabled {
          opacity: 0.6;
          pointer-events: none;
        }
      }
    }

    .search-field {
      border: none;
      border-bottom: 1px solid #666;
      color: #333;
      @include mixinsLegacy.font-size(16);
      height: 28px;
      padding: 0;

      @include mixinsLegacy.input-placeholder {
        color: #555;
        @include mixinsLegacy.font-size(16);
      }
    }

    .checkbox {
      display: inline-block;
      cursor: pointer;
      margin-right: 25px;

      &.radio {
        input {
          ~ span {
            &::before {
              border-radius: 50%;
            }
          }
        }
      }

      input {
        opacity: 0;
        position: absolute;
        visibility: hidden;

        ~ span {
          color: #333;
          cursor: pointer;
          display: inline-block;
          @include mixinsLegacy.font-size(15);
          overflow: hidden;
          padding-left: 1px;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;

          &::before {
            border: 1px solid #333;
            content: '';
            display: inline-block;
            height: 16px;
            margin-right: 5px;
            vertical-align: bottom;
            width: 16px;
            @include mixinsLegacy.transition(all 0.3s);
          }
        }

        &:checked {
          ~ span {
            &::before {
              background-color: variables.$main-light-blue;
              border-color: variables.$main-light-blue;
            }
          }
        }
      }
    }

    .list-search-result {
      margin-top: 20px;

      &.header-title {
        //padding-right: 4px;
      }

      &.scroll {
        // margin: 0 -4px 0 0;
        margin: 0;
        overflow-y: auto;
        max-height: 195px;
        // min-height: 42px;
        // min-height: 195px;
        @include mixinsLegacy.scroll(#fff, 4px, variables.$main-light-blue, variables.$main-light-blue);
      }

      li {
        height: 39px;
        border-top: 1px solid #c4c4c4;
        list-style: none;

        &.header {
          border-top: none;
          height: 38px;

          span {
            font-weight: 700;
          }
        }

        span {
          display: inline-block;
          @include mixinsLegacy.font-size(13);
          height: 100%;
          line-height: 38px;
          overflow: hidden;
          padding-left: 8px;
          text-overflow: ellipsis;
          vertical-align: top;
          white-space: nowrap;

          > span {
            cursor: pointer;
          }

          i {
            background-color: variables.$main-light-blue;
            border-radius: 50%;
            color: #fff;
            cursor: default;
            display: inline-block;
            @include mixinsLegacy.font-size(16);
            font-style: normal;
            font-weight: 700;
            height: 16px;
            line-height: 13px;
            margin: 0 0 0 3px;
            padding: 0 1px 0 0;
            text-align: center;
            width: 16px;
          }

          &.name {
            width: calc(100% - (240px + 150px + 90px));
          }

          &.email {
            width: 240px;
          }

          &.fund {
            width: 150px;
          }

          &.action {
            width: 90px;

            a,
            span {
              color: variables.$main-light-blue;
              padding: 0;
            }
          }
        }
      }

      &.my-contacts {
        li {
          span {
            &.name {
              width: calc(100% - (280px + 90px));
            }

            &.email {
              width: 280px;
            }

            &.action {
              .remove {
                color: #c30016;
              }
            }
          }
        }
      }

      &.funds-result {
        li {
          span {
            &.name {
              width: calc(100% - (200px + 90px));
            }

            &.document {
              width: 200px;
            }

            &.country {
              width: 200px;
            }
          }
        }
      }
    }
  }
}
