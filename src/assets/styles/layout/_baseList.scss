@use '../utils/variables';

.base {
  &-list {
    background-color: variables.$base-list-bg;
    display: inline-block;
    min-width: 100%;
    max-width: 3500px;
    padding-top: 0;
    border-radius: variables.$units-md;

    li {
      display: grid;
      justify-content: space-around;
      align-items: center;
      grid-column-gap: 20px;
      min-height: 47px;

      > span {
        display: inline-block;
        font-size: 14px;
        font-size: 0.875rem;
        padding: 0;
        width: auto;
        line-height: 46px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: relative;
        z-index: 1;

        a {
          &:hover {
            color: variables.$main-light-blue;
          }
        }

        &.name {
          width: auto;
          position: sticky;
          left: 52px;
          z-index: 1;
        }
      }

      .open-drill-dawn {
        background-color: variables.$base-list-bg;
        border-radius: 0 0 0 variables.$units-md;
        padding: 0 0 0 15px;
        width: 52px;
        position: sticky;
        left: 0;
        z-index: 1;

        span {
          display: inline-block;
          height: 17px;
          text-indent: -9999px;
          vertical-align: middle;
          width: 17px;
          cursor: pointer;

          &.open {
            background: url(/src/assets/png/open-detail-dark.png) no-repeat 0 0;
          }

          &.close {
            background: url(/src/assets/png/close-detail-dark.png) no-repeat 0 0;
          }
        }

        i {
          background: url(/src/assets/png/ico_aglutinar.png) no-repeat 0 0;
          cursor: pointer;
          display: inline-block;
          height: 18px;
          margin-left: -1px;
          opacity: 0.3;
          vertical-align: middle;
          width: 18px;
          visibility: visible;
        }

        &.sticky {
          left: 0;
        }

        &.pointerEvents {
          pointer-events: none;
          opacity: 0.5;

          .lds-dual-ring {
            display: inline-block;
            height: 17px;
            vertical-align: middle;
            width: 17px;

            div {
              top: 0;
            }
          }
        }
      }

      &.header-title {
        position: sticky;
        z-index: 2;
        top: 0;
        border-top: 0;

        span {
          font-weight: 700;
          color: variables.$main-light-blue;
        }

        .order-by {
          display: inline-block;
          border: 5px solid variables.$gray;
          height: 0;
          width: 0;
          margin: 0 0 0 4px;
          cursor: pointer;
        }

        &[class$='-asc'] {
          .order-by {
            border-color: variables.$main-light-blue transparent transparent;
            vertical-align: -3px;
          }
        }

        &[class$='-desc'] {
          .order-by {
            border-color: transparent transparent variables.$main-light-blue;
            vertical-align: 1px;
          }
        }

        &::before {
          content: '';
          position: absolute;
          background-color: variables.$page-header-bg;
          top: 0;
          pointer-events: none;
          z-index: -1;
          height: 100%;
          width: 100%;
        }

        &::after {
          bottom: -30px;
          content: '';
          height: 60px;
          pointer-events: none;
          position: absolute;
          left: 0;
          right: 0;
          z-index: -2;
        }
      }

      &:not(.header-title):hover {
        background: radial-gradient(
          52.96% 3704.62% at 50% 50%,
          rgba(35, 40, 56, 0.75) 59.9%,
          rgba(35, 40, 56, 0.376) 80.21%,
          rgba(35, 40, 56, 0) 100%
        );

        span {
          &.name,
          &.open-drill-dawn {
            background-color: variables.$sideMenu-bg;
          }
        }

        // .sub-item {
        //   &:hover {

        //     .name,
        //     & {
        //       background: variables.$base-list-bg;
        //     }
        //   }
        // }
      }

      &.list-loading {
        border-bottom: 0;
        grid-template-columns: 1fr;
        padding: 40px;

        &:hover {
          background: initial;
        }
      }

      &.no-results {
        padding: 20px 0;
        text-align: center;
        display: inline-block;
        width: 100%;
      }

      &:not(.sub-item) {
        &:not(:first-child) {
          border-top: 1px solid #344b69;
        }
      }
    }

    .sticky {
      position: sticky;
      z-index: 1;
    }

    .ReactCollapse {
      &--collapse {
        display: grid;
      }
    }

    .sub-item {
      border-top: 0;
      grid-template-columns: 1fr 139px 57px 150px 160px;

      border-bottom: 0px solid variables.$simpleListBorderColor;

      &:last-child {
        border-bottom: 0;
      }

      span {
        &.name {
          &.sticky {
            left: 0;
          }
        }
      }
    }
  }
}
