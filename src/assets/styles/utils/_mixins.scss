@use 'sass:math';

@mixin scroll($bgTrach, $width, $bgThumb, $bgThumbHover) {
  &::-webkit-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-moz-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-ms-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-o-scrollbar-track {
    background-color: $bgTrach;
  }

  &::-webkit-scrollbar {
    width: $width;
  }
  &::-moz-scrollbar {
    width: $width;
  }
  &::-ms-scrollbar {
    width: $width;
  }
  &::-o-scrollbar {
    width: $width;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-moz-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-ms-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-o-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }

  &::-webkit-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-moz-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-ms-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-o-scrollbar-thumb {
    background: $bgThumb;
  }
}

@mixin scroll-horizontal($bgTrach, $height, $bgThumb, $bgThumbHover) {
  &::-webkit-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-moz-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-ms-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-o-scrollbar-track {
    background-color: $bgTrach;
  }

  &::-webkit-scrollbar {
    height: $height;
  }
  &::-moz-scrollbar {
    height: $height;
  }
  &::-ms-scrollbar {
    height: $height;
  }
  &::-o-scrollbar {
    height: $height;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-moz-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-ms-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-o-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }

  &::-webkit-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-moz-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-ms-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-o-scrollbar-thumb {
    background: $bgThumb;
  }
}

@mixin radial-gradient-oval($from, $to, $percent) {
  background: -moz-radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background: -webkit-radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background: -o-radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background: -ms-radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background: radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background-color: $from;
}

@mixin input-placeholder($color) {
  &.placeholder {
    color: $color;
  }
  &:-moz-placeholder {
    color: $color;
  }
  &::-moz-placeholder {
    color: $color;
  }
  &:-ms-input-placeholder {
    color: $color;
  }
  &::-webkit-input-placeholder {
    color: $color;
  }
}

@mixin reset-form-style {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

@mixin font-size($sizeValue: 1.6) {
  font-size: $sizeValue + px;
  font-size: math.div($sizeValue, 16) + rem;
}
