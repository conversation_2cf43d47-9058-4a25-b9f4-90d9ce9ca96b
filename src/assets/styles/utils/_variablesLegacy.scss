$color-blue-01: #33a0ff;
$color-blue-02: #33a0ff;
$color-blue-03: #344b69;
$color-blue-04: #282d39;
$color-cerulean: #33a0ff;
$color-gray-01: #747474;
$color-gray-02: #656565;
$color-gray-03: #303030;
$color-gray-04: #3d3d3d;
$color-gray-05: #1b1f25;
$color-gray-06: #2e3137;
$color-gray-07: #434343;
$color-white: #fff;
$color-alto: #d2d2d2;
$color-green: #188917;
$color-orange: #ff9000;
$color-red: #c30016;

$font: 'Lato', sans-serif;

$font-size-xxl: 26px;
$font-size-xl: 22px;
$font-size-lg: 18px;
$font-size-md: 14px;
$font-size-sm: 13px;
$font-size-xs: 11px;

$font-weight-bold: 700;
$font-weight-regular: 400;
$font-weight-light: 300;

$letter-spacing-xl: 0.27px;
$letter-spacing-lg: 0.25px;
$letter-spacing-md: 0.23px;
$letter-spacing-sm: 0.22px;

$breakpoint-xs: 0;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

$gap-xxl: 35px;
$gap-xl: 30px;
$gap-lg: 25px;
$gap-md: 20px;
$gap-sm: 15px;
$gap-xs: 10px;

// phone
$screen-xs: 480px !default;
$screen-xs-min: $screen-xs !default;
$screen-phone: $screen-xs-min !default;

// tablet
$screen-sm: 768px !default;
$screen-sm-min: $screen-sm !default;
$screen-tablet: $screen-sm-min !default;

// desktop
$screen-md: 992px !default;
$screen-md-min: $screen-md !default;
$screen-desktop: $screen-md-min !default;

// wide desktop
$screen-lg: 1220px !default;
$screen-lg-min: $screen-lg !default;
$screen-lg-desktop: $screen-lg-min !default;

// wide desktop
$screen-xl: 1380px !default;
$screen-xl-min: $screen-xl !default;
$screen-xl-desktop: $screen-lg-min !default;

// So media queries don't overlap when required, provide a maximum
$screen-xs-max: ($screen-sm-min - 1) !default;
$screen-sm-max: ($screen-md-min - 1) !default;
$screen-md-max: ($screen-lg-min - 1) !default;

//== Grid system

//** Number of columns in the grid.
$grid-columns: 20 !default;
//** Padding between columns. Gets divided in half for the left and right.
$grid-gutter-width: 10px !default;
// Navbar collapse
//** Point at which the navbar becomes uncollapsed.
$grid-float-breakpoint: $screen-sm-min !default;
//** Point at which the navbar begins collapsing.
$grid-float-breakpoint-max: ($grid-float-breakpoint - 1) !default;

//== Container sizes

// Small screen / tablet
$container-tablet: (740px + $grid-gutter-width) !default;
//** For `$screen-sm-min` and up.
$container-sm: $container-tablet !default;

// Medium screen / desktop
$container-desktop: (970px + $grid-gutter-width) !default;
//** For `$screen-md-min` and up.
$container-md: $container-desktop !default;

// Large screen / wide desktop
$container-large-desktop: (1190px + $grid-gutter-width) !default;
//** For `$screen-lg-min` and up.
$container-lg: $container-large-desktop !default;
