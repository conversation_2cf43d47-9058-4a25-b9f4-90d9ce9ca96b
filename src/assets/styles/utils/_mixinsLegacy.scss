@use 'sass:math';

@use 'variablesLegacy';
@use 'variables';

//colors
$black: #333c40;
$white: #ffffff;
$lgray: #999999;
$mgray: #e0e0e0;
$gray: #cccccc;
$blue: #0076a3;
$text-intro-color: #2b393e;
$text-intro-blue: #0086a8;

// font size
@mixin font-size($sizeValue: 1.6) {
  font-size: $sizeValue + px;
  font-size: math.div($sizeValue, 16) + rem;
}

@mixin fontSize($size) {
  font-size: $size; //Fallback in px
  font-size: #{math.div($size, 16px)}rem;
}

@mixin scrollNew {
  &::-webkit-scrollbar {
    width: 4px;
    -webkit-appearance: none;
    &-thumb {
      background: variablesLegacy.$color-blue-02;
      &:hover {
        background: variablesLegacy.$color-blue-01;
      }
    }
    &-track {
      background-color: variablesLegacy.$color-gray-02;
    }
  }
}

@mixin scrollHorizontalNew {
  &::-webkit-scrollbar {
    height: 4px;
    -webkit-appearance: none;
    &-thumb {
      background: variablesLegacy.$color-blue-02;
      &:hover {
        background: variablesLegacy.$color-blue-01;
      }
    }
    &-track {
      background-color: variablesLegacy.$color-gray-02;
    }
  }
}

@mixin button {
  font-family: variablesLegacy.$font;
  font-weight: variablesLegacy.$font-weight-bold;
  letter-spacing: variablesLegacy.$letter-spacing-lg;
  color: variablesLegacy.$color-white;
  margin-top: 0;
  margin-bottom: 0;
  @include fontSize(variablesLegacy.$font-size-md);
}

// placeholder
@mixin input-placeholder {
  &.placeholder {
    @content;
  }
  &:-moz-placeholder {
    @content;
  }
  &::-moz-placeholder {
    @content;
  }
  &:-ms-input-placeholder {
    @content;
  }
  &::-webkit-input-placeholder {
    @content;
  }
}

@mixin scroll($bgTrach, $width, $bgThumb, $bgThumbHover) {
  &::-webkit-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-moz-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-ms-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-o-scrollbar-track {
    background-color: $bgTrach;
  }

  &::-webkit-scrollbar {
    width: $width;
  }
  &::-moz-scrollbar {
    width: $width;
  }
  &::-ms-scrollbar {
    width: $width;
  }
  &::-o-scrollbar {
    width: $width;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-moz-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-ms-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-o-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }

  &::-webkit-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-moz-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-ms-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-o-scrollbar-thumb {
    background: $bgThumb;
  }
}

@mixin scroll-horizontal($bgTrach, $height, $bgThumb, $bgThumbHover) {
  &::-webkit-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-moz-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-ms-scrollbar-track {
    background-color: $bgTrach;
  }
  &::-o-scrollbar-track {
    background-color: $bgTrach;
  }

  &::-webkit-scrollbar {
    height: $height;
  }
  &::-moz-scrollbar {
    height: $height;
  }
  &::-ms-scrollbar {
    height: $height;
  }
  &::-o-scrollbar {
    height: $height;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-moz-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-ms-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }
  &::-o-scrollbar-thumb:hover {
    background: $bgThumbHover;
  }

  &::-webkit-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-moz-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-ms-scrollbar-thumb {
    background: $bgThumb;
  }
  &::-o-scrollbar-thumb {
    background: $bgThumb;
  }
}

// transition
@mixin transition($transition...) {
  -moz-transition: $transition;
  -o-transition: $transition;
  -webkit-transition: $transition;
  -ms-transition: $transition;
  transition: $transition;
}

// generic transform
@mixin transform($transforms) {
  -moz-transform: $transforms;
  -o-transform: $transforms;
  -ms-transform: $transforms;
  -webkit-transform: $transforms;
  transform: $transforms;
}

// rotate
@mixin rotate($deg) {
  @include transform(rotate(#{$deg}deg));
}

// scale
@mixin scale($scale) {
  @include transform(scale($scale));
}

// translate
@mixin translate($x, $y) {
  @include transform(translate($x, $y));
}

// skew
@mixin skew($x, $y) {
  @include transform(skew(#{$x}deg, #{$y}deg));
}

// border-radius
@mixin border-radius($raio) {
  -webkit-border-radius: $raio;
  -moz-border-radius: $raio;
  -ms-border-radius: $raio;
  -o-border-radius: $raio;
  border-radius: $raio;
}
//transform origin
@mixin transform-origin($origin) {
  -moz-transform-origin: $origin;
  -o-transform-origin: $origin;
  -ms-transform-origin: $origin;
  -webkit-transform-origin: $origin;
  transform-origin: $origin;
}

// linear gradient
@mixin gradient($bg, $colors...) {
  background-color: $bg;
  background-image: -webkit-linear-gradient(bottom, $colors...);
  background-image: -moz-linear-gradient(bottom, $colors...);
  background-image: -ms-linear-gradient(bottom, $colors...);
  background-image: -o-linear-gradient(bottom, $colors...);
  background-image: linear-gradient(to top, $colors...);
}

// linear gradient
@mixin linear-gradient($direct, $bg, $colors...) {
  background-color: $bg;
  background-image: -webkit-linear-gradient($direct, $colors);
  background-image: -moz-linear-gradient($direct, $colors);
  background-image: -ms-linear-gradient($direct, $colors);
  background-image: -o-linear-gradient($direct, $colors);
  background-image: linear-gradient($direct, $colors);
}

//radial gradient
@mixin radial-gradient($from, $to) {
  background: -moz-radial-gradient(center, circle cover, $from 0%, $to 100%);
  background: -webkit-radial-gradient(center, circle cover, $from 0%, $to 100%);
  background: -o-radial-gradient(center, circle cover, $from 0%, $to 100%);
  background: -ms-radial-gradient(center, circle cover, $from 0%, $to 100%);
  background: radial-gradient(center, circle cover, $from 0%, $to 100%);
  background-color: $from;
}

//radial gradient
@mixin radial-gradient-oval($from, $to, $percent) {
  background: -moz-radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background: -webkit-radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background: -o-radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background: -ms-radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background: radial-gradient(ellipse at 50% 15px, $from 0%, $to $percent);
  background-color: $from;
}

// mixin box-shadow
@mixin box-shadow($top, $left, $blur, $color, $size: 0, $inset: false) {
  @if $inset {
    -webkit-box-shadow: inset $top $left $blur $size $color;
    -moz-box-shadow: inset $top $left $blur $size $color;
    box-shadow: inset $top $left $blur $size $color;
  } @else {
    -webkit-box-shadow: $top $left $blur $size $color;
    -moz-box-shadow: $top $left $blur $size $color;
    box-shadow: $top $left $blur $size $color;
  }
}

@mixin reset-form-style {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

// mixin para divisor
@mixin divisor-tab {
  @include gradient(rgba(0, 119, 181, 0), rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0));
  content: '';
  display: block;
  height: 27px;
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
}

// Box sizing
@mixin box-sizing($boxmodel) {
  -webkit-box-sizing: $boxmodel;
  -moz-box-sizing: $boxmodel;
  box-sizing: $boxmodel;
}

// Clearfix
@mixin clearfix() {
  &:before,
  &:after {
    content: ' '; // 1
    display: table; // 2
  }

  &:after {
    clear: both;
  }
}

// grid generation
@mixin make-grid-columns($i: 1, $list: '.cl-xs-#{$i}, .cl-sm-#{$i}, .cl-md-#{$i}, .cl-lg-#{$i}, .cl-xl-#{$i}') {
  @for $i from (1 + 1) through variablesLegacy.$grid-columns {
    $list: '#{$list}, .cl-xs-#{$i}, .cl-sm-#{$i}, .cl-md-#{$i}, .cl-lg-#{$i}, .cl-xl-#{$i}';
  }
  #{$list} {
    position: relative;
    // Prevent columns from collapsing when empty
    min-height: 1px;
    // Inner gutter via padding
    //padding-left:  ceil(($grid-gutter-width / 2));
    //padding-right: floor(($grid-gutter-width / 2));
  }
}

// [converter] This is defined recursively in LESS, but Sass supports real loops
@mixin float-grid-columns($class, $i: 1, $list: '.cl-#{$class}-#{$i}') {
  @for $i from (1 + 1) through variablesLegacy.$grid-columns {
    $list: '#{$list}, .cl-#{$class}-#{$i}';
  }
  #{$list} {
    float: left;
  }
}

@mixin calc-grid-column($index, $class, $type) {
  @if ($type == width) and ($index > 0) {
    .cl-#{$class}-#{$index} {
      width: math.percentage(math.div($index, variablesLegacy.$grid-columns));
    }
  }
  @if ($type == push) and ($index > 0) {
    .cl-#{$class}-push-#{$index} {
      left: math.percentage(math.div($index, variablesLegacy.$grid-columns));
    }
  }
  @if ($type == push) and ($index == 0) {
    .cl-#{$class}-push-0 {
      left: auto;
    }
  }
  @if ($type == pull) and ($index > 0) {
    .cl-#{$class}-pull-#{$index} {
      right: math.percentage(math.div($index, variablesLegacy.$grid-columns));
    }
  }
  @if ($type == pull) and ($index == 0) {
    .cl-#{$class}-pull-0 {
      right: auto;
    }
  }
  @if ($type == offset) {
    .cl-#{$class}-offset-#{$index} {
      margin-left: math.percentage(math.div($index, variablesLegacy.$grid-columns));
    }
  }
}

// [converter] This is defined recursively in LESS, but Sass supports real loops
@mixin loop-grid-columns($columns, $class, $type) {
  @for $i from 0 through $columns {
    @include calc-grid-column($i, $class, $type);
  }
}

// Create grid for specific class
@mixin make-grid($class) {
  @include float-grid-columns($class);
  @include loop-grid-columns(variablesLegacy.$grid-columns, $class, width);
  @include loop-grid-columns(variablesLegacy.$grid-columns, $class, pull);
  @include loop-grid-columns(variablesLegacy.$grid-columns, $class, push);
  @include loop-grid-columns(variablesLegacy.$grid-columns, $class, offset);
}

// Grid system
//
// Generate semantic grid columns with these mixins.

// Centered container element
@mixin container-fixed($gutter: variablesLegacy.$grid-gutter-width) {
  margin-right: auto;
  margin-left: auto;
  //padding-left:  floor(($gutter / 2));
  //padding-right: ceil(($gutter / 2));
  @include clearfix;
}

// Creates a wrapper for a series of columns
@mixin make-row($gutter: variablesLegacy.$grid-gutter-width) {
  //margin-left:  ceil(($gutter / -2));
  //margin-right: floor(($gutter / -2));
  @include clearfix;
}

// Generate the extra small columns
@mixin make-xs-column($columns, $gutter: variablesLegacy.$grid-gutter-width) {
  position: relative;
  float: left;
  width: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  min-height: 1px;
  //padding-left:  ($gutter / 2);
  //padding-right: ($gutter / 2);
}
@mixin make-xs-column-offset($columns) {
  margin-left: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
}
@mixin make-xs-column-push($columns) {
  left: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
}
@mixin make-xs-column-pull($columns) {
  right: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
}

// Generate the small columns
@mixin make-sm-column($columns, $gutter: variablesLegacy.$grid-gutter-width) {
  position: relative;
  min-height: 1px;
  //padding-left:  ($gutter / 2);
  //padding-right: ($gutter / 2);

  @media (min-width: variablesLegacy.$screen-sm-min) {
    float: left;
    width: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-sm-column-offset($columns) {
  @media (min-width: variablesLegacy.$screen-sm-min) {
    margin-left: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-sm-column-push($columns) {
  @media (min-width: variablesLegacy.$screen-sm-min) {
    left: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-sm-column-pull($columns) {
  @media (min-width: variablesLegacy.$screen-sm-min) {
    right: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}

// Generate the medium columns
@mixin make-md-column($columns, $gutter: variablesLegacy.$grid-gutter-width) {
  position: relative;
  min-height: 1px;
  //padding-left:  ($gutter / 2);
  //padding-right: ($gutter / 2);

  @media (min-width: variablesLegacy.$screen-md-min) {
    float: left;
    width: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-md-column-offset($columns) {
  @media (min-width: variablesLegacy.$screen-md-min) {
    margin-left: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-md-column-push($columns) {
  @media (min-width: variablesLegacy.$screen-md-min) {
    left: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-md-column-pull($columns) {
  @media (min-width: variablesLegacy.$screen-md-min) {
    right: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}

// Generate the large columns
@mixin make-lg-column($columns, $gutter: variablesLegacy.$grid-gutter-width) {
  position: relative;
  min-height: 1px;
  //padding-left:  ($gutter / 2);
  //padding-right: ($gutter / 2);

  @media (min-width: variablesLegacy.$screen-lg-min) {
    float: left;
    width: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-lg-column-offset($columns) {
  @media (min-width: variablesLegacy.$screen-lg-min) {
    margin-left: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-lg-column-push($columns) {
  @media (min-width: variablesLegacy.$screen-lg-min) {
    left: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}
@mixin make-lg-column-pull($columns) {
  @media (min-width: variablesLegacy.$screen-lg-min) {
    right: math.percentage(math.div($columns, variablesLegacy.$grid-columns));
  }
}

@mixin notes {
  font-family: variablesLegacy.$font;
  font-weight: variablesLegacy.$font-weight-regular;
  letter-spacing: variablesLegacy.$letter-spacing-md;
  color: variablesLegacy.$color-white;
  margin-top: 0;
  margin-bottom: 0;
  @include fontSize(variablesLegacy.$font-size-sm);
}

@mixin page-content {
  font-family: variablesLegacy.$font;
  font-weight: variablesLegacy.$font-weight-regular;
  letter-spacing: variablesLegacy.$letter-spacing-lg;
  color: variablesLegacy.$color-white;
  margin-top: 0;
  margin-bottom: 0;
  @include fontSize(variablesLegacy.$font-size-md);
}

@mixin input {
  font-family: variablesLegacy.$font;
  font-weight: variablesLegacy.$font-weight-light;
  letter-spacing: variablesLegacy.$letter-spacing-lg;
  color: variablesLegacy.$color-white;
  margin-top: 0;
  margin-bottom: 0;
  @include fontSize(variablesLegacy.$font-size-lg);
}
