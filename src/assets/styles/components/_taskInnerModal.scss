@use '../utils/variables';

.TaskInner {
  padding: 25px 35px;
  &__modal {
    background-color: rgba(black, 0.8);
    height: 100%;
    left: 0;
    overflow: auto;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
    .form-config {
      background: variables.$base-list-bg;
      left: 50%;
      max-width: 550px;
      max-height: 500px;
      padding: 40px;
      position: absolute;
      top: 50%;
      width: 100%;
      -moz-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -webkit-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      .close {
        cursor: pointer;
        border: 0;
        background: #fff url(@assets/png/close-white.png) no-repeat center;
        background-color: variables.$base-list-bg;
        height: 42px;
        position: absolute;
        right: 0;
        text-indent: -9999px;
        top: 0;
        width: 42px;
      }
      h3 {
        color: white;
        font-size: 20px;
        font-size: 1.25rem;
        line-height: 30px;
        margin-bottom: 20px;
      }
      .text,
      .select {
        display: block;
        margin-bottom: 20px;
        span {
          color: variables.$main-light-blue;
          display: block;
          font-weight: 700;
          margin-bottom: 8px;
          font-size: 14px;
          font-size: 0.875rem;
        }
        input,
        select {
          background-color: transparent;
          border: none;
          border-radius: 0;
          color: #fff;
          border-bottom: 1px solid variables.$simpleListBorderColor;
          font-family: 'Lato';
          height: 27px;
          width: 100%;
          padding: 0;
          font-size: 14px;
          font-size: 0.875rem;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
        }
      }
      .steps-actions-control {
        text-align: right;
        .bt {
          cursor: pointer;
          border: 0;
          color: #fff;
          display: inline-block;
          font-size: 0.8125rem;
          font-weight: 700;
          height: 35px;
          line-height: 35px;
          text-align: center;
          width: 127px;
          &.gray {
            background-color: variables.$gray;
          }
          &.blue {
            background-color: variables.$main-light-blue;
            overflow: hidden;
            position: relative;
            -moz-transition: all 0.4s;
            -o-transition: all 0.4s;
            -webkit-transition: all 0.4s;
            -ms-transition: all 0.4s;
            transition: all 0.4s;
            &:disabled {
              color: #fff;
              opacity: 0.5;
              pointer-events: none;
            }
          }
          &.left {
            float: left;
          }
        }
      }
    }
  }
}
