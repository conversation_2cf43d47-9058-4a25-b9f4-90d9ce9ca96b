@use '../utils/variables';

.lds-dual-ring {
  display: flex;
  align-items: center;
  height: 100%;
  margin: 0 auto;
  position: relative;
  width: 15px;
  top: 1px;
  div {
    position: absolute;
    width: 15px;
    height: 15px;
    // top: 15px;
    // left: 0;
    border-radius: 50%;
    border-color: variables.$main-light-blue transparent;
    border-style: solid;
    border-width: 2px;
    -webkit-animation: lds-dual-ring 0.5s linear infinite;
    animation: lds-dual-ring 0.5s linear infinite;
  }
  &.fix-top {
    div {
      top: 0;
    }
  }
}

// Loading
@-webkit-keyframes lds-dual-ring {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@-ms-keyframes lds-dual-ring {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@keyframes lds-dual-ring {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
