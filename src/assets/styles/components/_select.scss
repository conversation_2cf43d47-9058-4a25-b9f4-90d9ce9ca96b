@use '../utils/variables';

.new-option-dropdown,
.tearsheet-classifications-dropdown {
  margin-bottom: 32px;

  .select__control {
    min-height: initial;
    border: 0;
    border-bottom: 1px solid variables.$simpleListBorderColor;
    border-radius: 0;
    background-color: transparent;
    box-shadow: initial;

    .select__value-container {
      padding-left: 0;

      .select__single-value {
        color: white;
        font-size: 14px;
        line-height: 21px;

        &__label {
          color: white;
          padding: 5px 0 5px 8px;
          font-weight: 400;
          font-size: 14px;
          line-height: 21px;
          display: flex;
          align-items: center;
          letter-spacing: 0.01em;
        }

        &__remove {
          cursor: pointer;

          &:hover {
            background-color: transparent;
            color: white;
          }
        }
      }
    }

    .select__input-container {
      height: 16px;
      color: #ffffff;
      font-size: 14px;
      line-height: 21px;
      font-weight: 400;

      .select__input {
        height: auto;
      }
    }

    .select__indicators {
      .select__indicator-separator {
        width: 0;
      }

      .select__indicator {
        padding: 0;

        svg {
          width: 15px;
        }

        &.select__clear-indicator {
          display: none;
        }
      }
    }

    &:hover,
    &:focus {
      border-bottom-color: variables.$main-light-blue;
    }
  }

  .select__menu {
    background-color: variables.$sideMenu-bg;
    border-radius: initial;
    box-shadow: inset 0 14px 20px -2px rgb(0 0 0 / 30%);
    border-radius: 0px 0px 5px 5px;

    &-list {
      padding: 0;
      border-radius: 0px 0px 5px 5px;

      .select__option {
        cursor: pointer;
        font-weight: 400;
        font-size: 14px;
        line-height: 21px;
        display: flex;
        padding: 0px 18px 0px 10px;
        min-height: 25px;
        align-items: center;
        letter-spacing: 0.01em;
        color: white;

        &:hover {
          background-color: variables.$modal-lines;
          color: white;
        }

        &--is-focused {
          background-color: variables.$sideMenu-bg;
          color: white;
        }

        &--is-selected {
          background-color: variables.$main-light-blue;
          color: white;

          &:hover {
            background-color: variables.$main-light-blue;
          }
        }
      }

      .select__menu-notice {
        span {
          display: none;
        }

        &--no-options {
          padding: 0;

          div[role='button'] {
            padding: 4px 6px;

            div {
              width: calc(100% - 15px);
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              text-align: left;
            }
          }
        }
      }

      /* Scrollbar */
      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-track {
        background: variables.$base-list-bg;
      }

      &::-webkit-scrollbar-thumb {
        background: variables.$main-light-blue;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: variables.$main-light-blue;
      }
    }
  }
}

.tearsheet-classifications-dropdown {
  width: 120px;
  margin-bottom: 0;
}
