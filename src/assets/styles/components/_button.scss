@use '../utils/_variablesLegacy';
@use '../utils/variables';
@use '../utils/_mixinsLegacy';

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  min-width: 120px;
  height: 26px;
  padding-left: 20px;
  padding-right: 20px;
  cursor: pointer;
  @include mixinsLegacy.button;
  &__primary {
    background-color: rgba(variables.$main-light-blue, 0.7);
    border: 0;
    transition: background-color 300ms ease-in-out;
    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:hover:not(:disabled) {
      background-color: rgba(variables.$main-light-blue, 0.8);
      transition: background-color 300ms ease-in-out;
    }
    &.left {
      padding: 0;
    }

    .lds-dual-ring {
      div {
        top: inherit;
        border-color: #fff transparent;
      }
    }
  }
  &__success {
    background-color: rgba(#4da178, 0.7);
    border: 0;
    transition: background-color 300ms ease-in-out;
    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:hover:not(:disabled) {
      background-color: rgba(#4da178, 0.8);
      transition: background-color 300ms ease-in-out;
    }
    &.left {
      padding: 0;
    }
  }
  &__primary-outline {
    color: variables.$main-light-blue;
    background-color: rgba(white, 0);
    border: 1px solid variables.$main-light-blue;
    transition: background-color 300ms ease-in-out;
    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:hover:not(:disabled) {
      background-color: rgba(white, 0.1);
      transition: background-color 300ms ease-in-out;
    }
  }
  &__secondary {
    background-color: rgba(variablesLegacy.$color-gray-01, 0.7);
    border: 0;
    transition: background-color 300ms ease-in-out;
    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:hover:not(:disabled) {
      background-color: rgba(variablesLegacy.$color-gray-01, 0.8);
      transition: background-color 300ms ease-in-out;
    }
  }
  &__secondary-outline {
    background-color: rgba(white, 0);
    border: 1px solid white;
    transition: background-color 300ms ease-in-out;
    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:hover:not(:disabled) {
      background-color: rgba(white, 0.1);
      transition: background-color 300ms ease-in-out;
    }
  }
  &__type {
    color: variables.$main-light-blue;
    background-color: rgba(white, 0);
    border: 0;
    transition: background-color 300ms ease-in-out;
    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:hover:not(:disabled) {
      background-color: rgba(white, 0.1);
      transition: background-color 300ms ease-in-out;
    }
  }
  &__secondary-type {
    color: white;
    background-color: rgba(white, 0);
    border: 0;
    transition: background-color 300ms ease-in-out;
    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    &:hover:not(:disabled) {
      background-color: rgba(white, 0.1);
      transition: background-color 300ms ease-in-out;
    }
  }
  &__export {
    background-color: #4da178;
    border: 0;
    transition: 0.25s;
    &:hover,
    &:focus {
      box-shadow:
        inset -5em 0 0 0 variables.$main-light-blue,
        inset 5em 0 0 0 variables.$main-light-blue;
    }
    &:disabled {
      opacity: 0.6;
      pointer-events: none;
      cursor: not-allowed;
    }
  }
}
