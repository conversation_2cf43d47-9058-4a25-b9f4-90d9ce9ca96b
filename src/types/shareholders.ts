export const SHAREHOLDER_TYPES = {
  FUND: 1,
  INDIVIDUAL: 2,
  UNKNOW: 50,
} as const

export const DOCUMENT_TYPES = {
  INDIVIDUAL: 1,
  FUND: 2,
  UNKNOW: 99,
} as const

export const REPORT_STATUS = {
  NEW: 1,
  PREPROCESSING: 2,
  PROCESSING: 3,
  SUCCESS: 4,
  PA<PERSON>IALSUCCESS: 5,
  ERROR: 6,
  CANCELED: 7,
} as const

export type ShareholderTypes = typeof SHAREHOLDER_TYPES
export type DocumentTypes = typeof DOCUMENT_TYPES
export type ReportStatus = typeof REPORT_STATUS

export type FundShareholder = typeof SHAREHOLDER_TYPES.FUND
export type IndividualShareholder = typeof SHAREHOLDER_TYPES.INDIVIDUAL
export type UnknowShareholder = typeof SHAREHOLDER_TYPES.UNKNOW

export type IndividualShareholderDocument = typeof DOCUMENT_TYPES.INDIVIDUAL
export type FundShareholderDocument = typeof DOCUMENT_TYPES.FUND
export type UnknowShareholderDocument = typeof DOCUMENT_TYPES.UNKNOW

export type ShareholderType = FundShareholder | IndividualShareholder | UnknowShareholder
export type DocumentType = IndividualShareholderDocument | FundShareholderDocument | UnknowShareholderDocument
