import { i18n } from 'translate'

export const translations = {
  tickerLabel: i18n.t('globals.headerContent.stockType'),
  dateLabel: i18n.t('globals.headerContent.date'),
  deleteMonitoredListModalTitle: i18n.t('monitoring.interestGroup.deleteList.title'),
  deleteMonitoredListModalMessage: i18n.t('monitoring.interestGroup.deleteList.message'),
  deleteMonitoredListSuccessTitle: i18n.t('monitoring.interestGroup.deleteList.success.title'),
  deleteMonitoredListSuccessMessage: i18n.t('monitoring.interestGroup.deleteList.success.message'),
  deletedMonitoredListGotoHistory: i18n.t('monitoring.interestGroup.deleteList.success.goToExportHistory'),
  loadButtonLabel: i18n.t('monitoring.load'),
  pageContentPlaceholder: i18n.t('monitoring.interestGroup.pageContentPlaceholder'),
  tableTitleName: i18n.t('monitoring.interestGroup.table.name'),
  tableTitleDocument: i18n.t('monitoring.interestGroup.table.document'),
  tableTitleSharesChange: i18n.t('monitoring.interestGroup.table.sharesChange'),
  tableTitleFinalVolume: i18n.t('monitoring.interestGroup.table.finalVolume'),
  tableNoNegotiation: i18n.t('monitoring.interestGroup.table.noNegotiation'),
  monitoredExportListMessage: i18n.t('monitoring.monitoredExportListMessage'),
  success: i18n.t('monitoring.success'),
  exportDailyPosition: i18n.t('monitoring.exportDailyPosition'),
  exportListMessage: i18n.t('monitoring.exportListMessage'),
  goToHistory: i18n.t('globals.goToHistory'),
  uploadModalTitle: i18n.t('components.uploadModal.monitoredShareholders.title'),
  uploadModalInfoText: i18n.t('components.uploadModal.monitoredShareholders.infoText'),
  emailMonitoredAlert: i18n.t('monitoring.interestGroup.emailMonitoredAlert'),

  uploadButtonLabel: i18n.t('monitoring.interestGroup.upload.buttonLabel'),
  uploadSuccessTitle: i18n.t('monitoring.interestGroup.upload.success.title'),
  uploadSuccessMessage: i18n.t('monitoring.interestGroup.upload.success.message'),

  requestFailTitle: i18n.t('globals.errors.requestFail.title'),
  requestFailMessage: i18n.t('globals.errors.requestFail.message'),
  cancelButtonLabel: i18n.t('components.deleteModal.cancelButton'),
  deleteButtonLabel: i18n.t('components.deleteModal.deleteButton'),
}
