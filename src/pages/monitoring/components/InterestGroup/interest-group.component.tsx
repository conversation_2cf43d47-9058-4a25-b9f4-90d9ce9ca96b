import { useCallback, useEffect, useState } from 'react'
import { i18n } from 'translate'
import { TOption, TActionsOptions, useTours, useToast } from '@mz-codes/design-system'
import { getCompany, getCustomerId } from 'globals/storages/locals'
import { getTickers } from 'globals/services/tickers'

import { GoToHistoryButton } from 'components'
import {
  getSerializedInterestGroupShareholdersList,
  InterestGroupList,
  getAvailableDates,
  postDeleteMonitoredList,
  postInterestGroupExportList,
  postInterestGroupExportScreen,
  postUploadMonitoredShareholders,
} from 'hooks'

import { dateFromUTC, formatDateToString } from 'utils'
import { BaseError } from 'errors'
import { AxiosProgressEvent } from 'axios'
import { InterestGroupRender } from './interest-group.template'
import { translations } from './interest-group.translations'
import { tours } from './interest-group.tours'

const idiom = i18n.language.startsWith('pt') ? 1 : 0
const acceptedFiles = '.xls, .xlsx'

export function InterestGroup(): React.ReactElement {
  const company = getCompany()
  const companyId = company.id
  const companyName = company.displayName
  const customerId = getCustomerId()
  const today = new Date()
  const { createToast } = useToast()
  const [loading, setLoading] = useState(false)
  const [tickers, setTickers] = useState<TOption[]>([])
  const [selectedTicker, setSelectedTicker] = useState<TOption>({} as TOption)
  const [referenceDate, setReferenceDate] = useState<Date>(today)
  const [availableDates, setAvailableDates] = useState<Date[]>([])
  const [disableLoadButton] = useState(false)
  const [monitoredShareholdersList, setMonitoredShareholdersList] = useState<InterestGroupList['report']>([])

  const [showUploadModal, setShowUploadModal] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File>()
  const [uploadProgress, setUploadProgress] = useState<number>(0)

  const [showDeleteModal, setShowDeleteModal] = useState(false)

  const disableExportButton = !monitoredShareholdersList.length
  const disableHeaderOptions = !tickers || !availableDates?.length || loading

  const requestFailToast = useCallback(() => {
    createToast({
      title: translations.requestFailTitle,
      description: translations.requestFailMessage,
      duration: 20000,
      type: 'alert',
    })
  }, [createToast])

  const handleGetTickers = useCallback(async () => {
    const response = await getTickers(companyId)

    const options = response.map<TOption>((ticker) => ({
      label: ticker.label,
      value: ticker.tickerId,
    }))

    setTickers(options)
    setSelectedTicker(options[0])
  }, [companyId])

  useEffect(() => {
    handleGetTickers()
  }, [handleGetTickers])

  useEffect(() => {
    if (!selectedTicker.value) return

    getAvailableDates({
      companyId,
      tickerId: selectedTicker.value as string,
    }).then((data) => {
      const parsedDates: Date[] = data
        .sort((a: string, b: string) => (a > b ? 1 : -1))
        .map((date: string) => dateFromUTC(date))

      const lastAvailableDate = parsedDates[parsedDates.length - 1]

      setAvailableDates(parsedDates)

      if (!lastAvailableDate) return

      setReferenceDate(lastAvailableDate)
      setLoading(false)
    })
  }, [companyId, selectedTicker])

  const handleLoadMonitoredShareholdersList = async () => {
    setLoading(true)
    try {
      const data = await getSerializedInterestGroupShareholdersList({
        customerId,
        tickerId: selectedTicker.value as string,
        referenceDate: formatDateToString(referenceDate),
      })
      setMonitoredShareholdersList(data.report)
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 10000,
          type: 'alert',
        })
        return
      }
      requestFailToast()
    } finally {
      setLoading(false)
    }
  }

  const handleExportShareholdersList = async () => {
    try {
      await postInterestGroupExportList({
        customerId,
        tickerId: selectedTicker.value as string,
        referenceDate: formatDateToString(referenceDate),
        idiom,
      })

      createToast({
        title: translations.success as string,
        description: translations.exportListMessage as string,
        duration: 9000,
        type: 'success',
        buttons: <GoToHistoryButton />,
      })
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
      requestFailToast()
    }
  }
  const handleExportBase = async () => {
    try {
      await postInterestGroupExportScreen({
        customerId,
        tickerId: selectedTicker.value as string,
        referenceDate: formatDateToString(referenceDate),
        idiom,
      })

      createToast({
        title: translations.success as string,
        description: translations.monitoredExportListMessage as string,
        duration: 9000,
        type: 'success',
        buttons: <GoToHistoryButton />,
      })
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
      requestFailToast()
    }
  }

  const handleDeleteModal = () => {
    setShowDeleteModal(!showDeleteModal)
  }

  const handleDeleteMonitoredList = async () => {
    try {
      await postDeleteMonitoredList({
        companyName,
        companyId,
        customerId,
        tickerId: selectedTicker.value as string,
        referenceDate: formatDateToString(referenceDate),
        language: idiom,
      })

      createToast({
        title: translations.deleteMonitoredListSuccessTitle as string,
        description: translations.deleteMonitoredListSuccessMessage as string,
        duration: 9000,
        type: 'success',
        buttons: <GoToHistoryButton />,
      })

      handleDeleteModal()
      setMonitoredShareholdersList([])
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
      requestFailToast()
    }
  }

  const handleLoadClick = () => {
    setShowUploadModal(true)
  }

  const handleCloseUploadModal = () => {
    setShowUploadModal(false)
    setSelectedFile(undefined)
  }

  const handleSelectedFile = (file: File) => {
    if (file === selectedFile) return
    setSelectedFile(file)
  }

  const handleProgressFileUpload = (progressEvent: AxiosProgressEvent) => {
    const { loaded, total = 0 } = progressEvent

    const progress = total > 0 ? Math.round((loaded * 100) / total) : 0
    setUploadProgress(progress)
  }

  const handleUploadFile = async () => {
    try {
      if (!selectedFile) return
      await postUploadMonitoredShareholders({
        file: selectedFile,
        customerId,
        idiom,
        onUploadProgress: handleProgressFileUpload,
      })

      createToast({
        type: 'success',
        title: translations.uploadSuccessTitle as string,
        description: translations.uploadSuccessMessage as string,
        duration: 10000,
      })
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          type: 'error',
          title: err.title,
          description: err.message,
        })
        return
      }
      requestFailToast()
    } finally {
      setUploadProgress(0)
      setShowUploadModal(false)
      setSelectedFile(undefined)
    }
  }

  const actionsDropdownOptions: TActionsOptions[] = [
    {
      name: i18n.t('components.actionsDropdownOptions.import'),
      key: 0,
      action: handleLoadClick,
      type: 'update',
    },
    {
      name: i18n.t('components.actionsDropdownOptions.exportScreen'),
      key: 1,
      action: handleExportBase,
      type: 'export',
      disabled: disableExportButton,
    },
    {
      name: i18n.t('components.actionsDropdownOptions.exportList'),
      key: 2,
      action: handleExportShareholdersList,
      type: 'export',
    },
    {
      name: i18n.t('components.actionsDropdownOptions.deleteList'),
      key: 3,
      action: handleDeleteModal,
      type: 'delete',
    },
  ]

  const handleTickerValue = (option: TOption) => {
    if (option === selectedTicker) return
    setSelectedTicker(option)
    setMonitoredShareholdersList([])
  }

  const handleReferenceDate = (date: Date) => {
    if (date === referenceDate) return
    setReferenceDate(date)
    setMonitoredShareholdersList([])
  }

  const { run, setPageTours } = useTours()

  useEffect(() => {
    setPageTours(tours)
  }, [setPageTours])

  useEffect(() => {
    run()
  }, [run])

  return (
    <InterestGroupRender
      translations={translations}
      disableHeaderOptions={disableHeaderOptions}
      tickers={tickers}
      selectedTicker={selectedTicker}
      handleTickerValue={handleTickerValue}
      referenceDate={referenceDate}
      availableDates={availableDates}
      handleReferenceDate={handleReferenceDate}
      handleLoadMonitoredShareholdersList={handleLoadMonitoredShareholdersList}
      handleDisableLoadButton={disableLoadButton}
      actionsDropdownOptions={actionsDropdownOptions}
      handleLoading={loading}
      monitoredShareholdersList={monitoredShareholdersList}
      uploadModalAcceptedFiles={acceptedFiles}
      showUploadModal={showUploadModal}
      handleCloseUploadModal={handleCloseUploadModal}
      selectedFile={selectedFile}
      onSelectedFile={handleSelectedFile}
      handleUploadFile={handleUploadFile}
      uploadProgress={uploadProgress}
      showDeleteModal={showDeleteModal}
      handleDeleteModal={handleDeleteModal}
      handleDeleteMonitoredList={handleDeleteMonitoredList}
    />
  )
}
