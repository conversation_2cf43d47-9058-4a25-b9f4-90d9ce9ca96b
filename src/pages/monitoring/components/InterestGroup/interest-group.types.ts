import { TOption, TActionsOptions } from '@mz-codes/design-system'
import { InterestGroupList } from 'hooks'
import { translations } from './interest-group.translations'

type TTranslations = typeof translations

export type InterestGroupRenderProps = {
  translations: TTranslations
  disableHeaderOptions: boolean

  tickers: TOption[]
  selectedTicker: TOption
  handleTickerValue(option: TOption): void
  referenceDate: Date
  availableDates: Date[]
  handleReferenceDate(date: Date | null): void
  handleLoadMonitoredShareholdersList(): Promise<void>
  handleDisableLoadButton: boolean
  actionsDropdownOptions: TActionsOptions[]

  handleLoading: boolean
  monitoredShareholdersList?: InterestGroupList['report']

  uploadModalAcceptedFiles: string
  showUploadModal: boolean
  selectedFile?: File
  onSelectedFile(file: File): void
  uploadProgress?: number
  handleUploadFile(): void
  handleCloseUploadModal(): void

  showDeleteModal: boolean
  handleDeleteModal(): void
  handleDeleteMonitoredList(): void
}
