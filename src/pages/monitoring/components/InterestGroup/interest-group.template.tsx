import { i18n } from 'translate'

import {
  NewDatepicker,
  theme,
  ActionsDropdown,
  Dropdown,
  Loading,
  Buttons,
  DeleteModal,
  <PERSON><PERSON><PERSON>,
  Header,
} from '@mz-codes/design-system'
import { PageContent, Table, UploadModal, Page, ExternalLink } from 'components'
import { generateUniqueId } from 'utils'
import { IMAGES } from 'assets'
import { EXTERNAL_PRODUCT } from 'consts/external-products'

import { PageContentPlaceholder } from '../InterestGroupTable/styled'
import { Content as InterestGroupContent } from '../DailyPositionContent'
import { InterestGroupTable } from '../InterestGroupTable'
import { InterestGroupRenderProps } from './interest-group.types'

export function InterestGroupRender(props: InterestGroupRenderProps) {
  const {
    translations,
    disableHeaderOptions,
    tickers,
    selectedTicker,
    handleTickerValue,
    referenceDate,
    availableDates,
    handleReferenceDate,
    handleLoadMonitoredShareholdersList,
    handleDisableLoadButton,
    actionsDropdownOptions,
    handleLoading,
    monitoredShareholdersList,
    uploadModalAcceptedFiles,
    showUploadModal,
    selectedFile,
    onSelectedFile,
    uploadProgress,
    handleUploadFile,
    handleCloseUploadModal,
    showDeleteModal,
    handleDeleteModal,
    handleDeleteMonitoredList,
  } = props

  const getDeltaColor = (deltaValue: number) => {
    if (deltaValue < 0) return theme.colors.semantic.red[500]
    return theme.colors.semantic.green[500]
  }

  return (
    <Page>
      <Header>
        <Header.Content>
          <Header.Item>
            <Header.Label>{translations.tickerLabel as string}</Header.Label>
            <Dropdown
              disabled={disableHeaderOptions}
              options={tickers}
              selected={selectedTicker}
              onChange={handleTickerValue}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.dateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              blocked={disableHeaderOptions}
              selected={referenceDate}
              onChange={handleReferenceDate}
              availableDates={availableDates}
              hint={i18n.t('globals.referenceDate')}
            />
          </Header.Item>
          <Header.Item $alignRight>
            <Tooltip
              $wrapperStyle={{
                margin: 'auto 35px auto auto',
              }}
              $tooltipStyle={{ width: '300px' }}
              text={translations.emailMonitoredAlert as string}
            >
              <ExternalLink
                id="shareholders-interest-group-alert-link"
                product={EXTERNAL_PRODUCT.SETTINGS}
                pagePath="/responsible"
              >
                <img src={IMAGES.EMAIL_ALERT} alt="" />
              </ExternalLink>
            </Tooltip>
          </Header.Item>
        </Header.Content>
        <Header.ButtonGroup style={{ marginLeft: 'initial' }}>
          <Buttons.Primary
            disabled={handleDisableLoadButton || disableHeaderOptions}
            onClick={handleLoadMonitoredShareholdersList}
          >
            {translations.loadButtonLabel as string}
          </Buttons.Primary>
          <ActionsDropdown
            title={i18n.t('components.actionsDropdownOptions.actions')}
            disabled={disableHeaderOptions}
            options={actionsDropdownOptions}
          />
        </Header.ButtonGroup>
      </Header>
      <PageContent style={{ paddingBottom: '0' }}>
        {handleLoading && <Loading />}
        {monitoredShareholdersList?.length === 0 && !handleLoading && (
          <PageContentPlaceholder>{translations.pageContentPlaceholder as string}</PageContentPlaceholder>
        )}
        {monitoredShareholdersList?.length !== 0 && (
          <InterestGroupContent>
            {monitoredShareholdersList?.map((group) => {
              const key = generateUniqueId()
              return (
                <InterestGroupTable key={key} title={group.listName}>
                  <Table.Container>
                    <Table.THead style={{ position: 'sticky' }}>
                      <Table.TR>
                        <Table.TH style={{ width: '45%' }}>{translations.tableTitleName as string}</Table.TH>
                        <Table.TH>{translations.tableTitleDocument as string} </Table.TH>
                        <Table.TH style={{ textAlign: 'right' }}>
                          {translations.tableTitleSharesChange as string}
                        </Table.TH>
                        <Table.TH style={{ textAlign: 'right' }}>
                          {translations.tableTitleFinalVolume as string}
                        </Table.TH>
                      </Table.TR>
                    </Table.THead>
                    <Table.TBody style={{ height: 'auto' }}>
                      {group.listData.length === 0 && (
                        <Table.TR style={{ borderBottom: '0' }}>
                          <Table.TD
                            colSpan={4}
                            style={{
                              textAlign: 'center',
                              padding: '33.5px 20px',
                            }}
                          >
                            {`${translations.tableNoNegotiation} ${group.listName}`}
                          </Table.TD>
                        </Table.TR>
                      )}

                      {group.listData &&
                        group.listData.map((shareholder) => {
                          const { delta, document, finalPosition, name } = shareholder
                          const keyId = generateUniqueId()
                          return (
                            <Table.TR key={keyId}>
                              <Table.TD style={{ width: '45%' }}>{name}</Table.TD>
                              <Table.TD>{document}</Table.TD>
                              <Table.TD
                                style={{
                                  textAlign: 'right',
                                  color: getDeltaColor(parseFloat(delta)),
                                }}
                              >
                                {delta}
                              </Table.TD>
                              <Table.TD style={{ textAlign: 'right' }}>{finalPosition}</Table.TD>
                            </Table.TR>
                          )
                        })}
                    </Table.TBody>
                  </Table.Container>
                </InterestGroupTable>
              )
            })}
          </InterestGroupContent>
        )}
      </PageContent>

      <UploadModal
        show={showUploadModal}
        onConfirm={handleUploadFile}
        onClose={handleCloseUploadModal}
        selectedFile={selectedFile}
        onSelectedFile={onSelectedFile}
        title={translations.uploadModalTitle as string}
        message={translations.uploadModalInfoText as string}
        progress={uploadProgress}
        acceptedFiles={uploadModalAcceptedFiles}
        confirmButtonLabel={translations.uploadModalTitle as string}
      />

      <DeleteModal
        show={showDeleteModal}
        onClose={handleDeleteModal}
        onDelete={handleDeleteMonitoredList}
        message={translations.deleteMonitoredListModalMessage}
        cancelButtonLabel={translations.cancelButtonLabel}
        deleteButtonLabel={translations.deleteButtonLabel}
      />
    </Page>
  )
}
