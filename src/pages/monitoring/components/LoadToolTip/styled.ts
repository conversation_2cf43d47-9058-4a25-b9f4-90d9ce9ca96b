import styled from 'styled-components'

type TooltipProps = {
  hasHighlightToolTip: boolean
}

export const Wrapper = styled.div`
  position: absolute;
  right: 0;
  top: 0;
  width: 135px;
  height: 25px;
  z-index: 3;
`

export const Tooltip = styled.div<TooltipProps>`
  position: absolute;
  background: #1b1f25;
  left: 21.5%;
  visibility: hidden;
  visibility: ${({ hasHighlightToolTip }) => (hasHighlightToolTip ? 'visible' : 'hidden')};
  z-index: 2;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  line-height: 1em;
  font-size: 14px;
  top: 240%;
  width: 260px;
  padding-left: 15px;
  font-weight: 700;
  color: white;

  &::before {
    content: '';
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0px 5px 5px 5px;
    border-color: transparent transparent #1b1f25 transparent;
    left: 50%;
    position: absolute;
    transform: translateX(-50%);
    bottom: 100%;
  }

  ${Wrapper}:hover & {
    visibility: initial;
  }
`
