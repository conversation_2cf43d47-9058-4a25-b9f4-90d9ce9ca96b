import {
  Buttons,
  Loading,
  Dropdown,
  Header,
  Icons,
  Tooltip,
  ALIGNMENTS,
  NewDatepicker,
  Table,
} from '@mz-codes/design-system'

import { PageContent, MonitoredShareholdersSearchModal, Page, DataNotFound } from 'components'
import { generateUniqueId } from 'utils'

import { i18n } from 'translate'
import { MonitoredLabel } from '../MonitoredLabel'
import * as Steps from '../Steps'
import * as DailyPosition from '../DailyPositionContent'

import { translations } from './daily-position.translations'
import { TDailyPositionTemplateProps } from './daily-position.types'

export function DailyPositionTemplate(props: TDailyPositionTemplateProps): JSX.Element {
  const {
    disableHeaderOptions,
    tickers,
    selectedTicker,
    onTickerChangeValue,
    onStartDateChange,
    selectedStartDate,
    onEndDateChange,
    handleStartDateAvailableDatesLimit,
    handleEndDateAvailableDatesLimit,
    selectedEndDate,
    classificationList,
    selectedClassification,
    onClassificationChangeValue,
    handleLoadDailyPositionList,
    disableLoadButton,
    onExportClick,
    isLoading,
    searchShareholdersModalVisibility,
    handleOpenMonitoredShareholdersSearchModal,
    handleCloseMonitoredShareholdersSearchModal,
    selectedMonitoredShareholder,
    setSelectedMonitoredShareholder,
    isExportDisabled,
    dailyPositionList,
    startTour,
  } = props

  return (
    <Page>
      <Header>
        <Header.Content id="daily-position-header-content">
          <Header.Item>
            <Header.Label>{translations.tickerLabel as string}</Header.Label>
            <Dropdown
              disabled={disableHeaderOptions}
              options={tickers}
              selected={selectedTicker}
              onChange={onTickerChangeValue}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.startDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              id="daily-position-date-range-picker-start"
              blocked={disableHeaderOptions}
              selected={selectedStartDate}
              onChange={onStartDateChange}
              maxDate={handleStartDateAvailableDatesLimit()?.[handleStartDateAvailableDatesLimit().length - 1]}
              minDate={handleStartDateAvailableDatesLimit()?.[0]}
              hint={i18n.t('startDate')}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.endDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              id="daily-position-date-range-picker-end"
              blocked={disableHeaderOptions}
              selected={selectedEndDate}
              onChange={onEndDateChange}
              maxDate={handleEndDateAvailableDatesLimit()?.[handleEndDateAvailableDatesLimit().length - 1]}
              minDate={handleEndDateAvailableDatesLimit()?.[0]}
              hint={i18n.t('endDate')}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.classificationLabel as string}</Header.Label>
            <Dropdown
              disabled={disableHeaderOptions}
              options={classificationList}
              selected={selectedClassification}
              onChange={onClassificationChangeValue}
            />
          </Header.Item>
          <Header.Item id="daily-position-monitored-shareholder">
            <Header.Label tooltip={translations.selectedShareholderTooltip as string}>
              {translations.monitoredLabel as string}
            </Header.Label>
            <MonitoredLabel
              disabled={disableHeaderOptions}
              text={selectedMonitoredShareholder.name}
              onClick={handleOpenMonitoredShareholdersSearchModal}
            />
          </Header.Item>
          <Header.Item $alignRight>
            <Tooltip $wrapperStyle={{ cursor: 'help' }} text={translations.help as string}>
              <Icons.HelpCircle size={20} onClick={() => startTour('daily-position-introduction')} />
            </Tooltip>
          </Header.Item>
        </Header.Content>
        <Header.ButtonGroup>
          {disableLoadButton ? (
            <Tooltip
              text={translations.loadTooltipText as string}
              $alignment={ALIGNMENTS.BOTTOM_LEFT}
              $wrapperStyle={{ zIndex: 3 }}
            >
              <Buttons.Primary
                id="daily-position-confirm-button"
                $size="100%"
                disabled={disableLoadButton}
                onClick={handleLoadDailyPositionList}
              >
                {translations.loadLabel as string}
              </Buttons.Primary>
            </Tooltip>
          ) : (
            <Buttons.Primary
              id="daily-position-confirm-button"
              $size="100%"
              disabled={disableLoadButton}
              onClick={handleLoadDailyPositionList}
            >
              {translations.loadLabel}
            </Buttons.Primary>
          )}
          <Buttons.Export onClick={onExportClick} disabled={isExportDisabled}>
            {translations.exportLabel as string}
          </Buttons.Export>
        </Header.ButtonGroup>
      </Header>

      <PageContent>
        {isLoading ? (
          <Loading />
        ) : !dailyPositionList ? (
          <Steps.Content>
            <Steps.Item id="daily-position-step-1" onClick={() => startTour('daily-position-export')}>
              <Steps.Title>1</Steps.Title>
              <Steps.Legend>{translations.stepOneLabel as string}</Steps.Legend>
            </Steps.Item>
            <Steps.Item id="daily-position-step-2">
              <Steps.Title>2</Steps.Title>
              <Steps.Legend>{translations.stepTwoLabel as string}</Steps.Legend>
            </Steps.Item>
            <Steps.Item id="daily-position-step-3">
              <Steps.Title>3</Steps.Title>
              <Steps.Legend>{translations.stepThreeLabel as string}</Steps.Legend>
            </Steps.Item>
          </Steps.Content>
        ) : (
          <DailyPosition.Content>
            <DailyPosition.Header>
              <DailyPosition.Title>
                <img src={selectedMonitoredShareholder.shareholderImage} alt={selectedMonitoredShareholder.name} />
                {selectedMonitoredShareholder.name}
              </DailyPosition.Title>
              <DailyPosition.Title>{selectedMonitoredShareholder.document}</DailyPosition.Title>
            </DailyPosition.Header>
            <Table>
              <Table.THead>
                <Table.TR>
                  <Table.TH>{translations.tableTitlePositionDate as string}</Table.TH>
                  <Table.TH>{translations.tableTitleOperation as string}</Table.TH>
                  <Table.TH $textAlign="right">{translations.tableTitleStockQuantity as string}</Table.TH>
                  <Table.TH $textAlign="right">{translations.tableTitleClosingPrice as string}</Table.TH>
                  <Table.TH $textAlign="right">{translations.tableTitleTotalValue as string}</Table.TH>
                </Table.TR>
              </Table.THead>
              <Table.TBody>
                {dailyPositionList?.length === 0 ? (
                  <DataNotFound>{translations.dataNotFound as string}</DataNotFound>
                ) : (
                  dailyPositionList?.map((position) => {
                    const { referenceDate, operation, variation, closingPrice, value } = position
                    const key = generateUniqueId()
                    return (
                      <Table.TR key={key}>
                        <Table.TD>{referenceDate}</Table.TD>
                        <Table.TD>{operation}</Table.TD>
                        <Table.TD $textAlign="right">{variation}</Table.TD>
                        <Table.TD $textAlign="right">{closingPrice}</Table.TD>
                        <Table.TD $textAlign="right">{value}</Table.TD>
                      </Table.TR>
                    )
                  })
                )}
              </Table.TBody>
            </Table>
          </DailyPosition.Content>
        )}
      </PageContent>

      <MonitoredShareholdersSearchModal
        title={translations.searchModalTitle as string}
        message={translations.searchModalMessage as string}
        onClose={handleCloseMonitoredShareholdersSearchModal}
        visibility={searchShareholdersModalVisibility}
        classification={selectedClassification}
        setSelected={setSelectedMonitoredShareholder}
      />
    </Page>
  )
}
