import { i18n } from 'translate'

export const translations = {
  help: i18n.t('globals.help'),

  startDateLabel: i18n.t('tickerPriceHistory.StartDateLabel'),
  endDateLabel: i18n.t('tickerPriceHistory.EndDateLabel'),
  dateFormat: i18n.t('globals.DatePickerDateFormat'),
  classificationLabel: i18n.t('monitoring.classification'),
  selectedShareholderTooltip: i18n.t('monitoring.selectedShareholderTooltip'),
  tickerLabel: i18n.t('stockType'),
  exportLabel: i18n.t('monitoring.export'),
  loadLabel: i18n.t('monitoring.load'),
  stepOneLabel: i18n.t('monitoring.steps.stepOne'),
  stepTwoLabel: i18n.t('monitoring.steps.stepTwo'),
  stepThreeLabel: i18n.t('monitoring.steps.stepThree'),
  datePickerToolTipText: i18n.t('monitoring.tooltipText'),
  loadTooltipText: i18n.t('monitoring.loadTooltip'),
  monitoredLabel: i18n.t('monitoring.monitored'),
  exportDailyPosition: i18n.t('monitoring.exportDailyPosition'),
  exportListMessage: i18n.t('monitoring.exportListMessage'),

  tableTitlePositionDate: i18n.t('monitoring.dailyPosition.positionDate'),
  tableTitleOperation: i18n.t('monitoring.dailyPosition.operation'),
  tableTitleStockQuantity: i18n.t('monitoring.dailyPosition.stockQuantity'),
  tableTitleClosingPrice: i18n.t('monitoring.dailyPosition.closingPrice'),
  tableTitleTotalValue: i18n.t('monitoring.dailyPosition.totalValue'),

  dataNotFound: i18n.t('dataNotFound'),

  searchModalTitle: i18n.t('monitoring.monitoredShareholdersSearchModal.title'),
  searchModalMessage: i18n.t('monitoring.monitoredShareholdersSearchModal.message'),

  noMonitoredShareholdersAlertTitle: i18n.t('monitoring.errors.noMonitoredShareholdersRegistred.title'),
  noMonitoredShareholdersAlertMessage: i18n.t('monitoring.errors.noMonitoredShareholdersRegistred.message'),
}
