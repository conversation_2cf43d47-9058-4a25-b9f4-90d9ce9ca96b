import { useState, useEffect } from 'react'
import { i18n } from 'translate'
import { TOption, useToast, useTours } from '@mz-codes/design-system'
import { getCompany } from 'globals/storages/locals'
import { getTickers } from 'globals/services/tickers'
import { getDailyPositionClassifications } from 'client'
import { onExportDailyPosition } from 'globals/services/onExportDailyPosition'

import { dateFromUTC, daysBetweenDates, formatDateToString } from 'utils'

import { allMonitoredShareholder } from 'components'
import { SerializedMonitoredShareholder, getAvailableDates, getSerializedDailyPosition, DailyPositionData } from 'hooks'
import { BaseError } from 'errors'

import { DefaultMonitoredError, InvalidDatesRange } from '../../errors'
import { translations } from './daily-position.translations'
import { DailyPositionTemplate } from './daily-position.template'
import { tours } from './daily-position.tours'

const idiom = i18n.language.startsWith('pt') ? '1' : '0'
const defaultTickers = { label: '', value: '' }
const defaultClassification = { label: 'Todos', value: '' }

export function DailyPosition(): React.ReactElement {
  const maxRangeInDays = 35
  const company = getCompany()
  const today = new Date()
  const { createToast } = useToast()

  const [tickers, setTickers] = useState<TOption[]>([])
  const [selectedTicker, setSelectedTicker] = useState<TOption>(defaultTickers)
  const [selectedStartDate, setSelectedStartDate] = useState<Date>(today)
  const [selectedEndDate, setSelectedEndDate] = useState<Date>(today)
  const [isLoading, setIsLoading] = useState(true)
  const [disableLoadButton, setDisableLoadButton] = useState(true)
  const [classificationList, setClassificationList] = useState<TOption[]>([])
  const [selectedClassification, setSelectedClassification] = useState<TOption>(defaultClassification)
  const [isExportDisabled, setDisableExport] = useState(false)

  const [availableDates, setAvailableDates] = useState<Date[]>([])

  const [searchShareholdersModalVisibility, setSearchShareholdersModalVisibility] = useState(false)
  const [selectedMonitoredShareholder, setSelectedMonitoredShareholder] =
    useState<SerializedMonitoredShareholder>(allMonitoredShareholder)
  const [dailyPosition, setDailyPosition] = useState<undefined | DailyPositionData[]>()

  const disableHeaderOptions = !tickers || !availableDates?.length || isLoading

  const handleStartDateAvailableDatesLimit = () => {
    const dates = availableDates as Date[]
    return dates.filter((date) => date <= selectedEndDate)
  }

  const handleEndDateAvailableDatesLimit = () => {
    const dates = availableDates as Date[]
    return dates.filter((date) => date >= selectedStartDate)
  }

  useEffect(() => {
    getTickers(company.id).then((items) => {
      const options = items.map<TOption>((ticker) => ({
        label: ticker.label,
        value: ticker.tickerId,
      }))

      setTickers(options)
      setSelectedTicker(options[0])
    })

    getDailyPositionClassifications(company.id).then((classifications) => {
      if (!classifications.length) {
        createToast({
          type: 'alert',
          title: translations.noMonitoredShareholdersAlertTitle,
          description: translations.noMonitoredShareholdersAlertMessage,
          duration: 10000,
        })
        setDisableExport(true)
      }

      const availableClassifications = [
        defaultClassification,
        ...classifications.map((classification: string, index: number) => {
          return { label: classification, value: classification, id: index }
        }),
      ]

      setClassificationList(availableClassifications)
      setSelectedClassification(availableClassifications[0])
    })
  }, [company.id, createToast])

  useEffect(() => {
    if (!selectedTicker.value) return
    setIsLoading(true)

    getAvailableDates({ companyId: company.id, tickerId: selectedTicker.value as string }).then((res) => {
      const parsedDates: Date[] = res
        .sort((a: string, b: string) => (a > b ? 1 : -1))
        .map((date: string) => dateFromUTC(date))

      const lastAvaiableDate = parsedDates[parsedDates.length - 1]

      setAvailableDates(parsedDates)
      setIsLoading(false)

      if (!lastAvaiableDate) return

      setSelectedStartDate(lastAvaiableDate)
      setSelectedEndDate(lastAvaiableDate)
    })
  }, [selectedTicker, company.id])

  const { run, startTour, setPageTours } = useTours()

  useEffect(() => {
    setPageTours(tours)
  }, [setPageTours])

  useEffect(() => {
    run()
  }, [run])

  useEffect(() => {
    setDisableLoadButton(isLoading || selectedMonitoredShareholder.contactId === allMonitoredShareholder.contactId)
  }, [isLoading, selectedMonitoredShareholder])

  useEffect(() => {
    setDailyPosition(undefined)
  }, [selectedMonitoredShareholder])

  const onTickerChangeValue = (value: TOption) => {
    setDisableExport(false)
    setSelectedTicker(value)
  }

  const checkRangeLimits = (startDate: Date, endDate: Date): boolean => {
    const diffInDays = daysBetweenDates(startDate, endDate)
    return startDate <= endDate && endDate >= startDate && diffInDays <= maxRangeInDays
  }

  const onStartDateChange = (value: Date) => {
    if (!checkRangeLimits(value, selectedEndDate)) setSelectedEndDate(value)
    setSelectedStartDate(value)
    setDisableExport(false)
  }

  const onEndDateChange = (value: Date) => {
    if (!checkRangeLimits(selectedStartDate, value)) setSelectedStartDate(value)
    setSelectedEndDate(value)

    setDisableExport(false)
  }

  const onClassificationChangeValue = (classification: TOption) => {
    if (classification === selectedClassification) return
    setDisableExport(false)
    setSelectedClassification(classification)
    setSelectedMonitoredShareholder(allMonitoredShareholder)
    setDailyPosition(undefined)
  }

  const handleLoadDailyPositionList = async () => {
    setIsLoading(true)
    const resData = await getSerializedDailyPosition({
      companyId: company.id,
      tickerId: selectedTicker.value as string,
      referenceDateStart: formatDateToString(selectedStartDate),
      referenceDateEnd: formatDateToString(selectedEndDate),
      contactId: selectedMonitoredShareholder.contactId,
    })
    setDailyPosition(resData)
    setIsLoading(false)
  }

  const checkSelectedDates = (): void => {
    const diffInDays = daysBetweenDates(selectedStartDate, selectedEndDate)

    if (diffInDays > maxRangeInDays) throw InvalidDatesRange
  }

  const handleOpenMonitoredShareholdersSearchModal = () => {
    setSearchShareholdersModalVisibility(true)
  }

  const handleCloseMonitoredShareholdersSearchModal = () => {
    setSearchShareholdersModalVisibility(false)
  }

  const onExportClick = async () => {
    try {
      checkSelectedDates()

      const result = await onExportDailyPosition({
        companyId: company.id,
        tickerId: selectedTicker.value as string,
        referenceDateStart: formatDateToString(selectedStartDate),
        referenceDateEnd: formatDateToString(selectedEndDate),
        language: idiom,
        classification: selectedClassification.value as string,
      })

      if (!result.success) throw DefaultMonitoredError

      createToast({
        type: 'success',
        title: translations.exportDailyPosition as string,
        description: translations.exportListMessage as string,
      })

      setDisableExport(true)
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          type: 'error',
        })
        return
      }
      createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        type: 'error',
      })
    }
  }

  return (
    <DailyPositionTemplate
      translations={translations}
      disableHeaderOptions={disableHeaderOptions}
      tickers={tickers}
      onTickerChangeValue={onTickerChangeValue}
      selectedTicker={selectedTicker}
      onStartDateChange={onStartDateChange}
      selectedStartDate={selectedStartDate}
      onEndDateChange={onEndDateChange}
      handleStartDateAvailableDatesLimit={handleStartDateAvailableDatesLimit}
      handleEndDateAvailableDatesLimit={handleEndDateAvailableDatesLimit}
      selectedEndDate={selectedEndDate}
      classificationList={classificationList}
      selectedClassification={selectedClassification}
      onClassificationChangeValue={onClassificationChangeValue}
      isLoading={isLoading}
      handleLoadDailyPositionList={handleLoadDailyPositionList}
      disableLoadButton={disableLoadButton}
      onExportClick={onExportClick}
      searchShareholdersModalVisibility={searchShareholdersModalVisibility}
      handleOpenMonitoredShareholdersSearchModal={handleOpenMonitoredShareholdersSearchModal}
      handleCloseMonitoredShareholdersSearchModal={handleCloseMonitoredShareholdersSearchModal}
      selectedMonitoredShareholder={selectedMonitoredShareholder}
      setSelectedMonitoredShareholder={setSelectedMonitoredShareholder}
      isExportDisabled={isExportDisabled}
      dailyPositionList={dailyPosition}
      startTour={startTour}
    />
  )
}
