import { TOption } from '@mz-codes/design-system'
import { DailyPositionData, SerializedMonitoredShareholder } from 'hooks'
import { translations } from './daily-position.translations'

export type TTranslations = typeof translations

export type TDailyPositionTemplateProps = {
  translations: TTranslations
  disableHeaderOptions: boolean
  tickers: TOption[]
  selectedTicker: TOption
  onTickerChangeValue(option: TOption): void
  onStartDateChange(args: Date | null): void
  selectedStartDate: Date
  onEndDateChange(args: Date | null): void
  handleStartDateAvailableDatesLimit(): Date[]
  handleEndDateAvailableDatesLimit(): Date[]
  selectedEndDate: Date
  classificationList: TOption[]
  selectedClassification: TOption
  onClassificationChangeValue(value: TOption): void
  handleLoadDailyPositionList(): Promise<void>
  disableLoadButton: boolean
  onExportClick(): Promise<void>
  isLoading: boolean
  searchShareholdersModalVisibility: boolean
  handleOpenMonitoredShareholdersSearchModal(): void
  handleCloseMonitoredShareholdersSearchModal(): void
  selectedMonitoredShareholder: SerializedMonitoredShareholder
  setSelectedMonitoredShareholder(monitoredShareholder: SerializedMonitoredShareholder): void
  isExportDisabled: boolean
  dailyPositionList?: DailyPositionData[]
  startTour(tourName: string): void
}
