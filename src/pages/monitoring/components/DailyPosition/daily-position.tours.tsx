import { translations } from './daily-position.translations'

export const tours = {
  'daily-position-introduction': [
    {
      disableBeacon: true,
      target: '#daily-position-header-content',
      content: translations.stepOneLabel,
    },
    {
      disableBeacon: true,
      target: '#daily-position-date-range-picker',
      content: translations.datePickerToolTipText,
    },
    {
      disableBeacon: true,
      target: '#daily-position-monitored-shareholder',
      content: translations.selectedShareholderTooltip,
    },
    {
      disableBeacon: true,
      target: '#daily-position-confirm-button',
      content: translations.loadTooltipText,
    },
  ],
}
