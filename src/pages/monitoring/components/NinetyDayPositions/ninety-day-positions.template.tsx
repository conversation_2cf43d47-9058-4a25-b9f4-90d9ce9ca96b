import { Page, PageContent, UploadModal } from 'components'
import { Buttons, Dropdown, NewDatepicker, Header } from '@mz-codes/design-system'

import { i18n } from 'translate'
import { TNinetyDayPositionsProps } from './ninety-day-positions.types'

import * as Steps from '../Steps'

function NinetyDayPositionsTemplate(props: TNinetyDayPositionsProps) {
  const {
    tickers,
    selectedTicker,
    onTickerChange,

    selectedStartDate,
    onStartDateChange,

    selectedEndDate,
    onEndDateChange,

    onLoadClick,
    showUploadModal,

    onUploadClick,

    onCloseUploadModal,
    selectedFile,
    onSelectedFile,
    uploadProgress,
    translations,

    uploadModalAcceptedFiles,
    uploadModalTemplateDownload,
  } = props

  return (
    <Page>
      <Header>
        <Header.Content>
          <Header.Item>
            <Header.Label>{translations.tickerLabel}</Header.Label>
            <Dropdown options={tickers} selected={selectedTicker} onChange={onTickerChange} />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.startDateLabel}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedStartDate}
              onChange={onStartDateChange}
              maxDate={selectedEndDate}
              hint={i18n.t('startDate')}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.endDateLabel}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedEndDate}
              onChange={onEndDateChange}
              minDate={selectedStartDate}
              hint={i18n.t('endDate')}
            />
          </Header.Item>
        </Header.Content>
        <Header.ButtonGroup>
          <Buttons.Primary onClick={onLoadClick}>{translations.loadButton}</Buttons.Primary>
        </Header.ButtonGroup>
      </Header>
      <PageContent style={{ display: 'flex' }}>
        <Steps.Content>
          <Steps.Item>
            <Steps.Title>1</Steps.Title>
            <Steps.Legend>{translations.stepOneLabel}</Steps.Legend>
          </Steps.Item>
          <Steps.Item>
            <Steps.Title>2</Steps.Title>
            <Steps.Legend>{translations.stepTwoLabel}</Steps.Legend>
          </Steps.Item>
          <Steps.Item>
            <Steps.Title>3</Steps.Title>
            <Steps.Legend>{translations.stepThreeLabel}</Steps.Legend>
          </Steps.Item>
        </Steps.Content>
      </PageContent>

      <UploadModal
        show={showUploadModal}
        onConfirm={onUploadClick}
        onClose={onCloseUploadModal}
        selectedFile={selectedFile}
        onSelectedFile={onSelectedFile}
        title={translations.uploadModalTitle}
        message={translations.uploadModalMessage}
        progress={uploadProgress}
        acceptedFiles={uploadModalAcceptedFiles}
        confirmButtonLabel={translations.uploadModalConfirmButtonLabel}
        templateLabel={translations.uploadModalTemplateLabel}
        onTemplateClick={uploadModalTemplateDownload}
      />
    </Page>
  )
}

export default NinetyDayPositionsTemplate
