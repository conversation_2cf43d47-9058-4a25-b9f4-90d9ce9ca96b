import { MZ_IRM_NEW, api } from 'globals/api'
import { isBaseError } from 'pages/monitoring/errors/guards'
import { NinetyDayPositionReportError } from '../errors'
import {
  TGetNinetyDayPositionFileTemplateParams,
  TGetNinetyDayPositionFileTemplateResponse,
} from './get-ninety-day-position-file-template.types'

const getNinetyDayPositionFileTemplate = async (
  params: TGetNinetyDayPositionFileTemplateParams
): Promise<TGetNinetyDayPositionFileTemplateResponse> => {
  try {
    const { idiom } = params
    const response = await api.get<ArrayBuffer, TGetNinetyDayPositionFileTemplateResponse>(
      `${MZ_IRM_NEW}/reports/ninety-day-position/template`,
      {
        responseType: 'arraybuffer',
        params: {
          idiom,
        },
      }
    )

    return response
  } catch (error: unknown) {
    if (isBaseError(error)) throw error

    throw new NinetyDayPositionReportError()
  }
}

export { getNinetyDayPositionFileTemplate }
