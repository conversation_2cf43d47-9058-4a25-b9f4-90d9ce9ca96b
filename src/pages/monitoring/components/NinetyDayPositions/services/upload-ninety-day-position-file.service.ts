import { format } from 'date-fns'
import { MZ_IRM_NEW, api } from 'globals/api'
import { isBaseError } from 'pages/monitoring/errors/guards'
import { daysBetweenDates } from 'utils'
import { NinetyDayPositionReportError, NinetyDayPositionReportIvalidPeriodReportError } from '../errors'
import { TUploadNinetyDayPositionFileParams } from './upload-ninety-day-position-file.types'

const checkRangeLimits = (startDate: Date, endDate: Date): void => {
  const diffInDays = daysBetweenDates(startDate, endDate)
  if (diffInDays > 95) throw new NinetyDayPositionReportIvalidPeriodReportError()
}

const uploadNinetyDayPositionFile = async (params: TUploadNinetyDayPositionFileParams) => {
  try {
    const { companyId, tickerId, file, onUploadProgress, startDate, endDate, idiom } = params

    checkRangeLimits(startDate, endDate)

    const parsedReferenceDateStart = format(startDate, 'yyyy-MM-dd')
    const parsedReferenceDateEnd = format(endDate, 'yyyy-MM-dd')

    const payload = new FormData()
    payload.append('referenceDateStart', parsedReferenceDateStart)
    payload.append('referenceDateEnd', parsedReferenceDateEnd)
    payload.append('idiom', idiom.toString())
    payload.append('file', file)

    const config = { onUploadProgress }
    const { data } = await api.post(
      `${MZ_IRM_NEW}/companies/${companyId}/tickers/${tickerId}/reports/ninety-day-position`,
      payload,
      config
    )

    return data
  } catch (error: unknown) {
    if (isBaseError(error)) throw error

    throw new NinetyDayPositionReportError()
  }
}

export { uploadNinetyDayPositionFile }
