import { i18n } from 'translate'

const confirmButtonLabel = i18n.t('ninetyDayPositions.uploadFileModal.confirmButton')

export const translations = {
  loadButton: i18n.t('ninetyDayPositions.load'),
  tickerLabel: i18n.t('globals.headerContent.stockType'),
  startDateLabel: i18n.t('globals.startDate'),
  endDateLabel: i18n.t('globals.endDate'),

  stepOneLabel: i18n.t('ninetyDayPositions.steps.stepOne'),
  stepTwoLabel: i18n.t('ninetyDayPositions.steps.stepTwo'),
  stepThreeLabel: i18n.t('ninetyDayPositions.steps.stepThree'),

  endDateToolTipText: i18n.t('ninetyDayPositions.tooltipText'),

  exportSuccesstitle: i18n.t('ninetyDayPositions.export.success.title'),
  exportSuccessDescription: i18n.t('ninetyDayPositions.export.success.message'),

  uploadModalTitle: i18n.t('ninetyDayPositions.uploadFileModal.title'),
  uploadModalMessage: i18n.t('ninetyDayPositions.uploadFileModal.infoText', { confirmButtonLabel }),
  uploadModalTemplateLabel: i18n.t('ninetyDayPositions.uploadFileModal.templateLabel'),
  uploadModalConfirmButtonLabel: confirmButtonLabel,
}
