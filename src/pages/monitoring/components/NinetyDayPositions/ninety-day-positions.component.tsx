import { useEffect, useState } from 'react'
import { TOption, useToast } from '@mz-codes/design-system'

import { i18n } from 'translate'
import { doDownload, subtractDays } from 'utils'

import { GoToHistoryButton } from 'components'

import { getTickers } from 'globals/services/tickers'
import { getCompany } from 'globals/storages/locals'

import { BaseError } from 'errors'
import { AxiosProgressEvent } from 'axios'
import { uploadNinetyDayPositionFile, getNinetyDayPositionFileTemplate } from './services'

import NinetyDayPositionsTemplate from './ninety-day-positions.template'

import { translations } from './ninety-day-positions.translations'

const defaultTickers = { label: '', value: '' }
const acceptedFiles = '.xls, .xlsx'

function NinetyDayPositions() {
  const company = getCompany()
  const today = new Date()
  const { createToast } = useToast()

  const [tickers, setTickers] = useState<TOption[]>([])
  const [selectedTicker, setSelectedTicker] = useState<TOption>(defaultTickers)

  const [selectedStartDate, setSelectedStartDate] = useState<Date>(subtractDays(today, 90))
  const [selectedEndDate, setSelectedEndDate] = useState<Date>(today)

  const [showUploadModal, setShowUploadModal] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File>()
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const idiom = i18n.language === 'pt-BR' ? 1 : 0

  useEffect(() => {
    const fetchData = async () => {
      const response = await getTickers(company.id)

      const options = response.map<TOption>((ticker) => ({
        label: ticker.label,
        value: ticker.tickerId,
      }))

      setTickers(options)
      setSelectedTicker(options[0])
    }

    fetchData()
  }, [company.id])

  const handleDownloadTemplate = async () => {
    const response = await getNinetyDayPositionFileTemplate({ idiom })

    doDownload(response)
  }

  const handleTickerChange = (ticker: TOption) => {
    if (ticker === selectedTicker) return
    setSelectedTicker(ticker)
  }

  const handleStartDateChange = (date: Date) => {
    if (date === selectedStartDate) return

    setSelectedStartDate(date)
  }

  const handleEndDateChange = (date: Date) => {
    if (date === selectedEndDate) return
    setSelectedEndDate(date)
  }

  const handleLoadClick = () => {
    setShowUploadModal(true)
  }

  const handleCloseUploadModal = () => {
    setShowUploadModal(false)
    setSelectedFile(undefined)
  }

  const handleSelectedFile = (file: File) => {
    if (file === selectedFile) return
    setSelectedFile(file)
  }

  const handleProgressFileUpload = (progressEvent: AxiosProgressEvent) => {
    const { loaded, total = 0 } = progressEvent
    const progress = total > 0 ? Math.round((loaded * 100) / total) : 0
    setUploadProgress(progress)
  }

  const handleUploadFile = async () => {
    try {
      if (!selectedFile) return

      await uploadNinetyDayPositionFile({
        file: selectedFile,
        companyId: company.id,
        tickerId: selectedTicker.value as string,
        startDate: selectedStartDate,
        endDate: selectedEndDate,
        onUploadProgress: handleProgressFileUpload,
        idiom,
      })

      createToast({
        type: 'success',
        title: translations.exportSuccesstitle as string,
        description: translations.exportSuccessDescription as string,
        duration: 10000,
        buttons: <GoToHistoryButton />,
      })
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          type: 'error',
          title: err.title,
          description: err.message,
        })
        return
      }
      createToast({
        type: 'error',
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
      })
    } finally {
      setUploadProgress(0)
      setShowUploadModal(false)
      setSelectedFile(undefined)
    }
  }

  return (
    <NinetyDayPositionsTemplate
      tickers={tickers}
      selectedTicker={selectedTicker}
      onTickerChange={handleTickerChange}
      selectedStartDate={selectedStartDate}
      onStartDateChange={handleStartDateChange}
      selectedEndDate={selectedEndDate}
      onEndDateChange={handleEndDateChange}
      showUploadModal={showUploadModal}
      onLoadClick={handleLoadClick}
      onCloseUploadModal={handleCloseUploadModal}
      onUploadClick={handleUploadFile}
      selectedFile={selectedFile}
      onSelectedFile={handleSelectedFile}
      uploadProgress={uploadProgress}
      translations={translations}
      uploadModalAcceptedFiles={acceptedFiles}
      uploadModalTemplateDownload={handleDownloadTemplate}
    />
  )
}

export { NinetyDayPositions }
