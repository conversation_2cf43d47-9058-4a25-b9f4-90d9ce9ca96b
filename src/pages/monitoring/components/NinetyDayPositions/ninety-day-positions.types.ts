import { TOption } from '@mz-codes/design-system'
import { translations } from './ninety-day-positions.translations'

type TTranslations = typeof translations

export type TNinetyDayPositionsProps = {
  tickers: TOption[]
  selectedTicker: TOption
  onTickerChange(option: TOption): void

  selectedStartDate: Date
  onStartDateChange(date: Date | null): void

  selectedEndDate: Date
  onEndDateChange(date: Date | null): void

  showUploadModal: boolean

  onLoadClick(): void
  onCloseUploadModal(): void

  onUploadClick(): void

  selectedFile?: File
  onSelectedFile(file: File): void
  uploadProgress?: number

  translations: TTranslations

  uploadModalAcceptedFiles: string
  uploadModalTemplateDownload?(): void
}
