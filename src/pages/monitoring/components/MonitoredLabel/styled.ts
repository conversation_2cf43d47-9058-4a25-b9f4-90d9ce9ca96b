import styled, { css } from 'styled-components'
import { ARROW_DOWN } from 'assets/png'

type TContainer = {
  disabled?: boolean
}

export const Container = styled.div<TContainer>((props) => {
  const { disabled } = props
  return css`
    display: flex;
    align-items: center;
    cursor: ${disabled ? 'default' : 'pointer'};
    opacity: ${disabled ? 0.5 : 'unset'};
    &[disabled] {
      pointer-events: none;
    }
  `
})

export const Label = styled.label`
  color: white;
  font-weight: 400;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.neutral.grey[300]};
  line-height: 14px;
  border: 0;
  height: 30px;
  display: flex;
  align-items: center;
  cursor: inherit;
`

export const Arrow = styled.div`
  background: url(${ARROW_DOWN});
  display: inline-block;
  width: 9px;
  height: 6px;
  margin-left: 6px;
  transform: rotate(265deg);
  cursor: inherit;
`
