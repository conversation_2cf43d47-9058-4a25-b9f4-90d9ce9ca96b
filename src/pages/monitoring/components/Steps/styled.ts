import styled from 'styled-components'

export const Content = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
`

export const Item = styled.div`
  max-width: 430px;
  display: flex;
  align-items: center;
  flex-direction: column;
`

export const Title = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
  border: 6px solid ${({ theme }) => theme.legacy.colors.primary.primary};
  color: white;
  font-weight: 400;
  font-size: 40px;
  line-height: 14px;
  border-radius: 50%;
`

export const Legend = styled.p`
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  display: flex;
  align-items: center;
  text-align: center;
  letter-spacing: 0.01em;
  color: white;
`
