import styled from 'styled-components'

export const Content = styled.div`
  width: 100%;
`

export const Header = styled.header`
  height: 49px;
  display: flex;
  align-items: center;
  padding: 10px 20px;
  justify-content: flex-start;
  position: relative;
  box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);
`
export const Title = styled.h3`
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  position: relative;
  line-height: 48px;
  color: white;
  img {
    margin-right: 12px;
  }
  &:first-child {
    margin-right: 8px;
    padding-right: 8px;
    position: relative;
    &::before {
      content: '';
      width: 1px;
      height: 17px;
      background: #303030;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
`
