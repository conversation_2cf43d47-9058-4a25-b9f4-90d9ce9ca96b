import { i18n } from 'translate'

export const translations = {
  filters: {
    reportType: i18n.t('exportHistory.filters.reportType'),
    ticker: i18n.t('exportHistory.filters.ticker'),
    status: i18n.t('exportHistory.filters.status'),
    quantity: i18n.t('exportHistory.filters.quantity'),
    startDate: i18n.t('tickerPriceHistory.StartDateLabel'),
    endDate: i18n.t('tickerPriceHistory.EndDateLabel'),
  },
  status: {
    requested: i18n.t('exportHistory.Status.Requested'),
    processing: i18n.t('exportHistory.Status.Processing'),
    uploading: i18n.t('exportHistory.Status.Uploading'),
    finished: i18n.t('exportHistory.Status.Finished'),
    failed: i18n.t('exportHistory.Status.Failed'),
  },
  actions: {
    download: i18n.t('exportHistory.actions.download'),
    sendAlert: i18n.t('exportHistory.actions.sendAlert'),
    reprocess: i18n.t('exportHistory.actions.reprocess'),
    delete: i18n.t('exportHistory.actions.delete'),
  },
  content: {
    generatedDate: i18n.t('generatedDate'),
    referenceDate: i18n.t('globals.referenceDate'),
    exportReportType: i18n.t('exportReportType'),
    ticker: i18n.t('ticker'),
    status: i18n.t('globals.status'),
    actions: i18n.t('globals.actions'),
    noHistoryFound: i18n.t('exportHistory.noHistoryFound'),
    dateTimeAmPmFormat: i18n.t('globals.dateFormatAmPm'),
    dateFormat: i18n.t('globals.DatePickerDateFormat'),
    dateLabels: {
      startDate: i18n.t('globals.startDate'),
      endDate: i18n.t('globals.endDate'),
      dateTimeAmPm: i18n.t('displayDateFormat.dateTimeAmPm'),
      date: i18n.t('displayDateFormat.date'),
    },
  },
  toast: {
    delete: {
      success: {
        title: i18n.t('globals.export.delete.title'),
        description: i18n.t('globals.export.delete.message'),
      },
      error: {
        title: i18n.t('globals.errors.exportHistory.delete.title'),
        description: i18n.t('globals.errors.exportHistory.delete.message'),
      },
    },
    sendAlert: {
      success: {
        title: i18n.t('exportHistory.sendAlert.toastTitle'),
        description: i18n.t('exportHistory.sendAlert.toastMessage'),
      },
      error: {
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
      },
    },
    reprocess: {
      success: {
        title: i18n.t('globals.reprocess.success.title'),
        description: i18n.t('globals.reprocess.success.message'),
      },
      error: {
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
      },
    },
    download: {
      error: {
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
      },
    },
  },
  modals: {
    sendAlert: {
      title: i18n.t('globals.errors.requestFail.title'),
      confirmMessage: i18n.t('exportHistory.sendAlert.confirmMessage'),
      cancelButton: i18n.t('components.confirmModal.cancelButton'),
      confirmButton: i18n.t('components.confirmModal.confirmButton'),
    },
    delete: {
      title: i18n.t('exportHistory.deleteModal.title'),
      label: i18n.t('exportHistory.deleteModal.label'),
    },
  },
}
