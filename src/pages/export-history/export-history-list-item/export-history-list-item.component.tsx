import { useState } from 'react'
import { Icons, theme, useToast } from '@mz-codes/design-system'
import { dateFromUTC, formatDate, hasAdminPermission, hasScopePermission, localInformation, doDownload } from 'utils'
import { getIrmReportFile, getReportFile } from 'hooks'
import { ExportHistoryListItemTemplate } from './export-history-list-item.template'
import {
  TExportHistoryListItem,
  TExportHistoryStatusCode,
  TExportHistoryActionButtonTypes,
  TDownloadFile,
} from './export-history-list-item.types'
import { translations } from '../export-history.translations'

export function ExportHistoryListItem(props: TExportHistoryListItem) {
  const {
    createdAt,
    referenceDate,
    ticker,
    tickerId,
    reportType,
    status,
    reportModel,
    reportId,
    handleSendEmailConfiguration,
    handleReprocessExport,
    handleDeleteExport,
    meta,
    origin,
    fileName,
  } = props

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const checkMetaData = Object.keys(meta).length > 0
  const companyId = localInformation.getCompanyId()
  const hasAdminPermissionValidation = hasAdminPermission()
  const { createToast } = useToast()
  const hasPermissionToDeleteExport = hasScopePermission('mzshareholders:management:companies:id:reports:id:delete')
  const { dateFormat } = translations.content
  const generatedReferenceDate = referenceDate ? formatDate(dateFromUTC(referenceDate), dateFormat) : ''

  const downloadFile = async ({
    reportId: fileReportId,
    downloadFileName,
    origin: fileOrigin = 'shareholders',
  }: TDownloadFile) => {
    try {
      const response =
        fileOrigin === 'IRM'
          ? await getIrmReportFile({ reportId: fileReportId, companyId })
          : await getReportFile({ reportId: fileReportId })

      if (!response) {
        throw new Error('Resposta vazia do servidor')
      }

      if (typeof response === 'object' && 'data' in response && 'headers' in response) {
        doDownload(response, downloadFileName)
      } else {
        const formattedResponse = {
          data: response,
          headers: {
            'content-type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          },
        }
        doDownload(formattedResponse, downloadFileName)
      }
    } catch (error) {
      createToast({
        title: translations.toast.download.error.title,
        description: translations.toast.download.error.description,
        type: 'error',
      })
    }
  }

  const handleGetFile = async () => {
    try {
      setIsLoading(true)

      const ext = fileName.split('.').pop()
      const downloadFileName = !ticker || !referenceDate ? fileName : `${ticker}-${reportType}-${referenceDate}.${ext}`

      await downloadFile({
        reportId,
        downloadFileName,
        origin,
      })
    } catch (error) {
      createToast({
        title: translations.toast.download.error.title,
        description: translations.toast.download.error.description,
        type: 'error',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (statusCode: TExportHistoryStatusCode) => {
    const statusColors = {
      requested: theme.colors.semantic.yellow[500],
      processing: theme.colors.semantic.yellow[500],
      uploading: theme.colors.semantic.yellow[500],
      finished: theme.colors.semantic.green[500],
      failed: theme.colors.semantic.red[500],
    }
    return statusColors[statusCode]
  }

  const getStatusFile = (statusFile: TExportHistoryStatusCode) => {
    return translations.status[statusFile]
  }

  function getActionButton(type: TExportHistoryActionButtonTypes) {
    const buttonConfig = {
      excel: {
        handle: () => handleGetFile(),
        icon: <Icons.Download size={18} />,
        title: translations.actions.download,
      },
      email: {
        handle: () =>
          handleSendEmailConfiguration({
            reportId,
            tickerId,
            reportType,
          }),
        icon: <Icons.Send size={18} />,
        title: translations.actions.sendAlert,
      },
    }

    return buttonConfig[type] || buttonConfig.excel
  }

  const itemConfig = getActionButton(reportModel)

  const processingStatus = (statusValue: TExportHistoryStatusCode) => {
    const processingNumber = ['requested', 'processing', 'uploading']
    return processingNumber.includes(statusValue)
  }

  return (
    <ExportHistoryListItemTemplate
      generatedDate={createdAt}
      generatedReferenceDate={generatedReferenceDate}
      origin={origin}
      reportType={reportType}
      ticker={ticker}
      status={status}
      isLoading={isLoading}
      reportModel={reportModel}
      itemConfig={itemConfig}
      processingStatus={processingStatus}
      hasAdminPermissionValidation={hasAdminPermissionValidation}
      checkMetaData={checkMetaData}
      handleReprocessExport={handleReprocessExport}
      hasPermissionToDeleteExport={hasPermissionToDeleteExport}
      reportId={reportId}
      handleDeleteExport={handleDeleteExport}
      getStatusFile={getStatusFile}
      getStatusColor={getStatusColor}
    />
  )
}
