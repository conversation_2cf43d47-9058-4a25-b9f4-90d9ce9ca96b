import { ALIGNMENTS, IconButton, Icons, Loading, Table, Tooltip } from '@mz-codes/design-system'
import { TExportHistoryListItemTemplate } from './export-history-list-item.types'
import { ExportHistoryListItemIconWrapperTemplate as IconWrapper } from '../export-history-list-item-icon-wrapper'
import { translations } from '../export-history.translations'

export function ExportHistoryListItemTemplate(props: TExportHistoryListItemTemplate) {
  const {
    generatedDate,
    generatedReferenceDate,
    origin,
    reportType,
    ticker,
    status,
    isLoading,
    reportModel,
    itemConfig,
    processingStatus,
    hasAdminPermissionValidation,
    checkMetaData,
    getStatusColor,
    getStatusFile,
    handleReprocessExport,
    reportId,
    hasPermissionToDeleteExport,
    handleDeleteExport,
  } = props

  const handleDelete = () => handleDeleteExport(reportId)
  const handleProcess = () => handleReprocessExport(reportId)

  const canShowActionButton = !isLoading && !processingStatus(status)
  const canShowReprocessButton =
    canShowActionButton && hasAdminPermissionValidation && checkMetaData && reportModel !== 'email' && origin !== 'IRM'
  const canShowDeleteButton = canShowActionButton && hasPermissionToDeleteExport && origin !== 'IRM'
  const canShowItemConfigButton =
    canShowActionButton && ((status === 'finished' && reportModel !== 'email') || reportModel === 'email')

  return (
    <Table.TR>
      <Table.TD>{generatedDate}</Table.TD>
      <Table.TD>{origin === 'IRM' ? 'N/A' : generatedReferenceDate}</Table.TD>
      <Table.TD>{reportType}</Table.TD>
      <Table.TD>{reportType === 'monitoredReportExcel' || origin === 'IRM' ? 'N/A' : ticker}</Table.TD>
      <Table.TD style={{ color: getStatusColor(status) }}>{getStatusFile(status)}</Table.TD>
      <Table.TD style={{ overflow: 'visible' }}>
        <IconWrapper>
          {isLoading && <Loading />}
          {canShowItemConfigButton && (
            <Tooltip $width="auto" $alignment={ALIGNMENTS.BOTTOM_CENTER} text={itemConfig.title}>
              <IconButton onClick={itemConfig.handle}>{itemConfig.icon}</IconButton>
            </Tooltip>
          )}

          {canShowReprocessButton && (
            <Tooltip $width="auto" $alignment={ALIGNMENTS.BOTTOM_CENTER} text={translations.actions.reprocess}>
              <IconButton onClick={handleProcess}>
                <Icons.Reload size={18} />
              </IconButton>
            </Tooltip>
          )}

          {canShowDeleteButton && (
            <Tooltip $width="auto" $alignment={ALIGNMENTS.BOTTOM_CENTER} text={translations.actions.delete}>
              <IconButton onClick={handleDelete}>
                <Icons.Trash size={18} />
              </IconButton>
            </Tooltip>
          )}
        </IconWrapper>
      </Table.TD>
    </Table.TR>
  )
}
