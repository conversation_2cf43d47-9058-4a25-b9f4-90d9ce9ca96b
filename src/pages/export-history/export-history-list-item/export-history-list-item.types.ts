import { ReactNode } from 'react'
import { TEmailConfig } from '../export-history.types'

export type TExportHistoryStatusCode = 'requested' | 'processing' | 'uploading' | 'finished' | 'failed'
export type TExportHistoryActionButtonTypes = 'excel' | 'email'

export type TExportHistoryListItemTemplate = {
  generatedDate: string
  generatedReferenceDate: string
  origin: string
  reportType: string
  ticker: string
  status: TExportHistoryStatusCode
  isLoading: boolean
  reportModel: TExportHistoryActionButtonTypes
  itemConfig: {
    handle: () => void | Promise<void>
    icon: ReactNode
    title: string
  }
  processingStatus: (status: TExportHistoryStatusCode) => boolean
  hasAdminPermissionValidation: boolean
  checkMetaData: boolean
  handleReprocessExport: (reportId: string) => void
  hasPermissionToDeleteExport: boolean
  reportId: string
  handleDeleteExport: (reportId: string) => void
  getStatusFile: (status: TExportHistoryStatusCode) => string
  getStatusColor: (status: TExportHistoryStatusCode) => string
}

export type TExportHistoryListItem = {
  createdAt: string
  referenceDate: string
  reportType: string
  ticker: string
  fileName: string
  tickerId: string
  status: TExportHistoryStatusCode
  reportModel: TExportHistoryActionButtonTypes
  handleReprocessExport: (reportId: string) => void
  handleDeleteExport: (reportId: string) => void
  reportId: string
  origin: string
  meta: Record<string, string | number | boolean | null>
  handleSendEmailConfiguration: (data: TEmailConfig) => void
}

export type TDownloadFile = {
  reportId: string
  downloadFileName: string
  origin: string
}
