import { TOption } from '@mz-codes/design-system'

export type TExportHistoryHeader = {
  selectedRow: TOption
  selectedTicker: TOption
  selectedReportType: TOption
  selectedReportStatus: TOption
  tickers: Array<TOption>
  startDate: Date
  endDate: Date
  topQuantity: Array<TOption>
  createReportOptions: (reportObject: Record<string, string>) => TOption[]
  hasReportFilterSelected: () => boolean
  handleSelectedRows: (rows: TOption) => void
  handleSelectedTicker: (selectedTicker: TOption) => void
  handleStartDate: (startDate: Date | null) => void
  handleEndDate: (endDate: Date | null) => void
  handleReportType: (reportType: TOption) => void
  handleStatus: (status: TOption) => void
}
