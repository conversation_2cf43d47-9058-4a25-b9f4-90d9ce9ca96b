import { i18n } from 'translate'
import { Header, NewDatepicker, Dropdown } from '@mz-codes/design-system'
import { reportStatusTranslations, reportTranslations } from 'utils'
import { TExportHistoryHeader } from './export-history-header.types'
import { translations } from '../export-history.translations'

export function ExportHistoryHeader(props: TExportHistoryHeader) {
  const {
    selectedRow,
    selectedTicker,
    selectedReportType,
    tickers,
    selectedReportStatus,
    startDate,
    endDate,
    topQuantity,
    createReportOptions,
    hasReportFilterSelected,
    handleSelectedRows,
    handleSelectedTicker,
    handleStartDate,
    handleEndDate,
    handleReportType,
    handleStatus,
  } = props

  const exportTypeOptions = createReportOptions(reportTranslations)
  const exportStatusOptions = createReportOptions(reportStatusTranslations)

  const dropdownOptionsHeight = 400

  return (
    <Header>
      <Header.Content width="100%" data-testid="header-content" style={{ alignItems: 'flex-end' }}>
        <Header.Item style={{ display: 'flex' }}>
          <Header.Label>{translations.filters.startDate}</Header.Label>
          <NewDatepicker
            onChange={handleStartDate}
            maxDate={endDate}
            selected={startDate}
            hint={translations.filters.startDate}
            lang={i18n.language}
            data-testid="datepicker-start"
          />
        </Header.Item>
        <Header.Item style={{ display: 'flex' }}>
          <Header.Label>{translations.filters.endDate}</Header.Label>
          <NewDatepicker
            onChange={handleEndDate}
            minDate={startDate}
            maxDate={new Date()}
            selected={endDate}
            hint={translations.filters.endDate}
            lang={i18n.language}
            data-testid="datepicker-end"
          />
        </Header.Item>
        <Header.Item>
          <Header.Label>{translations.filters.ticker}</Header.Label>
          <Dropdown
            options={tickers}
            onChange={handleSelectedTicker}
            disabled={!hasReportFilterSelected()}
            height={dropdownOptionsHeight}
            selected={selectedTicker}
            data-testid="header-filter"
          />
        </Header.Item>
        <Header.Item>
          <Header.Label>{translations.filters.reportType}</Header.Label>
          <Dropdown
            options={exportTypeOptions}
            onChange={handleReportType}
            height={dropdownOptionsHeight}
            selected={selectedReportType}
            data-testid="header-filter"
          />
        </Header.Item>
        <Header.Item>
          <Header.Label>{translations.filters.status}</Header.Label>
          <Dropdown
            options={exportStatusOptions}
            onChange={handleStatus}
            height={dropdownOptionsHeight}
            selected={selectedReportStatus}
            data-testid="header-filter"
          />
        </Header.Item>
        <Header.Item>
          <Header.Label>{translations.filters.quantity}</Header.Label>
          <Dropdown
            options={topQuantity}
            onChange={handleSelectedRows}
            height={dropdownOptionsHeight}
            selected={selectedRow}
            data-testid="header-filter"
          />
        </Header.Item>
      </Header.Content>
    </Header>
  )
}
