import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { customRender } from 'test'
import { TDatepicker, TDropdown } from '@mz-codes/design-system'
import { i18n } from 'translate'
import { ExportHistoryHeader } from './export-history-header.template'
import { TExportHistoryHeader } from './export-history-header.types'
import { translations } from '../export-history.translations'

const mockHasReportFilterSelected = vi.fn()

const defaultProps: TExportHistoryHeader = {
  topQuantity: [
    { label: '10', value: 10 },
    { label: '20', value: 20 },
  ],
  selectedRow: { label: 'Row 1', value: 'row-1' },
  selectedReportType: { label: 'Shareholders', value: 'shareholders' },
  tickers: [
    { label: 'AAPL', value: 'AAPL' },
    { label: 'GOOGL', value: 'GOOGL' },
  ],
  selectedTicker: { label: 'AAPL', value: 'AAPL' },
  selectedReportStatus: { label: 'Success', value: 'success' },
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-12-31'),
  createReportOptions: vi.fn(),
  handleSelectedRows: vi.fn(),
  handleSelectedTicker: vi.fn(),
  handleStartDate: vi.fn(),
  handleEndDate: vi.fn(),
  handleReportType: vi.fn(),
  handleStatus: vi.fn(),
  hasReportFilterSelected: mockHasReportFilterSelected,
}

const mockDatepicker = vi.fn()
const mockDropdown = vi.fn()

vi.mock('@mz-codes/design-system', async () => {
  const actual = await vi.importActual('@mz-codes/design-system')
  return {
    ...actual,
    Datepicker: (props: TDatepicker & { 'data-testid': string }) => {
      mockDatepicker(props)
      return <div data-testid={props['data-testid']} />
    },
    Dropdown: (props: TDropdown & { 'data-testid': string }) => {
      mockDropdown(props)
      return <div data-testid={props['data-testid']} />
    },
  }
})

describe('Export History Header Template', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('renders the header template', () => {
    const { getByTestId } = customRender(<ExportHistoryHeader {...defaultProps} />)

    const header = getByTestId('header')
    const headerContent = getByTestId('header-content')

    expect(header).toBeInTheDocument()
    expect(headerContent).toBeInTheDocument()
    expect(headerContent).toHaveAttribute('width', '100%')
  })

  describe('Datepickers', () => {
    it('renders 4 Header.Item components when reportType is not from shareholders', () => {
      mockHasReportFilterSelected.mockReturnValue(false)

      const { getAllByTestId } = customRender(<ExportHistoryHeader {...defaultProps} />)

      const headerItems = getAllByTestId('header-item')
      expect(headerItems).toHaveLength(5)

      const datepickersHeaderItem = headerItems[0]
      expect(datepickersHeaderItem).toHaveStyle('display: flex')
    })

    it('renders 5 Header.Item components when reportType is from shareholders', () => {
      mockHasReportFilterSelected.mockReturnValue(true)

      const { getAllByTestId } = customRender(<ExportHistoryHeader {...defaultProps} />)

      const headerItems = getAllByTestId('header-item')
      expect(headerItems).toHaveLength(5)
    })

    it('renders the datepickers correctly', () => {
      const { getByTestId } = customRender(<ExportHistoryHeader {...defaultProps} />)

      const startDatePicker = getByTestId('datepicker-start')
      const endDatePicker = getByTestId('datepicker-end')

      expect(startDatePicker).toBeInTheDocument()
      expect(endDatePicker).toBeInTheDocument()
    })

    it('renders start and end datepickers with correct props', () => {
      customRender(<ExportHistoryHeader {...defaultProps} />)

      expect(mockDatepicker).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          'data-testid': 'datepicker-start',
          selected: defaultProps.startDate,
          onChange: expect.any(Function),
          label: translations.filters.startDate,
          language: i18n.language,
          $maxAvailableDate: defaultProps.endDate,
        })
      )

      expect(mockDatepicker).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({
          'data-testid': 'datepicker-end',
          selected: defaultProps.endDate,
          onChange: expect.any(Function),
          label: translations.filters.endDate,
          language: i18n.language,
          $maxAvailableDate: expect.any(Date),
          $minAvailableDate: defaultProps.startDate,
        })
      )
    })
  })

  describe('Dropdowns', () => {
    it('renders correcttly if reportType is NOT from shareholders', () => {
      mockHasReportFilterSelected.mockReturnValue(false)

      const { queryAllByTestId } = customRender(<ExportHistoryHeader {...defaultProps} />)
      const dropdowns = [...queryAllByTestId('dropdown'), ...queryAllByTestId('header-filter')]

      expect(dropdowns).toHaveLength(4)
    })

    it('renders correcttly if reportType is from shareholders', () => {
      mockHasReportFilterSelected.mockReturnValue(true)

      const { queryAllByTestId } = customRender(<ExportHistoryHeader {...defaultProps} />)
      const dropdowns = [...queryAllByTestId('dropdown'), ...queryAllByTestId('header-filter')]

      expect(dropdowns).toHaveLength(4)
    })
  })
})
