import { useCallback, useEffect, useState } from 'react'

import { COMMON } from 'consts'
import { getTickers } from 'globals/services/tickers'
import { formatDateToString, localInformation } from 'utils'
import { deleteExportReport, postReprocessExportReport, getExportedFiles } from 'hooks'
import { patchResendEmailAlert } from 'hooks/useResendEmailAlert'
import { TOption, useToast } from '@mz-codes/design-system'
import { BaseError } from 'errors'
import { TExportedFile } from 'hooks/get-exported-files/use-get-exported-files.types'
import { TEmailConfig } from './export-history.types'
import { ExportHistoryTemplate } from './export-history.template'
import { translations } from './export-history.translations'

export function ExportHistory() {
  const { topQuantity, noFilterOption } = COMMON
  const { createToast } = useToast()

  const previousMonth = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  const companyId = localInformation.getCompanyId()

  const [selectedTicker, setSelectedTicker] = useState<TOption>(COMMON.noFilterOption)
  const [selectedReportType, setSelectedReportType] = useState<TOption>(COMMON.noFilterOption)
  const [selectedReportStatus, setSelectedReportStatus] = useState<TOption>(COMMON.noFilterOption)

  const [startDate, setStartDate] = useState<Date>(previousMonth)
  const [endDate, setEndDate] = useState<Date>(new Date())

  const [tickers, setTickers] = useState<TOption[]>([])
  const [reports, setReports] = useState<TExportedFile[]>([])

  const [showModal, setShowModal] = useState<boolean>(false)
  const [showReasonModal, setShowReasonModal] = useState<boolean>(false)

  const [currentReportId, setCurrentReportId] = useState<string>('')

  const [selectedRow, setSelectedRow] = useState<TOption>(COMMON.topQuantity[1])

  const [isLoading, setIsLoading] = useState<boolean>(true)

  const [emailConfig, setEmailConfig] = useState<TEmailConfig>({
    reportId: '',
    tickerId: '',
    reportType: '',
  })

  const fetchTickers = useCallback(async () => {
    const items = await getTickers(companyId)
    const options = items.map((ticker: { label: string; tickerId: string }) => ({
      label: ticker.label,
      value: ticker.tickerId as string,
    }))
    options.unshift(COMMON.noFilterOption)
    setTickers(options)
  }, [companyId])

  const getExportedFilesHandler = useCallback(async () => {
    const params = {
      limit: selectedRow.value,
      ...{
        tickerId: (selectedTicker?.value as string) || undefined,
        createdAtStart: formatDateToString(startDate),
        createdAtEnd: formatDateToString(endDate),
        reportType: (selectedReportType?.value as string) || undefined,
        status: (selectedReportStatus?.value as string) || undefined,
        reportModel: undefined,
        referenceDate: undefined,
      },
    }

    const response = await getExportedFiles({ companyId, params })
    setReports(response)
    setIsLoading(false)
  }, [selectedRow, companyId, selectedTicker, startDate, endDate, selectedReportType, selectedReportStatus])

  const handleSelectedRows = (newRows: TOption) => {
    if (newRows === selectedRow) return
    setSelectedRow(newRows)
  }

  const handleSelectedTicker = (tickerValue: TOption) => {
    if (tickerValue === selectedTicker) return
    setSelectedTicker(tickerValue)
  }

  const handleSelectedReportType = (reportTypeValue: TOption) => {
    if (reportTypeValue === selectedReportType) return
    setSelectedReportType(reportTypeValue)
  }

  const handleSelectedReportStatus = (statusValue: TOption) => {
    if (statusValue === selectedReportStatus) return
    setSelectedReportStatus(statusValue)
  }

  const handleStartDate = (newDate: Date | null) => {
    if (!newDate || newDate === startDate) return
    setStartDate(newDate)
  }

  const handleEndDate = (newDate: Date | null) => {
    if (!newDate || newDate === endDate) return
    setEndDate(newDate)
  }

  const createReportOptions = (reportObject: Record<string, string>) => {
    const entries = Object.entries(reportObject)

    const entriesOrdered = entries
      .map((entry) => {
        return { label: entry[1], value: entry[0] }
      })
      .sort((a, b) => a.label.localeCompare(b.label))

    entriesOrdered.unshift(noFilterOption)

    return entriesOrdered
  }

  const handleReprocessExport = async (shareholderReportId: string) => {
    try {
      await postReprocessExportReport({ companyId, shareholderReportId })
      getExportedFilesHandler()
      createToast({
        type: 'success',
        title: translations.toast.reprocess.success.title,
        description: translations.toast.reprocess.success.description,
      })
    } catch (err) {
      if (err instanceof BaseError) {
        createToast({
          type: 'error',
          title: translations.toast.reprocess.error.title,
          description: translations.toast.reprocess.error.description,
        })
      }
    }
  }

  const handleReasonModal = (shareholderReportId?: string) => {
    setShowReasonModal(!showReasonModal)

    if (shareholderReportId) setCurrentReportId(shareholderReportId)
  }

  const handleDeleteExport = async (reason: string) => {
    try {
      await deleteExportReport({
        companyId,
        reportId: currentReportId,
        reason,
      })
      createToast({
        title: translations.toast.delete.success.title,
        description: translations.toast.delete.success.description,
        type: 'success',
      })
    } catch (error) {
      createToast({
        title: translations.toast.delete.error.title,
        description: translations.toast.delete.error.description,
        type: 'error',
      })
    } finally {
      setShowReasonModal(false)
      getExportedFilesHandler()
    }
  }

  const handleAlertModal = () => {
    setShowModal(!showModal)
  }

  const handleSendEmailConfiguration = (data: TEmailConfig) => {
    setEmailConfig({ ...data })
    setShowModal(true)
  }

  const handleSendEmailAlert = async (emailData: TEmailConfig) => {
    try {
      await patchResendEmailAlert({
        companyId,
        tickerId: emailData.tickerId,
        reportId: emailData.reportId,
      })
      createToast({
        title: translations.toast.sendAlert.success.title,
        description: translations.toast.sendAlert.success.description,
        type: 'success',
      })
    } catch (error) {
      createToast({
        title: translations.toast.sendAlert.error.title,
        description: translations.toast.sendAlert.error.description,
        type: 'error',
      })
    } finally {
      setShowModal(false)
    }
  }

  const hasReportFilterSelected = () => {
    if (!selectedReportType || !selectedReportType.value) return false

    const reportsWithoutTicker = [
      'shareholders',
      'shareholdersGrouping',
      'shareholdersGroups',
      'spreadsheetGroupingBackup',
      'contact_tearsheet_private',
      'firm_tearsheet_private',
    ]

    if (reportsWithoutTicker.includes(selectedReportType.value as string)) return false

    return true
  }

  useEffect(() => {
    fetchTickers()
    getExportedFilesHandler()
  }, [fetchTickers, getExportedFilesHandler])

  useEffect(() => {
    setIsLoading(true)
    getExportedFilesHandler()
  }, [
    selectedRow,
    companyId,
    getExportedFilesHandler,
    selectedTicker,
    selectedReportType,
    selectedReportStatus,
    startDate,
    endDate,
  ])

  return (
    <ExportHistoryTemplate
      selectedRow={selectedRow}
      selectedTicker={selectedTicker}
      selectedReportType={selectedReportType}
      selectedReportStatus={selectedReportStatus}
      startDate={startDate}
      endDate={endDate}
      tickers={tickers}
      reports={reports}
      isLoading={isLoading}
      showModal={showModal}
      showReasonModal={showReasonModal}
      emailConfig={emailConfig}
      topQuantity={topQuantity}
      createReportOptions={createReportOptions}
      handleSelectedRows={handleSelectedRows}
      handleSelectedTicker={handleSelectedTicker}
      handleSelectedReportType={handleSelectedReportType}
      handleSelectedReportStatus={handleSelectedReportStatus}
      handleStartDate={handleStartDate}
      handleEndDate={handleEndDate}
      handleReprocessExport={handleReprocessExport}
      handleDeleteExport={handleDeleteExport}
      handleSendEmailConfiguration={handleSendEmailConfiguration}
      handleSendEmailAlert={handleSendEmailAlert}
      hasReportFilterSelected={hasReportFilterSelected}
      handleAlertModal={handleAlertModal}
      handleReasonModal={handleReasonModal}
    />
  )
}
