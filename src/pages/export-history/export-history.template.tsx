import { ConfirmationModal } from '@mz-codes/design-system'
import { Page, PageContent, ReasonModal } from 'components'
import { getReportTypeTranslations } from 'utils'
import { TExportHistoryTemplate } from './export-history.types'
import { ExportHistoryHeader } from './export-history-header'
import { ExportHistoryList } from './export-history-list'
import { translations } from './export-history.translations'

export function ExportHistoryTemplate(props: TExportHistoryTemplate) {
  const {
    selectedRow,
    selectedTicker,
    selectedReportType,
    selectedReportStatus,
    startDate,
    endDate,
    tickers,
    reports,
    isLoading,
    showModal,
    showReasonModal,
    emailConfig,
    topQuantity,
    createReportOptions,
    handleSelectedRows,
    handleSelectedTicker,
    handleSelectedReportType,
    handleSelectedReportStatus,
    handleStartDate,
    handleEndDate,
    handleReprocessExport,
    handleDeleteExport,
    handleSendEmailConfiguration,
    handleSendEmailAlert,
    hasReportFilterSelected,
    handleAlertModal,
    handleReasonModal,
  } = props

  return (
    <Page>
      <ExportHistoryHeader
        selectedRow={selectedRow}
        selectedTicker={selectedTicker}
        selectedReportType={selectedReportType}
        tickers={tickers}
        selectedReportStatus={selectedReportStatus}
        startDate={startDate}
        endDate={endDate}
        topQuantity={topQuantity}
        createReportOptions={createReportOptions}
        hasReportFilterSelected={hasReportFilterSelected}
        handleSelectedRows={handleSelectedRows}
        handleSelectedTicker={handleSelectedTicker}
        handleStartDate={handleStartDate}
        handleEndDate={handleEndDate}
        handleReportType={handleSelectedReportType}
        handleStatus={handleSelectedReportStatus}
      />
      <PageContent>
        <ExportHistoryList
          reports={reports}
          isLoading={isLoading}
          handleReasonModal={handleReasonModal}
          handleReprocessExport={handleReprocessExport}
          handleSendEmailConfiguration={handleSendEmailConfiguration}
        />
      </PageContent>
      <ConfirmationModal
        message={`${translations.modals.sendAlert.confirmMessage}${getReportTypeTranslations(emailConfig.reportType)}?`}
        show={showModal}
        onClose={handleAlertModal}
        onConfirm={() => handleSendEmailAlert(emailConfig)}
        cancelButtonLabel={translations.modals.sendAlert.cancelButton}
        confirmButtonLabel={translations.modals.sendAlert.confirmButton}
      />
      <ReasonModal
        title={translations.modals.delete.title}
        label={translations.modals.delete.label}
        show={showReasonModal}
        onClose={handleReasonModal}
        onConfirm={(reason) => handleDeleteExport(reason)}
        maxLength={255}
        minLength={40}
      />
    </Page>
  )
}
