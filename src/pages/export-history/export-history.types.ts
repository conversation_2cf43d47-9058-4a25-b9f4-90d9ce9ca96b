import { TOption } from '@mz-codes/design-system'
import { TExportedFile } from 'hooks/get-exported-files/use-get-exported-files.types'

export type TEmailConfig = {
  reportId: string
  tickerId: string
  reportType: string
}

export type TExportHistoryTemplate = {
  selectedRow: TOption
  selectedTicker: TOption
  selectedReportType: TOption
  selectedReportStatus: TOption
  startDate: Date
  endDate: Date
  tickers: TOption[]
  reports: TExportedFile[]
  isLoading: boolean
  showModal: boolean
  showReasonModal: boolean
  emailConfig: TEmailConfig
  topQuantity: TOption[]
  createReportOptions: (reportObject: Record<string, string>) => TOption[]
  handleSelectedRows: (row: TOption) => void
  handleSelectedTicker: (selectedTicker: TOption) => void
  handleSelectedReportType: (reportType: TOption) => void
  handleSelectedReportStatus: (status: TOption) => void
  handleStartDate: (startDate: Date | null) => void
  handleEndDate: (endDate: Date | null) => void
  handleReprocessExport: (shareholderReportId: string) => void
  handleDeleteExport: (shareholderReportId: string) => void
  handleSendEmailConfiguration: (data: TEmailConfig) => void
  handleSendEmailAlert: (emailConfig: TEmailConfig) => void
  hasReportFilterSelected: () => boolean
  handleAlertModal: () => void
  handleReasonModal: (shareholderReportId?: string) => void
}
