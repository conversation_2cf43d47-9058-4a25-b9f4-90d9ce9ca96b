import { Loading, Table } from '@mz-codes/design-system'
import { generateUniqueId, getReportTypeTranslations } from 'utils'
import { DataNotFound } from 'components'
import { format } from 'date-fns'
import { TExportedFile } from 'hooks'
import { ExportHistoryListItem } from '../export-history-list-item'
import { TExportHistoryList } from './export-history-list.types'
import { translations } from '../export-history.translations'

export function ExportHistoryList(props: TExportHistoryList) {
  const { reports, isLoading, handleReasonModal, handleReprocessExport, handleSendEmailConfiguration } = props
  return (
    <Table>
      <Table.THead>
        <Table.TR className="date">
          <Table.TH>{translations.content.generatedDate}</Table.TH>

          <Table.TH>{translations.content.referenceDate}</Table.TH>
          <Table.TH>{translations.content.exportReportType}</Table.TH>
          <Table.TH>{translations.content.ticker}</Table.TH>
          <Table.TH>{translations.content.status}</Table.TH>
          <Table.TH>{translations.content.actions}</Table.TH>
        </Table.TR>
      </Table.THead>
      <Table.TBody>
        {isLoading ? (
          <Table.TR>
            <Table.TD colSpan={6}>
              <Loading />
            </Table.TD>
          </Table.TR>
        ) : !reports || reports.length === 0 ? (
          <Table.TR>
            <Table.TD colSpan={6}>
              <DataNotFound $noBorderBottom>{translations.content.noHistoryFound}</DataNotFound>
            </Table.TD>
          </Table.TR>
        ) : (
          reports.map((item: TExportedFile) => {
            const {
              ticker,
              tickerId,
              referenceDate,
              reportType,
              createdAt,
              reportId,
              fileName,
              status,
              meta,
              reportModel,
              origin,
            } = item

            const formatCreatedAtDate = format(new Date(createdAt), translations.content.dateTimeAmPmFormat)

            return (
              <ExportHistoryListItem
                key={generateUniqueId()}
                createdAt={formatCreatedAtDate}
                referenceDate={referenceDate}
                reportType={getReportTypeTranslations(reportType)}
                ticker={ticker}
                fileName={fileName}
                tickerId={tickerId}
                status={status}
                reportId={reportId}
                meta={meta || {}}
                reportModel={reportModel}
                origin={origin}
                handleReprocessExport={handleReprocessExport}
                handleDeleteExport={handleReasonModal}
                handleSendEmailConfiguration={handleSendEmailConfiguration}
              />
            )
          })
        )}
      </Table.TBody>
    </Table>
  )
}
