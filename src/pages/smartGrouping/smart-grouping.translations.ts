import { TOptions } from 'i18next'
import { i18n } from 'translate'

export const translations = {
  grouped: i18n.t('pageSmartGrouping.grouped'),
  acceptAllGroupingSuggestions: i18n.t('pageSmartGrouping.acceptAllGroupingSuggestions'),
  suggestedGroup: i18n.t('pageSmartGrouping.suggestedGroup'),
  suggestedGrouping: i18n.t('pageSmartGrouping.suggestedGrouping'),
  groupingSuggestion: i18n.t('pageSmartGrouping.groupingSuggestion'),
  manualGrouping: i18n.t('pageSmartGrouping.manualGrouping'),
  acceptSuggestedGroup: i18n.t('pageSmartGrouping.acceptSuggestedGroup'),
  newGrouping: i18n.t('pageSmartGrouping.newGrouping'),
  newGroup: i18n.t('pageSmartGrouping.newGroup'),
  groupModalButton: i18n.t('pageSmartGrouping.groupModalButton'),
  finishGroupingModalTitle: i18n.t('pageSmartGrouping.finishGroupingModalTitle'),
  confirmSuggestedGroupingModalTitle: i18n.t('pageSmartGrouping.confirmSuggestedGroupingModalTitle'),
  confirmSuggestedGroupingModalSubtitle: (params: TOptions & { groupName?: string }) =>
    i18n.t('pageSmartGrouping.confirmSuggestedGroupingModalSubtitle', { ...params }),
  confirmAllGroupingSuggestionsSubtitle: (params: TOptions) =>
    i18n.t('pageSmartGrouping.confirmAllGroupingSuggestionsSubtitle', { ...params }),
  group: i18n.t('pageSmartGrouping.group'),
  searchGroup: i18n.t('pageSmartGrouping.searchGroup'),
  groupedShareholders: i18n.t('pageSmartGrouping.groupedShareholders'),
  ungroupedShareholders: i18n.t('pageSmartGrouping.ungroupedShareholders'),
  manageYourGroupings: i18n.t('pageSmartGrouping.manageYourGroupings'),
  fundOrShareholder: i18n.t('pageSmartGrouping.fundOrShareholder'),
  funds: i18n.t('pageSmartGrouping.funds'),
  groupLater: i18n.t('pageSmartGrouping.groupLater'),
  noFundOrShareholderToGroup: i18n.t('pageSmartGrouping.noFundOrShareholderToGroup'),
  confirm: i18n.t('pageSmartGrouping.confirm'),
  cancel: i18n.t('pageSmartGrouping.cancel'),
  genericErrorTitle: i18n.t('globals.errors.requestFail.title'),
  genericErrorMessage: i18n.t('globals.errors.requestFail.message'),
  loading: i18n.t('globals.loading'),
  noSuggestion: i18n.t('pageSmartGrouping.noSuggestion'),
  cancelButtonLabel: i18n.t('components.confirmModal.cancelButton'),
  confirmButtonLabel: i18n.t('components.confirmModal.confirmButton'),
}
