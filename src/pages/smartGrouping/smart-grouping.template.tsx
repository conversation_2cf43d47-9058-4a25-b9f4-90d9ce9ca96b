import { useCallback } from 'react'
import ReactPaginate from 'react-paginate'
import { Buttons, Dropdown, Icons, ConfirmationModal, Header } from '@mz-codes/design-system'
import { useNavigate } from 'react-router-dom'

import { Page, Card, SelectableData, GoBackHeader, GroupModal, RoundedBorderButton, GroupIcon } from 'components'
import { CardContainer } from './components/CardContainer'
import { DropdownContainer } from './components/DropdownContainer'
import { TSmartGroupingTemplate } from './smart-grouping.types'
import { ActionsButtonsContainer } from './components/ActionsButtonsContainer'
import { VisionButtonsContainer } from './components/VisionButtonsContainer'
import { translations } from './smart-grouping.translations'

export function SmartGroupingTemplate(props: TSmartGroupingTemplate) {
  const {
    loading,
    listLoading,

    smartGroupingHighlights,
    currentGrouping,
    handleCurrentGrouping,

    currentManualGroupingVision,
    handleCurrentManualGroupingVision,

    actionsButtons,
    suggestedGroups,
    currentSuggestedGroup,
    handleCurrentSuggestedGroup,
    handleGroupLater,

    shareholderList,

    handleSelectRow,
    handleSelectAllRows,
    handlePaginationNavigate,
    pageCount,
    currentPage,

    confirmationModal,
    handleConfirmationModal,
    handleConfirmationModalActionType,

    groupModal,
    handleGroupModal,
    groupList,
    handleManualGrouping,
    handleCreateGroup,
    setConfirmationModalActionType,
  } = props
  const navigate = useNavigate()

  const handleNavigate = useCallback(
    (path: string) => {
      navigate(path)
    },
    [navigate]
  )

  return (
    <Page>
      <GoBackHeader
        title={translations.manageYourGroupings}
        handleNavigate={() => handleNavigate('/shareholders/summary')}
      />
      <CardContainer>
        <Card
          loading={loading}
          title={translations.groupingSuggestion}
          subtitle={smartGroupingHighlights.totalSuggested}
          isActive={currentGrouping === 'suggestion'}
          handleClick={() => handleCurrentGrouping('suggestion')}
        />
        <Card
          loading={loading}
          title={translations.manualGrouping}
          subtitle={smartGroupingHighlights.totalManual}
          isActive={currentGrouping === 'manual'}
          handleClick={() => handleCurrentGrouping('manual')}
        />
      </CardContainer>

      <DropdownContainer>
        {currentGrouping === 'suggestion' && (
          <Header.Item style={{ paddingLeft: '20px' }}>
            <Header.Label>{translations.suggestedGroup}</Header.Label>
            <Dropdown
              height={450}
              options={suggestedGroups}
              selected={currentSuggestedGroup}
              placeholder={translations.loading}
              onChange={handleCurrentSuggestedGroup}
              disabled={loading || !currentSuggestedGroup?.value}
            />
          </Header.Item>
        )}

        {currentGrouping === 'manual' && (
          <VisionButtonsContainer>
            <RoundedBorderButton
              $active={currentManualGroupingVision === 'fund'}
              onClick={() => handleCurrentManualGroupingVision('fund')}
            >
              {translations.funds}
            </RoundedBorderButton>
            <RoundedBorderButton
              $active={currentManualGroupingVision === 'groupLater'}
              onClick={() => handleCurrentManualGroupingVision('groupLater')}
            >
              {translations.groupLater}
            </RoundedBorderButton>
          </VisionButtonsContainer>
        )}

        <ActionsButtonsContainer>
          {currentGrouping === 'suggestion' && (
            <>
              <Buttons.Icon
                text={translations.acceptAllGroupingSuggestions}
                onClick={() => {
                  setConfirmationModalActionType('acceptAllGroupingSuggestions')
                  handleConfirmationModal()
                }}
                disabled={smartGroupingHighlights.totalSuggested === '0'}
              >
                <Icons.Check />
              </Buttons.Icon>
              <Buttons.Icon
                disabled={!actionsButtons}
                text={translations.acceptSuggestedGroup}
                onClick={() => {
                  setConfirmationModalActionType('acceptSuggestedGroup')
                  handleConfirmationModal()
                }}
              >
                <Icons.Check />
              </Buttons.Icon>
            </>
          )}

          <Buttons.Icon disabled={!actionsButtons} text={translations.newGrouping} onClick={handleGroupModal}>
            <GroupIcon />
          </Buttons.Icon>
          {currentGrouping === 'manual' && currentManualGroupingVision === 'fund' && (
            <Buttons.Icon disabled={!actionsButtons} text={translations.groupLater} onClick={handleGroupLater}>
              <Icons.FolderOpen />
            </Buttons.Icon>
          )}
        </ActionsButtonsContainer>
      </DropdownContainer>

      <SelectableData
        loading={listLoading || loading}
        data={shareholderList}
        noDataLabel={translations.noFundOrShareholderToGroup}
        handleRows={handleSelectRow}
        handleAllRows={handleSelectAllRows}
      />

      {currentGrouping === 'manual' && !listLoading && pageCount > 1 && (
        <ReactPaginate
          breakLabel="..."
          previousLabel={<Icons.AngleDoubleLeft />}
          nextLabel={<Icons.AngleDoubleRight />}
          onPageChange={handlePaginationNavigate}
          pageRangeDisplayed={5}
          pageCount={pageCount}
          forcePage={currentPage}
          disableInitialCallback={listLoading}
          pageClassName="page-item"
          pageLinkClassName="page-link"
          previousClassName="page-item"
          previousLinkClassName="page-link"
          nextClassName="page-item"
          nextLinkClassName="page-link"
          breakClassName="page-item"
          breakLinkClassName="page-link"
          containerClassName="pagination"
          activeClassName="active"
        />
      )}

      <ConfirmationModal
        show={confirmationModal}
        onClose={handleConfirmationModal}
        onConfirm={handleConfirmationModalActionType?.handleConfirm}
        message={handleConfirmationModalActionType?.message}
        cancelButtonLabel={translations.cancelButtonLabel}
        confirmButtonLabel={translations.confirmButtonLabel}
      />

      <GroupModal
        title={translations.finishGroupingModalTitle}
        visibility={groupModal}
        onClose={handleGroupModal}
        onConfirm={handleManualGrouping}
        onCreateGroup={handleCreateGroup}
        options={groupList}
      />
    </Page>
  )
}
