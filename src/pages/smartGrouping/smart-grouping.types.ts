import { TOption } from '@mz-codes/design-system'
import { TSmartGroupingHighlightsResponseData } from 'hooks'
import { Dispatch, SetStateAction } from 'react'
import { TConfirmationModalActionType } from './smart-grouping.component'

export type TGroupingSuggestionData = { label: string; value: string; isSelected: boolean }

export type TSmartGroupingTemplate = {
  loading: boolean
  listLoading: boolean

  smartGroupingHighlights: TSmartGroupingHighlightsResponseData
  currentGrouping: 'suggestion' | 'manual'
  handleCurrentGrouping(groupingType: 'suggestion' | 'manual'): void

  currentManualGroupingVision: 'groupLater' | 'fund'
  handleCurrentManualGroupingVision(vision: 'groupLater' | 'fund'): void

  actionsButtons: boolean
  suggestedGroups: Array<TOption>
  currentSuggestedGroup?: TOption
  handleCurrentSuggestedGroup(option: TOption): void
  handleGroupLater(): void

  shareholderList: Array<TGroupingSuggestionData>
  selectedShareholders: Array<TGroupingSuggestionData>

  handleSelectRow(event: React.ChangeEvent<HTMLInputElement>): void
  handleSelectAllRows(): void
  handlePaginationNavigate(event: { selected: number }): void
  pageCount: number
  currentPage: number

  confirmationModal: boolean
  handleConfirmationModal(): void
  setConfirmationModalActionType: Dispatch<SetStateAction<TConfirmationModalActionType>>
  handleConfirmationModalActionType: {
    message: string
    handleConfirm(): Promise<void>
  }

  groupModal: boolean
  handleGroupModal(): void
  groupList?: Array<TOption>
  handleManualGrouping(shareholderGroupInfo: TOption<string>): Promise<void>
  handleCreateGroup(shareholderGroupName: string): Promise<void>
}
