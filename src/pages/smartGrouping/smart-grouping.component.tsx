import { useCallback, useEffect, useState } from 'react'
import { TOption, useToast } from '@mz-codes/design-system'
import {
  getSmartGroupingHighlights,
  getAlbertSuggestions,
  getAlbertSuggestionsData,
  putAlbertSuggestionsAcceptGrouping,
  putManualSuggestionsManualGrouping,
  TSmartGroupingHighlightsResponseData,
  putManualSuggestionsGroupLater,
  getManualSuggestionsPaginatedFundsList,
  getGroups,
  postCreateGroup,
  postGroupAllAlbertSuggestions,
  getManualSuggestionMaxPage,
  TGetMaxPage,
} from 'hooks'
import { getCompany } from 'globals/storages/locals'
import { TGroupingSuggestionData } from './smart-grouping.types'
import { SmartGroupingTemplate } from './smart-grouping.template'
import { translations } from './smart-grouping.translations'

const highlightsPlaceholder = {
  totalManual: '-',
  totalSuggested: '-',
  totalUngrouped: '-',
}

export type TConfirmationModalActionType = 'acceptSuggestedGroup' | 'newGrouping' | 'acceptAllGroupingSuggestions'
const itemsPerPage = 10

export function SmartGrouping() {
  const { createToast } = useToast()
  const [loading, setLoading] = useState<boolean>(false)
  const [listLoading, setListLoading] = useState<boolean>(false)

  const [smartGroupingHighlights, setSmartGroupingHighlights] =
    useState<TSmartGroupingHighlightsResponseData>(highlightsPlaceholder)
  const [currentGrouping, setCurrentGrouping] = useState<'suggestion' | 'manual'>('suggestion')

  const [currentManualGroupingVision, setCurrentManualGroupingVision] = useState<'fund' | 'groupLater'>('fund')
  const [suggestedGroups, setSuggestedGroups] = useState<Array<TOption>>([])
  const [currentSuggestedGroup, setCurrentSuggestedGroup] = useState<TOption>({} as TOption)
  const [shareholderList, setShareholderList] = useState<Array<TGroupingSuggestionData>>([])
  const [selectedGroupData, setSelectedGroupData] = useState<{
    shareholderGroupName: string
    shareholderGroupId: string
  }>()
  const [currentPage, setCurrentPage] = useState(0)
  const [maxPage, setMaxPage] = useState<number>(0)

  const [confirmationModal, setConfirmationModal] = useState<boolean>(false)
  const [confirmationModalActionType, setConfirmationModalActionType] =
    useState<TConfirmationModalActionType>('acceptSuggestedGroup')
  const [groupModal, setGroupModal] = useState<boolean>(false)
  const [groupList, setGroupList] = useState<Array<TOption>>([])

  const selectedShareholders = shareholderList.filter((element) => element.isSelected === true)
  const hasSelectedItems = !!shareholderList.find((element) => element.isSelected === true)

  const company = getCompany()

  const genericErrorToast = useCallback(() => {
    return createToast({
      title: translations.genericErrorTitle,
      description: translations.genericErrorMessage,
      type: 'error',
    })
  }, [createToast])

  const handleGetSmartGroupingHighlights = useCallback(async () => {
    try {
      const response = await getSmartGroupingHighlights({ companyId: company.id })
      setSmartGroupingHighlights(response.data)
      return response
    } catch (err: unknown) {
      setSmartGroupingHighlights(highlightsPlaceholder)
      return genericErrorToast()
    }
  }, [company?.id, genericErrorToast])

  const handleGetGroupSuggestions = useCallback(async () => {
    setLoading(true)
    try {
      const highlights = await handleGetSmartGroupingHighlights()
      if (!highlights || highlights.data.totalSuggested === '0') {
        setShareholderList([])
        setSuggestedGroups([])
        setCurrentSuggestedGroup({ label: translations.noSuggestion, value: '' })
        return
      }

      const suggestions = await getAlbertSuggestions({ companyId: company.id })

      const formatSuggestions = suggestions.data.map<TOption>((shareholderGroup) => ({
        label: `${shareholderGroup.albertShareholderGroupName}`,
        value: shareholderGroup.albertShareholderGroupId,
      }))

      setSuggestedGroups(formatSuggestions)
      setCurrentSuggestedGroup(formatSuggestions[0])
    } catch (err: unknown) {
      genericErrorToast()
    } finally {
      setLoading(false)
    }
  }, [company?.id, genericErrorToast, handleGetSmartGroupingHighlights])

  const handleGetManualSuggestionMaxPage = useCallback(
    async (props: TGetMaxPage) => {
      const { limit, archived, companyId } = props
      try {
        const res = await getManualSuggestionMaxPage({ companyId, limit, archived })
        if (!res.data.maxPage) {
          setShareholderList([])
          setMaxPage(0)
        }
        return res.data
      } catch (err: unknown) {
        return genericErrorToast()
      }
    },
    [genericErrorToast]
  )

  const handleGetShareholderList = useCallback(async () => {
    try {
      setListLoading(true)
      const highlights = await handleGetSmartGroupingHighlights()
      if (highlights?.data?.totalSuggested === '0') {
        setShareholderList([])
        setMaxPage(0)
        return
      }

      const albertSuggestions = await getAlbertSuggestionsData({
        companyId: company.id,
        shareholderGroupId: currentSuggestedGroup.value as string,
      })

      const formatShareholderList = albertSuggestions.data.map<TGroupingSuggestionData>((shareholder) => ({
        label: shareholder.shareholderName,
        value: shareholder.shareholderId,
        isSelected: false,
      }))

      setShareholderList(formatShareholderList)
    } catch (err: unknown) {
      genericErrorToast()
      setShareholderList([])
      setMaxPage(0)
    } finally {
      setListLoading(false)
    }
  }, [genericErrorToast, company?.id, currentSuggestedGroup?.value, handleGetSmartGroupingHighlights])

  const handleGetManualSuggestionsPaginatedFundsList = useCallback(
    async (page?: number, archived?: boolean) => {
      try {
        setListLoading(true)
        const hasPage = await handleGetManualSuggestionMaxPage({
          companyId: company.id,
          limit: itemsPerPage,
          archived: !!archived,
        })

        if (!hasPage || hasPage.maxPage === 0) return

        const paginatedShareholderList = await getManualSuggestionsPaginatedFundsList({
          companyId: company.id,
          limit: itemsPerPage,
          page: page || 1,
          archived: archived || false,
        })

        const formatShareholderList = paginatedShareholderList.data.shareholders.map<TGroupingSuggestionData>(
          (shareholder) => ({
            label: shareholder.shareholderName,
            value: shareholder.shareholderId,
            isSelected: false,
          })
        )

        setShareholderList(formatShareholderList)
        setMaxPage(hasPage.maxPage)
      } catch (err: unknown) {
        genericErrorToast()
        setShareholderList([])
        setMaxPage(0)
      } finally {
        setListLoading(false)
      }
    },
    [company.id, genericErrorToast, handleGetManualSuggestionMaxPage]
  )

  const handleAcceptSuggestedGrouping = async () => {
    try {
      const shareholdersIdsPayload = selectedShareholders.map((value) => value.value)

      await putAlbertSuggestionsAcceptGrouping({
        companyId: company.id,
        shareholderGroupId: currentSuggestedGroup.value as string,
        shareholdersIds: shareholdersIdsPayload,
      })

      if (shareholderList.length === shareholdersIdsPayload.length) {
        await handleGetGroupSuggestions()
        return
      }

      await handleGetShareholderList()
    } catch (err: unknown) {
      genericErrorToast()
    } finally {
      setConfirmationModal(false)
    }
  }

  const handleSelectGroupManually = async (shareholderGroupInfo: TOption<string>) => {
    setSelectedGroupData({
      shareholderGroupName: shareholderGroupInfo.label,
      shareholderGroupId: shareholderGroupInfo.value,
    })
    setConfirmationModalActionType('newGrouping')
    handleConfirmationModal()
  }

  const handleManualGrouping = async (shareholderGroupId: string) => {
    handleGroupModal()
    try {
      const shareholdersIdsPayload = selectedShareholders.map((value) => value.value)

      await putManualSuggestionsManualGrouping({
        companyId: company.id,
        shareholderGroupId,
        shareholdersIds: shareholdersIdsPayload,
      })

      const highlights = await handleGetSmartGroupingHighlights()
      if (highlights?.data?.totalUngrouped === '0') {
        setShareholderList([])
        setMaxPage(0)
        return
      }
      setConfirmationModal(false)

      if (currentGrouping === 'suggestion') {
        await handleGetGroupSuggestions()
        return
      }

      const isArchived = !!(currentManualGroupingVision === 'groupLater')
      await handleGetManualSuggestionsPaginatedFundsList(currentPage, isArchived)
    } catch (err: unknown) {
      genericErrorToast()
    } finally {
      setConfirmationModal(false)
      setSelectedGroupData({ shareholderGroupId: '', shareholderGroupName: '' })
    }
  }

  const handleCreateGroup = async (shareholderGroupName: string) => {
    try {
      const response = await postCreateGroup({
        companyId: company.id,
        shareholderGroupName,
      })

      const { shareholderGroupId } = response.data

      setSelectedGroupData({ shareholderGroupName, shareholderGroupId })

      setConfirmationModalActionType('newGrouping')
      setConfirmationModal(true)
    } catch (err: unknown) {
      genericErrorToast()
    }
  }

  const handleGroupLater = async () => {
    try {
      const shareholdersIdsPayload = selectedShareholders.map((value) => value.value)

      await putManualSuggestionsGroupLater({
        companyId: company.id,
        shareholdersIds: shareholdersIdsPayload,
      })

      const isArchived = !!(currentManualGroupingVision === 'groupLater')

      handleGetManualSuggestionsPaginatedFundsList(currentPage, isArchived)
    } catch (err: unknown) {
      genericErrorToast()
    } finally {
      setConfirmationModal(false)
    }
  }

  const handleGetGroups = async () => {
    setSelectedGroupData({ shareholderGroupId: '', shareholderGroupName: '' })

    try {
      const shareholderGroups = await getGroups(company.id)

      const formatShareholderGroups = shareholderGroups.data.data.map<TOption>((shareholderGroup) => ({
        label: shareholderGroup.name,
        value: shareholderGroup.shareholderGroupId,
      }))

      setGroupList(formatShareholderGroups)
    } catch (err: unknown) {
      genericErrorToast()
    }
  }

  const handleCurrentGrouping = (groupingType: 'suggestion' | 'manual') => {
    if (currentGrouping === groupingType) return
    setCurrentGrouping(groupingType)
    if (currentGrouping === 'suggestion') setCurrentPage(0)
  }

  const handleCurrentSuggestedGroup = (option: TOption) => {
    if (currentSuggestedGroup === option) return
    setCurrentSuggestedGroup(option)
  }

  const handleCurrentManualGroupingVision = (vision: 'groupLater' | 'fund') => {
    if (currentManualGroupingVision === vision) return
    setCurrentManualGroupingVision(vision)
    setCurrentPage(0)
  }

  const handleSelectRow = (event: React.ChangeEvent<HTMLInputElement>) => {
    const dataClone: Array<TGroupingSuggestionData> = JSON.parse(JSON.stringify(shareholderList))
    const rowIndex = dataClone.findIndex((row) => row.value === event.target.value)
    dataClone[rowIndex] = { ...dataClone[rowIndex], isSelected: !dataClone[rowIndex].isSelected }
    setShareholderList(dataClone)
  }

  const handleSelectAllRows = () => {
    const dataClone: Array<TGroupingSuggestionData> = JSON.parse(JSON.stringify(shareholderList))
    const hasUnselectedRows = dataClone.find((row) => row.isSelected === false)

    const transformedData = dataClone.map((value) => ({ ...value, isSelected: !!hasUnselectedRows }))
    return setShareholderList(transformedData)
  }

  const handlePaginationNavigate = (selectedItem: { selected: number }) => {
    if (listLoading) return

    setCurrentPage(selectedItem.selected)
  }

  const handleConfirmationModal = () => {
    setConfirmationModal(!confirmationModal)
  }

  const handlePostAllGroupingSuggestions = async (companyId: string) => {
    try {
      await postGroupAllAlbertSuggestions({ companyId })

      await handleGetGroupSuggestions()
    } catch (err: unknown) {
      genericErrorToast()
    } finally {
      handleConfirmationModal()
    }
  }

  const handleConfirmationModalActionType = (actionType: TConfirmationModalActionType) => {
    const genericModalConfirmationMessage = (groupName?: string) =>
      translations.confirmSuggestedGroupingModalSubtitle({
        count: selectedShareholders.length,
        groupName: groupName || 'error',
      })

    const modalHandler = {
      acceptSuggestedGroup: {
        message: genericModalConfirmationMessage(currentSuggestedGroup?.label),
        handleConfirm: handleAcceptSuggestedGrouping,
      },
      newGrouping: {
        message: genericModalConfirmationMessage(selectedGroupData?.shareholderGroupName),
        handleConfirm: () => handleManualGrouping(selectedGroupData?.shareholderGroupId as string),
      },
      acceptAllGroupingSuggestions: {
        message: translations.confirmAllGroupingSuggestionsSubtitle({
          count: Number(smartGroupingHighlights.totalSuggested),
        }),
        handleConfirm: () => handlePostAllGroupingSuggestions(company.id),
      },
    }
    return modalHandler[actionType]
  }

  const handleGroupModal = () => {
    if (!groupModal) handleGetGroups()

    setGroupModal(!groupModal)
  }

  const handleGetInfo = useCallback(async () => {
    try {
      await handleGetGroupSuggestions()
    } catch (error) {
      genericErrorToast()
    }
  }, [handleGetGroupSuggestions, genericErrorToast])

  useEffect(() => {
    handleGetInfo()
  }, [handleGetInfo])

  useEffect(() => {
    setShareholderList([])
    if (currentGrouping === 'suggestion' && currentSuggestedGroup?.value) {
      setCurrentManualGroupingVision('fund')
      handleGetShareholderList()
      return
    }

    if (currentGrouping === 'manual') {
      const isArchived = currentManualGroupingVision === 'groupLater'
      const convertIndexToPage = currentPage + 1
      handleGetManualSuggestionsPaginatedFundsList(convertIndexToPage, isArchived)
    }
  }, [
    currentPage,
    currentSuggestedGroup,
    currentManualGroupingVision,
    currentGrouping,
    handleGetManualSuggestionsPaginatedFundsList,
    handleGetShareholderList,
  ])

  return (
    <SmartGroupingTemplate
      loading={loading}
      listLoading={listLoading}
      smartGroupingHighlights={smartGroupingHighlights}
      currentGrouping={currentGrouping}
      handleCurrentGrouping={handleCurrentGrouping}
      currentManualGroupingVision={currentManualGroupingVision}
      handleCurrentManualGroupingVision={handleCurrentManualGroupingVision}
      actionsButtons={hasSelectedItems}
      suggestedGroups={suggestedGroups}
      currentSuggestedGroup={currentSuggestedGroup}
      handleCurrentSuggestedGroup={handleCurrentSuggestedGroup}
      handleGroupLater={handleGroupLater}
      shareholderList={shareholderList}
      selectedShareholders={selectedShareholders}
      handleSelectRow={handleSelectRow}
      handleSelectAllRows={handleSelectAllRows}
      handlePaginationNavigate={handlePaginationNavigate}
      pageCount={maxPage}
      currentPage={currentPage}
      confirmationModal={confirmationModal}
      handleConfirmationModal={handleConfirmationModal}
      setConfirmationModalActionType={setConfirmationModalActionType}
      handleConfirmationModalActionType={handleConfirmationModalActionType(confirmationModalActionType)}
      groupModal={groupModal}
      handleGroupModal={handleGroupModal}
      groupList={groupList}
      handleManualGrouping={handleSelectGroupManually}
      handleCreateGroup={handleCreateGroup}
    />
  )
}
