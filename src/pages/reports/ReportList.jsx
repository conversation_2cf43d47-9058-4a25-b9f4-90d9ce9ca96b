import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'

import { Collapse } from 'react-collapse'
import { PATH } from 'consts'

import ReactTooltip from 'react-tooltip'

import { i18n } from 'translate'

import { utils, defineTheme, generateUniqueId } from 'utils'
import ExpandedGroupChildItem from './ExpandedGroupChildItem'

class ReportList extends Component {
  reportType = {
    TopHolders: 1,
    TopBuyers: 2,
    TopSellers: 3,
    ZeroedPositions: 4,
    NewHolders: 5,
    BaseMovimented: 6,
    MovimentTopHolders: 7,
  }

  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      orderedBy: 'StocksLastDateValue',
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  onSortClickHandler = () => {
    this.props.onSortClickHandler()
  }

  renderClassName = () => {
    let className = `header-title ${this.state.orderedBy.toLowerCase()}`
    if (this.props.currentOrder === -1) {
      className += '-desc'
    } else {
      className += '-asc'
    }
    return className
  }

  render() {
    const albert = 88
    const group = 99
    const simple = 1
    return (
      <div className="base-content">
        <ul
          className={
            this.props.vision === 1 && this.props.type === 'simple'
              ? defineTheme('base-list shareholder-base-list shareholder-report simple simple2')
              : this.props.vision === 1
                ? defineTheme('base-list shareholder-base-list shareholder-report simple')
                : this.props.type === 'simple'
                  ? defineTheme('base-list shareholder-base-list shareholder-report simple2')
                  : defineTheme('base-list shareholder-base-list shareholder-report')
          }
        >
          {this.props.vision !== 8 ? (
            <li className={this.renderClassName()}>
              <span className="open-drill-dawn" />
              <span className="name">{i18n.t('name')}</span>
              <span className="shareholder-type">{i18n.t('type')}</span>
              {this.props.type === 'simple' ? <span className="group">{i18n.t('shareholderGroupTitle')}</span> : null}
              <span className="country">{i18n.t('country')}</span>
              {this.props.vision === 1 ? (
                <span className="volume-last-value align-right">
                  {this.props.visualization === 1 && i18n.t('volume')}
                  {this.props.visualization === 2 && i18n.t('reports.value')}
                </span>
              ) : (
                <span className="volume-last-value align-right">
                  {this.props.visualization === 1 && i18n.t('startVolume')}
                  {this.props.visualization === 2 && i18n.t('reports.startValue')}
                </span>
              )}

              {this.props.vision === 1 ? (
                <span className="volume-last-value align-right">{i18n.t('representativity')}</span>
              ) : null}
              {this.props.vision === 1 ? null : (
                <span className="value align-right">
                  {this.props.visualization === 1 && i18n.t('lastVolume')}
                  {this.props.visualization === 2 && i18n.t('reports.finalValue')}
                </span>
              )}

              {this.props.vision === 1 ? null : this.props.vision === 6 ? (
                <span className="change">
                  <span
                    tabIndex={0}
                    role="button"
                    onKeyDown={() => this.onSortClickHandler()}
                    onClick={() => this.onSortClickHandler()}
                  >
                    {i18n.t('change')}
                    <i className="order-by" />
                  </span>
                </span>
              ) : (
                <span className="change align-right">{i18n.t('change')}</span>
              )}
            </li>
          ) : (
            <li className={this.renderClassName()}>
              <span className="name">{i18n.t('name')}</span>
              <span className="volume-last-value aligh-right">{i18n.t('volumeMin')}</span>
              <span className="volume-last-value aligh-right">{i18n.t('volumeMax')}</span>
              <span className="value aligh-right">{i18n.t('volumeAvg')}</span>
            </li>
          )}

          {this.props.isLoading ? (
            <li className="list-loading">
              <div className="lds-dual-ring">
                <div />
              </div>
            </li>
          ) : this.props.currentBase.length === 0 ? (
            <li className="no-results">{i18n.t('noResults')}</li>
          ) : this.props.vision !== 8 ? (
            <>
              {this.props.currentBase.map((item, index) => (
                <li className="report-list-item" key={generateUniqueId()}>
                  <span className="open-drill-dawn">
                    {item.shareholderGroupId == null ? (
                      <i
                        data-tip={i18n.t('groupLabel')}
                        data-for="report-tooltip"
                        onClick={() => this.props.onOpenAgglutinationModal(item)}
                        onKeyDown={() => this.props.onOpenAgglutinationModal(item)}
                        role="button"
                        tabIndex={0}
                        aria-label="Open Modal"
                      />
                    ) : item.isExpandable ? (
                      this.props.shareholderGroupIsLoading.has(item.shareholderGroupId) ? (
                        <div className="lds-dual-ring center-load-icon">
                          <div />
                        </div>
                      ) : (
                        <span
                          className={
                            this.props.shareholderGroupExpanded.has(item.shareholderGroupId) ? 'close' : 'open'
                          }
                          onClick={() => this.props.expandGroups(item, index)}
                          onKeyDown={() => this.props.expandGroups(item, index)}
                          tabIndex={0}
                          role="button"
                          aria-label="Expand Group"
                        />
                      )
                    ) : (
                      ''
                    )}
                  </span>
                  <span
                    className={`${this.props.vision === 1 ? 'name c4' : 'name'}`}
                    data-tip={item.name}
                    data-for="report-tooltip"
                  >
                    {item.shareholderType === albert ? (
                      <Link to={`${PATH}/ownership/${item.shareholderGroupId}/albert/overview`}>{item.name}</Link>
                    ) : item.shareholderType === group ? (
                      <Link to={`${PATH}/ownership/${item.shareholderGroupId}/grouped/overview`}>{item.name}</Link>
                    ) : (
                      <Link to={`${PATH}/ownership/${item.shareholderId}/simple/overview`}>{item.name}</Link>
                    )}
                  </span>

                  <span className="shareholder-type">
                    {item.shareholderType === albert
                      ? 'Beta'
                      : item.shareholderType === group
                        ? i18n.t('ownership.group')
                        : item.shareholderType === simple
                          ? i18n.t('fund')
                          : i18n.t('private')}
                  </span>

                  {this.props.type === 'simple' ? (
                    <span className="group">
                      {item.shareholderGroupName ? (
                        <Link to={`${PATH}/ownership/${item.shareholderGroupId}/grouped/overview`}>
                          {item.shareholderGroupName}
                        </Link>
                      ) : (
                        <i
                          data-tip={i18n.t('groupLabel')}
                          data-for="report-tooltip"
                          onClick={() => this.props.onOpenAgglutinationModal(item)}
                          onKeyDown={() => this.props.onOpenAgglutinationModal(item)}
                          tabIndex={0}
                          role="button"
                          aria-label="Open Modal"
                        />
                      )}
                    </span>
                  ) : null}

                  <span className="country">
                    {item.shareholderType === albert ? '--' : item.shareholderType === group ? '--' : item.countryCode}
                  </span>

                  {this.props.vision === simple ? (
                    <span className="volume-last-value align-right">
                      {this.props.visualization === 1 && this.formatted(item.stockAmountEdited)}
                      {this.props.visualization === 2 &&
                        `R$ ${utils.formatRepresentativeness(item.totalAmount, this.state.idiom)}`}
                    </span>
                  ) : (
                    <span className="volume-last-value align-right">
                      {this.props.visualization === 1 && this.formatted(item.stockAmountEditedStart)}
                      {this.props.visualization === 2 &&
                        `R$ ${utils.formatRepresentativeness(item.totalAmountStart, this.state.idiom)}`}
                    </span>
                  )}

                  {this.props.vision === simple ? (
                    <span className="volume-last-value align-right">
                      {this.formatted((+item.representativity).toFixed(2), '', '%')}
                    </span>
                  ) : null}

                  {this.props.vision === simple ? null : (
                    <span className="value align-right">
                      {this.props.visualization === 1 && this.formatted(item.stockAmountEditedEnd)}
                      {this.props.visualization === 2 &&
                        `R$ ${utils.formatRepresentativeness(item.totalAmountEnd, this.state.idiom)}`}
                    </span>
                  )}

                  {this.props.vision === simple ? null : (
                    <span
                      className={`${
                        item.difference > 0 ? 'change up' : item.difference < 0 ? 'change down' : 'change'
                      } align-right`}
                    >
                      {this.props.visualization === 1 && this.formatted(item.difference)}
                      {this.props.visualization === 2 &&
                        `R$ ${utils.formatRepresentativeness(item.variationAmount, this.state.idiom)}`}
                    </span>
                  )}

                  {this.props.shareholderGroupChilds.has(item.shareholderGroupId) && (
                    <Collapse
                      isOpened={this.props.shareholderGroupChilds.has(item.shareholderGroupId)}
                      theme={
                        this.props.reportType !== this.reportType.TopHolders
                          ? { collapse: 'ReactCollapse--collapse type-2' }
                          : { collapse: 'ReactCollapse--collapse' }
                      }
                    >
                      <ul>
                        {this.props.shareholderGroupChilds
                          .get(item.shareholderGroupId)
                          .map((subItem, subIndex) =>
                            this.props.reportType === this.reportType.TopHolders ? (
                              <ExpandedGroupChildItem
                                shareholderType={subItem.shareholderType}
                                index={subIndex}
                                displayName={subItem.displayName}
                                shareholderId={subItem.shareholderId}
                                documentType={subItem.documentType}
                                countryCode={subItem.countryCode}
                                stockAmountEdited={this.formatted(subItem.stockAmountEdited)}
                                stockPercent={subItem.stockPercent}
                                key={subItem.shareholderId}
                              />
                            ) : (
                              <ExpandedGroupChildItem
                                shareholderType={subItem.shareholderType}
                                index={subIndex}
                                displayName={subItem.displayName}
                                shareholderId={subItem.shareholderId}
                                documentType={subItem.documentType}
                                country={subItem.country}
                                stockAmountStart={this.formatted(subItem.stockAmountStart)}
                                stockAmountEnd={this.formatted(subItem.stockAmountEnd)}
                                difference={subItem.difference}
                                type="2"
                                key={subItem.shareholderId}
                              />
                            )
                          )}
                      </ul>
                    </Collapse>
                  )}
                </li>
              ))}
              <ReactTooltip place="top" delayShow={500} id="report-tooltip" />
            </>
          ) : (
            this.props.currentBase.map((item) => (
              <li key={item.shareholderId}>
                <span className="name">
                  <Link to={`${PATH}/ownership/${item.shareholderId}/simple`} target="_blank">
                    {item.name}
                  </Link>
                </span>
                <span className="volume-last-value align-right">{this.formatted(item.minimum)}</span>
                <span className="volume-last-value align-right">{this.formatted(item.maximum)}</span>
                <span className="value align-right">{this.formatted(item.average)}</span>
              </li>
            ))
          )}
        </ul>
      </div>
    )
  }
}

export default ReportList
