import React, { Component } from 'react'
import { Buttons, Dropdown, Inputs, Header, NewDatepicker, theme, Tooltip, ALIGNMENTS } from '@mz-codes/design-system'
import { i18n } from 'translate'

import { baseViewReports, groupedOptions, irmReportOptions, reportsVisualization } from 'utils'

export default class ReportHeader extends Component {
  render() {
    const {
      tickers,
      disableLoadButton,
      firstDate = today,
      secondDate = today,
      datesAvailable,
      reportType,
      reportQuantity,
      selectedTopQuantity,
      currentViewType,
      currentTicker,
      currentShareholderType,
      shareholderTypes,
      visualizationType,
      currentGroupedType,
      onChangeStockType,
      onChangeTopQuantity,
      onChangeGroupedType,
      onChangeShareholderType,
      onChangeReportType,
      onChangeFirstDate,
      onChangeSecondDate,
      onChangeViewTypeGroup,
      onExport,
      onLoad,
      onChangeVisualizationType,
      zeroedCheckbox,
      handleZeroedCheckbox,
    } = this.props

    const today = new Date()
    const disableHeaderOptions = !tickers || !datesAvailable.length

    const handleStartDateAvailableDatesLimit = () => {
      return datesAvailable.filter((date) => date <= secondDate)
    }

    const handleEndDateAvailableDatesLimit = () => {
      return datesAvailable.filter((date) => date >= firstDate)
    }

    return (
      <Header style={{ flexWrap: 'wrap' }}>
        <Header.Content $wrapContent>
          <Header.Item>
            <Header.Label>{i18n.t('stockType')}</Header.Label>
            <Dropdown
              disabled={disableHeaderOptions}
              options={tickers}
              minWidth={150}
              onChange={onChangeStockType}
              selected={currentTicker}
            />
          </Header.Item>

          <Header.Item>
            <Header.Label>{i18n.t('reports.visualizationLabel')}</Header.Label>
            <Dropdown
              disabled={disableHeaderOptions}
              minWidth={150}
              options={reportsVisualization}
              onChange={onChangeVisualizationType}
              selected={visualizationType}
            />
          </Header.Item>

          <Header.Item>
            <Header.Label>{i18n.t('reportType')}</Header.Label>
            <Dropdown
              disabled={disableHeaderOptions}
              minWidth={150}
              options={irmReportOptions}
              onChange={onChangeReportType}
              selected={reportType}
            />
          </Header.Item>

          {reportType && reportType.value !== 8 && (
            <Header.Item>
              <Header.Label>{i18n.t('viewType')}</Header.Label>
              <Dropdown
                disabled={disableHeaderOptions}
                minWidth={150}
                options={baseViewReports}
                onChange={onChangeViewTypeGroup}
                selected={currentViewType}
              />
            </Header.Item>
          )}

          <Header.Item>
            <Header.Label>{i18n.t('groupedLabel')}</Header.Label>
            <Dropdown
              disabled={disableHeaderOptions}
              minWidth={150}
              options={groupedOptions}
              onChange={onChangeGroupedType}
              selected={currentGroupedType}
            />
          </Header.Item>

          {reportType && reportType.value !== 8 && (
            <Header.Item>
              <Header.Label>{i18n.t('quantity')}</Header.Label>
              <Dropdown
                disabled={disableHeaderOptions}
                minWidth={150}
                options={reportQuantity}
                onChange={onChangeTopQuantity}
                selected={selectedTopQuantity}
              />
            </Header.Item>
          )}

          {reportType && reportType.value === 1 && (
            <Header.Item>
              <Header.Label>{i18n.t('globals.endDate')}</Header.Label>
              <NewDatepicker
                lang={i18n.language}
                blocked={disableHeaderOptions}
                selected={secondDate}
                onChange={onChangeSecondDate}
                maxDate={datesAvailable?.[datesAvailable.length - 1]}
                minDate={datesAvailable?.[0]}
                availableDates={datesAvailable}
                hint={i18n.t('globals.referenceDate')}
              />
            </Header.Item>
          )}

          {reportType && reportType.value !== 1 && (
            <>
              <Header.Item>
                <Header.Label>{i18n.t('globals.startDate')}</Header.Label>
                <NewDatepicker
                  lang={i18n.language}
                  blocked={disableHeaderOptions}
                  selected={firstDate}
                  onChange={onChangeFirstDate}
                  maxDate={handleStartDateAvailableDatesLimit()?.[handleStartDateAvailableDatesLimit().length - 1]}
                  minDate={handleStartDateAvailableDatesLimit()?.[0]}
                  hint={i18n.t('startDate')}
                  availableDates={datesAvailable}
                />
              </Header.Item>

              <Header.Item>
                <Header.Label>{i18n.t('globals.endDate')}</Header.Label>
                <NewDatepicker
                  lang={i18n.language}
                  blocked={disableHeaderOptions}
                  selected={secondDate}
                  onChange={onChangeSecondDate}
                  maxDate={handleEndDateAvailableDatesLimit()?.[handleEndDateAvailableDatesLimit().length - 1]}
                  minDate={handleEndDateAvailableDatesLimit()?.[0]}
                  hint={i18n.t('endDate')}
                  availableDates={datesAvailable}
                />
              </Header.Item>
            </>
          )}

          {reportType && reportType.value !== 8 && (
            <Header.Item>
              <Header.Label>{i18n.t('shareholderType')}</Header.Label>
              <Dropdown
                disabled={disableHeaderOptions}
                minWidth={150}
                options={shareholderTypes}
                onChange={onChangeShareholderType}
                selected={currentShareholderType}
              />
            </Header.Item>
          )}
          <Header.Item style={{ height: '50px' }}>
            <Header.Label tooltip={i18n.t('reports.zeroedTooltip')}>
              <Inputs.Label $horizontalAlignment="center" style={{ cursor: 'pointer' }}>
                <Inputs.Checkbox
                  name={i18n.t('reports.zeroedLabel')}
                  checked={zeroedCheckbox}
                  key="checkboxTopHolders"
                  handleChange={handleZeroedCheckbox}
                  style={{ paddingLeft: '0' }}
                />
                <Header.Text variant="default">{i18n.t('reports.zeroedLabel')}</Header.Text>
              </Inputs.Label>
            </Header.Label>
          </Header.Item>
        </Header.Content>

        <Header.ButtonGroup>
          {disableLoadButton ? (
            <Tooltip
              text={i18n.t('reports.tooltips.loadButton')}
              $width="300px"
              $alignment={ALIGNMENTS.BOTTOM_LEFT}
              $wrapperStyle={{ zIndex: 3 }}
            >
              <Buttons.Primary disabled onClick={this.onLoad}>
                {i18n.t('load')}
              </Buttons.Primary>
            </Tooltip>
          ) : (
            <Buttons.Primary disabled={disableLoadButton || disableHeaderOptions} onClick={onLoad}>
              {i18n.t('load')}
            </Buttons.Primary>
          )}

          <Buttons.Export disabled={disableHeaderOptions} onClick={onExport}>
            {i18n.t('btnExport')}
          </Buttons.Export>
        </Header.ButtonGroup>
      </Header>
    )
  }
}
