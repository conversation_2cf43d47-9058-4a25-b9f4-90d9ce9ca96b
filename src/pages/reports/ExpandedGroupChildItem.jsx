import React from 'react'
import { Link } from 'react-router-dom'
import { i18n } from 'translate'
import { PATH } from 'consts'

function ExpandedGroupChildItem({
  shareholderType,
  index,
  displayName,
  shareholderId,
  countryCode,
  stockAmountEdited,
  stockPercent,
  documentType,
  country,
  stockAmountStart,
  stockAmountEnd,
  difference,
  type = '1',
}) {
  const vision = {
    simple: '0',
    grouped: '1',
    albert: '2',
  }
  const url =
    shareholderType === vision.albert
      ? `${PATH}/ownership/${shareholderId}/albert/overview`
      : shareholderType === vision.grouped
        ? `${PATH}/ownership/${shareholderId}/grouped/overview`
        : `${PATH}/ownership/${shareholderId}/simple/overview`

  const getShareholderType = (shareholderDocumentType) => {
    const shareholderProfileType = {
      1: '1',
      2: '0',
      default: '1',
    }

    return shareholderProfileType[shareholderDocumentType || 'default']
  }

  const shareholderTypeLabel = getShareholderType(documentType) === vision.simple ? i18n.t('fund') : i18n.t('private')

  return type === '1' ? (
    <li className="sub-item" key={index}>
      <span />
      <span className="name name-resume">
        <Link to={url}>{displayName}</Link>
      </span>
      <span className="shareholder-type">{shareholderTypeLabel}</span>
      <span className="country">{countryCode}</span>
      <span className="volume align-right">{stockAmountEdited}</span>
      <span className="percent align-right">{stockPercent}%</span>
    </li>
  ) : (
    <li className="sub-item type-2" key={index}>
      <span />
      <span className="name name-resume">
        <Link to={url}>{displayName}</Link>
      </span>
      <span className="shareholder-type">{shareholderTypeLabel}</span>
      <span className="country">{country}</span>
      <span className="start-volume align-right">{stockAmountStart}</span>
      <span className="end-volume align-right">{stockAmountEnd}</span>
      <span className="diference align-right">{difference}</span>
    </li>
  )
}

export default ExpandedGroupChildItem
