import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'

import { defineTheme } from 'utils'
import { PATH } from 'consts'

class ReportListCompliance extends Component {
  constructor(props) {
    super(props)
    state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  render() {
    return (
      <div className="base-content">
        <ul className={defineTheme('base-list new grouped-list shareholder-report')}>
          {this.props.currentBase.map((item) => (
            <li key={item.shareholderId}>
              <span className="name">
                <Link to={`${PATH}/ownership/${item.shareholderId}/simple/overview`} target="_blank">
                  {item.name}
                </Link>
              </span>
              <span className="volume-last-value">{this.formatted(item.minimum)}</span>
              <span className="volume-last-value">{this.formatted(item.maximum)}</span>
              <span className="value">{this.formatted(item.average)}</span>
            </li>
          ))}
        </ul>
      </div>
    )
  }
}

export default ReportListCompliance
