import React, { Component } from 'react'
import 'assets/styles/pages/_reports.scss'

import {
  getTickers,
  getTopHolders,
  getTopHoldersMovement,
  getTopBuyers,
  getTopSellers,
  getZeroedOut,
  getTopNewHolders,
  getTopVariation,
  getComplianceAvailableDays,
  getCurrentShareholderChildPosition,
  getCurrentShareholderAlbertChildPosition,
  getReportGroupDetailed,
} from 'client'

import { COMMON, PATH, REPORT_STATUS } from 'consts'

import { i18n } from 'translate'

import {
  defineTheme,
  localInformation,
  dateFromUTC,
  formatDateToString,
  irmReportOptions,
  groupedOptions,
  reportsVisualization,
  shareholderTypesOptions,
  baseViewReports,
} from 'utils'

import { StoreContext } from 'contexts/store'
import {
  hocWrapper,
  getAvailableDates,
  topReportsExport,
  topHoldersReportExport,
  topBuyersReportExport,
  topSellersReportExport,
  topNewHoldersReportExport,
  getGroups,
  postGroupShareholder,
  postCreateGroupAndGroupShareholder,
  topVariationReportExport,
  topZeroedReportExport,
  topHoldersMovementReportExport,
} from 'hooks'
import { DownloadFileButton, LinkButton, GoToHistoryButton, GroupModal } from 'components'
import { getCompany } from 'globals/storages/locals'
import ReportList from './ReportList'
import ReportListCompliance from './ReportListCompliance'
import ReportHeader from './ReportHeader'
import { SHAREHOLDER_TYPES } from 'types/shareholders'

class BaseShareholderReport extends Component {
  reportTypes = {
    1: 'topHolders',
    2: 'topBuyers',
    3: 'topSellers',
    4: 'topZeroedPositions',
    5: 'topNewHolders',
    6: 'topVariation',
    7: 'topHoldersMovement',
  }

  state = {
    idiom: i18n.language === 'pt-BR' ? 1 : 0,
    firstLoad: true,
    companyId: localInformation.getCompanyId(),
    company: getCompany(),
    currentBase: [],
    currentBatchInfo: null,
    currentDate: null,
    currentFund: null,
    currentInvestor: null,
    currentOrder: 1,
    currentReport: irmReportOptions[0],
    currentVisualization: reportsVisualization[0],
    currentShareholderType: shareholderTypesOptions[0],
    currentTicker: null,
    currentViewType: baseViewReports[0],
    datesAvailable: [],
    disableLoadButton: false,
    groupList: [],
    endDate: null,
    firstDate: null,
    fromAlbert: false,
    isDaily: true,
    isLoading: true,
    modalChangeValue: false,
    modalClosePrice: false,
    mustRender: true,
    searchTerm: '',
    secondDate: null,
    shareholderType: 1,
    showAggModal: false,
    showMapModal: false,
    showVolume: true,
    startDate: null,
    stockType: null,
    zeroedCheckbox: false,
    tickers: [],
    topQuantity: JSON.parse(JSON.stringify(COMMON.topQuantity))[1],
    shareholderGroupChilds: new Map(),
    shareholderGroupIsLoading: new Map(),
    shareholderGroupExpanded: new Map(),
    currentGroupedType: groupedOptions[0],
  }

  componentWillMount() {
    this.getTickers()
  }

  getTickers = () => {
    const { companyId } = this.state
    getTickers(companyId).then((res) => {
      if (res.success) {
        const options = res.data.map((ticker) => ({
          label: ticker.label,
          value: ticker.tickerId,
        }))

        const currentTicker = options[0] ?? null

        this.setState({
          tickers: options,
          currentTicker,
        })
      }
    })
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.currentTicker !== this.state.currentTicker) {
      this.getAvailableDates(this.state.currentTicker.value)
    }

    if (
      this.state.currentReport &&
      this.state.mustRender &&
      this.state.currentOrder &&
      this.state.firstLoad &&
      this.state.firstDate &&
      this.state.secondDate
    ) {
      this.handleLoadData()
      this.handleGetGroups()
    }
  }

  getAvailableDates = (tickerId) => {
    const { companyId } = this.state
    getAvailableDates({ companyId, tickerId }).then((res) => {
      if (!res.length) {
        this.props.createToast({
          type: 'alert',
          title: i18n.t('globals.infos.noBaseListFound.title'),
          description: i18n.t('globals.infos.noBaseListFound.message'),
        })
        return this.setState({
          datesAvailable: [],
          firstDate: null,
          secondDate: null,
          currentReport: irmReportOptions[0],
        })
      }

      const datesAvailable = res.sort((a, b) => (a > b ? 1 : -1)).map((date) => dateFromUTC(date))

      const secondDate = datesAvailable[datesAvailable.length - 1]
      const firstDate = res.length >= 2 ? datesAvailable[datesAvailable.length - 2] : secondDate

      this.setState({
        datesAvailable,
        firstDate,
        secondDate,
        currentReport: irmReportOptions[0],
      })
    })
  }

  handleGroupModalTitleShareholderType = () => {
    const { currentFund } = this.state
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.FUND) return i18n.t('ownership.fund').toLowerCase()
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.INDIVIDUAL)
      return i18n.t('ownership.individual').toLowerCase()
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.UNKNOW) return ''

    return
  }

  handleGetGroups = async () => {
    try {
      const { companyId } = this.state
      const shareholderGroups = await getGroups(companyId)

      const formatShareholderGroups = shareholderGroups.data.data.map((shareholderGroup) => ({
        label: shareholderGroup.name,
        value: shareholderGroup.shareholderGroupId,
      }))

      this.setState({
        groupList: formatShareholderGroups,
      })
    } catch {
      return this.props.createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        type: 'error',
      })
    }
  }

  onCreateGroup = async (groupName) => {
    const { companyId, currentFund } = this.state
    try {
      const res = await postCreateGroupAndGroupShareholder({
        companyId,
        groupName,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      await this.handleGetGroups()
      await this.handleLoadData()

      this.setState({ showAggModal: false })

      this.handleGroupedShareholderActionsToast(res.data.shareholderGroupId, currentFund.shareholderId)
    } catch (error) {
      this.props.createToast({
        title: i18n.t('globals.errors.createGroup.title'),
        description: i18n.t('globals.errors.createGroup.message'),
        duration: 9000,
        type: 'error',
      })

      this.setState({ showAggModal: false })
    }
  }

  onVinculateGroup = async (shareholderGroup) => {
    const { companyId, currentFund } = this.state
    const shareholderGroupId = shareholderGroup.value
    try {
      await postGroupShareholder({
        companyId,
        shareholderGroupId,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      await this.handleLoadData()

      this.setState({ showAggModal: false })

      this.handleGroupedShareholderActionsToast(shareholderGroupId, currentFund.shareholderId)
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          type: 'error',
          title: err.title,
          description: err.message,
        })
        return
      }
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
      })

      this.setState({ showAggModal: false })
    }
  }

  getWillMount = () => {
    this.componentWillMount()
  }

  getTopHolders = async (tickerId, referenceDate) => {
    const { companyId, topQuantity, currentShareholderType, currentViewType, currentGroupedType } = this.state

    const response = await getTopHolders({
      companyId,
      tickerId,
      referenceDate: formatDateToString(referenceDate),
      limit: topQuantity.value,
      shareholderType: currentShareholderType.value,
      viewType: currentViewType.value,
      groupedType: currentGroupedType.value,
    })

    this.setState({
      currentBase: response.positions,
      currentBatchInfo: response.batch,
      isLoading: false,
    })
  }

  getTopHoldersMovement = (tickerId, referenceDateStart, referenceDateEnd) => {
    const { companyId, topQuantity, currentShareholderType, currentViewType, currentGroupedType } = this.state

    getTopHoldersMovement({
      companyId,
      tickerId,
      referenceDateStart: formatDateToString(referenceDateStart),
      referenceDateEnd: formatDateToString(referenceDateEnd),
      limit: topQuantity.value,
      shareholderType: currentShareholderType.value,
      viewType: currentViewType.value,
      groupedType: currentGroupedType.value,
    }).then((response) => {
      this.setState({ currentBase: response.positions, currentBatchInfo: response.batch, isLoading: false })
    })
  }

  getTopBuyers = (tickerId, referenceDateStart, referenceDateEnd) => {
    const { companyId, topQuantity, currentShareholderType, currentViewType, currentGroupedType } = this.state

    getTopBuyers({
      companyId,
      tickerId,
      referenceDateStart: formatDateToString(referenceDateStart),
      referenceDateEnd: formatDateToString(referenceDateEnd),
      limit: topQuantity.value,
      shareholderType: currentShareholderType.value,
      viewType: currentViewType.value,
      groupedType: currentGroupedType.value,
    }).then((response) => {
      this.setState({ currentBase: response.positions, currentBatchInfo: response.batch, isLoading: false })
    })
  }

  getTopSellers = (tickerId, referenceDateStart, referenceDateEnd) => {
    const { companyId, topQuantity, currentShareholderType, currentViewType, currentGroupedType } = this.state

    getTopSellers({
      companyId,
      tickerId,
      referenceDateStart: formatDateToString(referenceDateStart),
      referenceDateEnd: formatDateToString(referenceDateEnd),
      limit: topQuantity.value,
      shareholderType: currentShareholderType.value,
      viewType: currentViewType.value,
      groupedType: currentGroupedType.value,
    }).then((response) => {
      this.setState({ currentBase: response.positions, currentBatchInfo: response.batch, isLoading: false })
    })
  }

  getZeroedOut = (tickerId, referenceDateStart, referenceDateEnd) => {
    const { companyId, topQuantity, currentShareholderType, currentViewType, currentGroupedType } = this.state

    getZeroedOut({
      companyId,
      tickerId,
      referenceDateStart: formatDateToString(referenceDateStart),
      referenceDateEnd: formatDateToString(referenceDateEnd),
      limit: topQuantity.value,
      shareholderType: currentShareholderType.value,
      viewType: currentViewType.value,
      groupedType: currentGroupedType.value,
    }).then((response) => {
      this.setState({ currentBase: response.positions, isLoading: false })
    })
  }

  getTopNewHolders = (tickerId, referenceDateStart, referenceDateEnd) => {
    const { companyId, topQuantity, currentShareholderType, currentViewType, currentGroupedType } = this.state

    getTopNewHolders({
      companyId,
      tickerId,
      referenceDateStart: formatDateToString(referenceDateStart),
      referenceDateEnd: formatDateToString(referenceDateEnd),
      limit: topQuantity.value,
      shareholderType: currentShareholderType.value,
      viewType: currentViewType.value,
      groupedType: currentGroupedType.value,
    }).then((response) => {
      this.setState({ currentBase: response.positions, currentBatchInfo: response.batch, isLoading: false })
    })
  }

  getTopVariation = (tickerId) => {
    const {
      companyId,
      topQuantity,
      currentShareholderType,
      currentViewType,
      currentGroupedType,
      firstDate,
      secondDate,
      currentOrder,
    } = this.state

    getTopVariation({
      companyId,
      tickerId,
      referenceDateStart: formatDateToString(firstDate),
      referenceDateEnd: formatDateToString(secondDate),
      limit: topQuantity.value,
      shareholderType: currentShareholderType.value,
      viewType: currentViewType.value,
      groupedType: currentGroupedType.value,
      currentOrder,
    }).then((response) => {
      this.setState({ currentBase: response.positions, isLoading: false })
    })
  }

  onChangeTopQuantity = (value) => {
    if (value === this.state.topQuantity) return

    const disableLoadButton = !!(value.value === *********)
    this.cleanExpandGroups()
    this.setState({ topQuantity: value, isLoading: true, disableLoadButton, mustRender: true })
  }

  onChangeViewTypeGroup = (value) => {
    if (value === this.state.currentViewType) return
    this.cleanExpandGroups()
    if (value.value !== 'albert') {
      this.setState({
        currentViewType: value,
        fromAlbert: false,
        isLoading: true,
        mustRender: true,
      })
    } else {
      this.setState({
        currentViewType: value,
        fromAlbert: true,
        isLoading: true,
        mustRender: true,
        currentGroupedType: groupedOptions[0],
      })
    }
  }

  onChangeFirstDate = (date) => {
    const { secondDate, firstDate } = this.state
    if (date === firstDate) return

    const adjustedSecondDate = date > secondDate ? date : secondDate

    this.cleanExpandGroups()
    this.setState({
      firstDate: date,
      secondDate: adjustedSecondDate,
      isLoading: true,
      mustRender: true,
    })
  }

  onChangeSecondDate = (date) => {
    const { firstDate, secondDate } = this.state
    if (date === secondDate) return

    const adjustedFirstDate = date < firstDate ? date : firstDate

    this.cleanExpandGroups()
    this.setState({
      firstDate: adjustedFirstDate,
      secondDate: date,
      isLoading: true,
      mustRender: true,
    })
  }
  onChangeShareholderType = (value) => {
    if (value === this.state.currentShareholderType) return

    this.cleanExpandGroups()
    this.setState({
      currentShareholderType: value,
      isLoading: true,
      mustRender: true,
    })
  }

  onChangeVisualizationType = (value) => {
    if (value.value === this.state.currentVisualization.value) return

    this.cleanExpandGroups()

    this.setState({
      currentVisualization: value,
    })
  }

  onChangeReportType = (value) => {
    if (value.value === this.state.currentReport.value) return

    this.cleanExpandGroups()

    this.setState({
      currentReport: value,
      isLoading: true,
      mustRender: true,
    })
  }

  onChangeStockType = (value) => {
    if (value === this.state.currentTicker) return
    this.cleanExpandGroups()
    this.setState({ currentTicker: value, isLoading: true, mustRender: true })
  }

  onChangeGroupedType = (value) => {
    if (value === this.state.currentGroupedType) return
    this.cleanExpandGroups()
    this.setState({ currentGroupedType: value, isLoading: true, mustRender: true })
  }

  handleExportData = async () => {
    const {
      companyId,
      currentTicker,
      firstDate,
      secondDate,
      topQuantity,
      currentShareholderType,
      currentViewType,
      currentGroupedType,
      currentReport,
    } = this.state

    const reportType = this.getReportType(currentReport.value)

    const params = {
      reportType,
      companyId,
      tickerId: currentTicker.value,
      viewType: currentViewType.value,
      groupedType: currentGroupedType.value,
      limit: topQuantity.value,
      referenceDate: formatDateToString(secondDate),
      referenceDateStart: formatDateToString(firstDate),
      referenceDateEnd: formatDateToString(secondDate),
      shareholderType: currentShareholderType.value || '',
      language: i18n.language === 'pt-BR' ? 1 : 0,
    }

    this.props.createToast({
      type: 'info',
      title: i18n.t('globals.export.sent.title'),
      description: i18n.t('globals.export.sent.message'),
    })

    const reportTypesKeyValues = Object.values(this.reportTypes).map((report) => [report, topReportsExport])
    const reportTypesDefaultExport = Object.fromEntries(reportTypesKeyValues)
    const exportReport = {
      ...reportTypesDefaultExport,
      topHolders: topHoldersReportExport,
      topBuyers: topBuyersReportExport,
      topSellers: topSellersReportExport,
      topNewHolders: topNewHoldersReportExport,
      topVariation: topVariationReportExport,
      topZeroedPositions: topZeroedReportExport,
      topHoldersMovement: topHoldersMovementReportExport,
    }

    const { shareholderReportId, status } = await exportReport[reportType](params)

    return this.props.createToast({
      type: 'success',
      title: i18n.t('globals.export.success.title'),
      description: i18n.t('globals.export.success.message'),
      buttons: (
        <>
          {status == REPORT_STATUS.SUCCESS && <DownloadFileButton shareholderReportId={shareholderReportId} />}
          <GoToHistoryButton />
        </>
      ),
    })
  }

  handleLoadData = async () => {
    this.setState({ mustRender: false, firstLoad: false })

    const reportTypes = {
      1: async () => this.getTopHolders(this.state.currentTicker.value, this.state.secondDate),
      2: async () =>
        this.getTopBuyers(
          this.state.currentTicker.value,
          this.state.firstDate,
          this.state.secondDate,
          this.state.fromAlbert
        ),
      3: async () =>
        this.getTopSellers(
          this.state.currentTicker.value,
          this.state.firstDate,
          this.state.secondDate,
          this.state.fromAlbert
        ),
      4: async () =>
        this.getZeroedOut(
          this.state.currentTicker.value,
          this.state.firstDate,
          this.state.secondDate,
          this.state.fromAlbert
        ),
      5: async () =>
        this.getTopNewHolders(
          this.state.currentTicker.value,
          this.state.firstDate,
          this.state.secondDate,
          this.state.fromAlbert
        ),
      6: async () => this.getTopVariation(this.state.currentTicker.value, this.state.fromAlbert),
      7: async () =>
        this.getTopHoldersMovement(
          this.state.currentTicker.value,
          this.state.firstDate,
          this.state.secondDate,
          this.state.fromAlbert
        ),
    }

    const report =
      this.state.currentReport?.value in reportTypes ? reportTypes[this.state.currentReport.value] : reportTypes[1]
    try {
      await report()
    } catch (err) {
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
      })
      this.setState({ isLoading: false })
    }
  }

  onSortClick = () => {
    const newOrder = this.state.currentOrder * -1
    this.setState({ currentOrder: newOrder, isLoading: true })
  }

  onOpenAgglutinationModal = (item) => {
    this.setState({ showAggModal: true, currentFund: item })
  }

  closeAggModal = () => {
    this.setState({ showAggModal: false })
  }

  handleGroupedShareholderActionsToast = (shareholderGroupId, shareholderId) => {
    this.props.createToast({
      title: i18n.t('globals.groupedShareholderActionsToastfy.success.title'),
      description: i18n.t('globals.groupedShareholderActionsToastfy.success.message'),
      duration: 15000,
      type: 'success',
      buttons: (
        <>
          <LinkButton link={`${PATH}/ownership/${shareholderGroupId}/grouped/overview`}>
            {i18n.t('groupDetail')}
          </LinkButton>
          <LinkButton link={`${PATH}/ownership/${shareholderId}/simple/overview`}>
            {i18n.t('shareholderDetail')}
          </LinkButton>
        </>
      ),
    })
  }

  /**
   * Expands groups in resume page
   * @param {Object} shareholderGroup
   * @param {int} index
   */
  expandGroups = (shareholderGroup) => {
    const { companyId } = this.state
    const tickerId = this.state.currentTicker.value
    const groupId = shareholderGroup.shareholderGroupId
    const reportType = this.getReportType(this.state.currentReport.value)
    const referenceDateStart = formatDateToString(this.state.firstDate)
    const referenceDateEnd = formatDateToString(this.state.secondDate)
    const { fromAlbert } = this.state

    const { shareholderGroupChilds, shareholderGroupIsLoading, shareholderGroupExpanded, zeroedCheckbox } = this.state

    shareholderGroupIsLoading.set(groupId)
    this.setState({ shareholderGroupIsLoading })

    const queryLimit = 100

    if (shareholderGroupChilds.has(groupId)) {
      shareholderGroupChilds.delete(groupId)
      shareholderGroupIsLoading.delete(groupId)
      shareholderGroupExpanded.delete(groupId)
      this.setState({ shareholderGroupChilds, shareholderGroupIsLoading, shareholderGroupExpanded })
    } else if (reportType === 'topHolders') {
      if (fromAlbert) {
        getCurrentShareholderAlbertChildPosition(companyId, tickerId, referenceDateStart, groupId).then((res) => {
          shareholderGroupChilds.set(groupId, res.data.positions)
          shareholderGroupExpanded.set(groupId)
          shareholderGroupIsLoading.delete(groupId)
          this.setState({ shareholderGroupChilds, shareholderGroupIsLoading, shareholderGroupExpanded })
        })
      } else {
        getCurrentShareholderChildPosition(
          companyId,
          tickerId,
          referenceDateEnd,
          groupId,
          queryLimit,
          zeroedCheckbox
        ).then((res) => {
          shareholderGroupChilds.set(groupId, res.data.positions)
          shareholderGroupExpanded.set(groupId)
          shareholderGroupIsLoading.delete(groupId)
          this.setState({ shareholderGroupChilds, shareholderGroupIsLoading, shareholderGroupExpanded })
        })
      }
    } else {
      getReportGroupDetailed(
        companyId,
        tickerId,
        groupId,
        referenceDateStart,
        referenceDateEnd,
        reportType,
        fromAlbert,
        queryLimit,
        zeroedCheckbox
      ).then((res) => {
        shareholderGroupChilds.set(groupId, res.data)
        shareholderGroupExpanded.set(groupId)
        shareholderGroupIsLoading.delete(groupId)
        this.setState({ shareholderGroupChilds, shareholderGroupIsLoading, shareholderGroupExpanded })
      })
    }
  }

  cleanExpandGroups = () => {
    const { shareholderGroupChilds, shareholderGroupIsLoading, shareholderGroupExpanded } = this.state
    shareholderGroupChilds.clear()
    shareholderGroupIsLoading.clear()
    shareholderGroupExpanded.clear()

    this.setState({ shareholderGroupChilds, shareholderGroupIsLoading, shareholderGroupExpanded })
  }

  getReportType = (reportTypeValue) => {
    return this.reportTypes[reportTypeValue]
  }

  handleZeroedCheckbox = (event) => {
    this.setState(
      {
        zeroedCheckbox: event.target.checked,
      },
      () => {
        this.cleanExpandGroups()
      }
    )
  }

  render() {
    const {
      currentBase,
      currentBatchInfo,
      currentReport,
      currentTicker,
      currentShareholderType,
      currentViewType,
      currentVisualization,
      datesAvailable,
      disableLoadButton,
      firstDate,
      isLoading,
      searchTerm,
      secondDate,
      tickers,
      topQuantity,
      currentGroupedType,
      zeroedCheckbox,
      groupList,
    } = this.state

    const topQuantityReportOptions = JSON.parse(JSON.stringify(COMMON.topQuantity))

    topQuantityReportOptions.push({
      value: *********,
      label: i18n.t('all'),
    })

    if (!tickers.length) {
      return (
        <div className="shareholders-wrapper">
          <div className={defineTheme('shareholders-content')}>
            <div className="lds-dual-ring">
              <div />
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className="shareholders-wrapper">
        <div className={defineTheme('shareholders-content')}>
          <div>
            <ReportHeader
              isLoading={isLoading}
              currentGroupedType={currentGroupedType}
              currentViewType={currentViewType}
              currentTicker={currentTicker}
              datesAvailable={datesAvailable}
              disableLoadButton={disableLoadButton}
              firstDate={firstDate}
              onChangeFirstDate={this.onChangeFirstDate}
              onChangeGroupedType={this.onChangeGroupedType}
              onChangeReportType={this.onChangeReportType}
              onChangeSecondDate={this.onChangeSecondDate}
              onChangeShareholderType={this.onChangeShareholderType}
              onChangeStockType={this.onChangeStockType}
              onChangeTopQuantity={this.onChangeTopQuantity}
              onChangeViewTypeGroup={this.onChangeViewTypeGroup}
              onChangeVisualizationType={this.onChangeVisualizationType}
              onExport={this.handleExportData}
              onLoad={this.handleLoadData}
              reportQuantity={topQuantityReportOptions}
              reportType={currentReport}
              searchTerm={searchTerm}
              secondDate={secondDate}
              currentShareholderType={currentShareholderType}
              shareholderTypes={shareholderTypesOptions}
              selectedTopQuantity={topQuantity}
              tickers={tickers}
              visualizationType={currentVisualization}
              zeroedCheckbox={zeroedCheckbox}
              handleZeroedCheckbox={this.handleZeroedCheckbox}
            />
            {this.state.mustRender && (
              <div className="report-placeholder">
                <p>{i18n.t('loadReport')}</p>
              </div>
            )}
            {!this.state.mustRender && (
              <div className={'scroll report-scroll shareholder-report'}>
                {this.state.currentReport.value !== 8 && (
                  <ReportList
                    onSortClickHandler={this.onSortClick}
                    onOpenChangeValueModal={this.onOpenChangeValueModal}
                    onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                    openClosePriceModal={this.openClosePriceModal}
                    expandGroups={this.expandGroups}
                    shareholderGroupChilds={this.state.shareholderGroupChilds}
                    currentBase={currentBase}
                    visualization={currentVisualization.value}
                    currentBatchInfo={currentBatchInfo}
                    idiom={this.state.idiom}
                    companyId={this.state.companyId}
                    tickerId={this.state.currentTicker.value}
                    referenceDate={this.state.currentDate}
                    vision={this.state.currentReport.value}
                    type={this.state.currentViewType.value}
                    reportType={this.state.currentReport.value}
                    totalStocks={this.state.currentBatchInfo ? this.state.currentBatchInfo.totalStocks : 0}
                    isLoading={isLoading}
                    shareholderGroupIsLoading={this.state.shareholderGroupIsLoading}
                    shareholderGroupExpanded={this.state.shareholderGroupExpanded}
                  />
                )}
                {this.state.currentReport.value === 8 && (
                  <ReportListCompliance currentBase={currentBase} idiom={this.state.idiom} />
                )}
              </div>
            )}
          </div>
        </div>
        <GroupModal
          title={`${i18n.t('ownership.agroup')} ${this.handleGroupModalTitleShareholderType()}`}
          visibility={this.state.showAggModal}
          options={groupList}
          onClose={this.closeAggModal}
          onCreateGroup={this.onCreateGroup}
          onConfirm={this.onVinculateGroup}
        />
      </div>
    )
  }
}

BaseShareholderReport.contextType = StoreContext

const ShareholderReport = hocWrapper(BaseShareholderReport)

export default ShareholderReport
