import { useLog<PERSON> } from '@logto/react'
import { Navigate } from 'react-router-dom'
import { useCallback, useEffect } from 'react'
import storeInformation from 'utils/storeInformation'
import { useExternalRedirect, EXTERNAL_PRODUCT } from 'hooks/use-external-redirect'
import { LogtoTemplate } from './logto.template'
import { env } from '../../env'

export function Logto() {
  const { signIn } = useLogto()
  const { navigateTo } = useExternalRedirect()

  const isUserLogged = !(storeInformation.getCore2Token() == null)

  const handleSignIn = useCallback(async () => {
    const callbackUrl = new URL('/logto/callback', window.location.origin)
    await signIn(callbackUrl.toString())
  }, [signIn])

  useEffect(() => {
    if (!isUserLogged) {
      handleSignIn()
    }
  }, [handleSignIn, isUserLogged])

  const userApplications = storeInformation.getCore2SelectedCustomerUserApplications() ?? {}
  const userHasAccess = Object.keys(userApplications).length > 0
  const userHasShareholderAccess = Object.keys(userApplications).includes('mz_irmnew') && !!userApplications?.mz_irmnew

  const userHasPermissions = !!userApplications && userHasAccess && userHasShareholderAccess

  if (!userHasPermissions && !env.IS_DEV) {
    navigateTo(EXTERNAL_PRODUCT.DASHBOARD)
    return null
  }

  if (isUserLogged) {
    return <Navigate to="/shareholders" replace />
  }

  return <LogtoTemplate />
}
