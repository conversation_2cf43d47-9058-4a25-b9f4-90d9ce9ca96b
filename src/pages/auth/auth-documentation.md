# Página de Autenticação

## Visão Geral

A página de autenticação é responsável pelo processo de login dos usuários no sistema, utilizando a integração com o serviço Logto.

## Funcionalidades

### Login de Usuário

- Autenticação dos usuários através do serviço Logto
- Redirecionamento automático para a página de login quando o usuário não está autenticado
- Armazenamento de tokens e informações do usuário após autenticação bem-sucedida

### Verificação de Permissões

- Verifica se o usuário tem acesso ao módulo de acionistas (mz_irmnew)
- Redireciona para o dashboard principal caso não tenha permissões necessárias

### Fluxo de Autenticação

1. O usuário é direcionado para a tela de login
2. Após informar credenciais válidas, é redirecionado para a página de callback
3. A aplicação processa o token de acesso e recupera as informações do usuário
4. O sistema armazena as informações necessárias do usuário (perfil, permissões, customizações)
5. O usuário é redirecionado para a página principal de acionistas

## Guia de Uso

### Interface de Login

A página de autenticação apresenta:

- **Formulário de Login**: Interface para inserção de credenciais
- **Tela de Carregamento**: Exibida durante o processo de autenticação
- **Página de Callback**: Processa o retorno da autenticação (geralmente não visível ao usuário)
- **Mensagens de Erro**: Exibidas em caso de problemas no login

### Processo de Login

1. **Acesso à Página**
   - Ao acessar o sistema, o usuário é automaticamente redirecionado para a página de login
   - Caso a sessão tenha expirado, o redirecionamento também ocorre automaticamente
   - A interface de login do Logto é carregada para inserção das credenciais
   - O sistema pode manter o último e-mail utilizado para facilitar logins recorrentes

2. **Inserção de Credenciais**
   - Digite o e-mail cadastrado no campo correspondente
   - Insira a senha associada à conta
   - Opcionalmente, marque "Lembrar-me" para facilitar logins futuros
   - Clique no botão "Entrar" ou pressione Enter para prosseguir

3. **Processamento da Autenticação**
   - O sistema exibe uma tela de carregamento enquanto processa a autenticação
   - As credenciais são validadas pelo serviço Logto
   - Em caso de sucesso, o token de acesso é gerado
   - O usuário é redirecionado para a página de callback

4. **Callback e Verificação**
   - A página de callback processa o token recebido do Logto
   - Recupera informações detalhadas do perfil do usuário
   - Verifica as permissões e acesso aos módulos
   - Armazena localmente os dados necessários para a sessão

### Cenários Comuns

1. **Primeiro Acesso**
   - Insira as credenciais fornecidas pelo administrador
   - O sistema pode solicitar troca de senha ou confirmação de informações
   - Complete o processo conforme solicitado
   - Após a configuração inicial, o acesso normal será concedido

2. **Login com Senha Incorreta**
   - Uma mensagem de erro será exibida
   - O sistema pode informar quantas tentativas restam
   - Utilize a opção "Esqueci minha senha" se necessário
   - Ou tente novamente com as credenciais corretas

3. **Redirecionamento por Falta de Permissão**
   - Após o login bem-sucedido, o sistema verifica as permissões
   - Se o usuário não tem acesso ao módulo de acionistas
   - Será redirecionado para o dashboard principal
   - Uma mensagem explicativa pode ser exibida

4. **Sessão Expirada**
   - Quando a sessão expira durante o uso do sistema
   - O usuário é automaticamente redirecionado para a página de login
   - Uma mensagem informativa sobre a expiração pode ser exibida
   - Realize o login novamente para continuar

### Recursos Adicionais

1. **Recuperação de Senha**
   - Utilize a opção "Esqueci minha senha" na tela de login
   - Siga as instruções enviadas por e-mail
   - Complete o processo de redefinição
   - O novo acesso estará disponível imediatamente após a alteração

2. **Suporte Técnico**
   - Em caso de problemas persistentes de login
   - Utilize os canais de suporte disponíveis
   - Informe o erro específico encontrado
   - A equipe de suporte poderá auxiliar na resolução

### Dicas de Segurança

- Nunca compartilhe suas credenciais de acesso
- Utilize senhas fortes e exclusivas para o sistema
- Evite acessar o sistema em redes públicas não seguras
- Sempre faça logout ao finalizar o uso, especialmente em computadores compartilhados
- Verifique se o endereço do site está correto antes de inserir suas credenciais

## Componentes Técnicos

- **Logto**: Componente principal de autenticação e login
- **LogtoCallback**: Manipulador de callback do processo de autenticação
- **Logout**: Componente de logout e encerramento de sessão
- **AuthLayout**: Layout base para páginas autenticadas da aplicação
- **AuthGuard**: Proteção de rotas baseada em estado de autenticação
- **TokenManager**: Gerenciamento de tokens de acesso e refresh
- **SessionProvider**: Provedor de contexto para dados de sessão
- **PermissionChecker**: Verificação de permissões baseada em scopes de usuário
- **LoginForm**: Formulário de entrada de credenciais quando aplicável
- **AuthContext**: Contexto React para compartilhamento de estado de autenticação
