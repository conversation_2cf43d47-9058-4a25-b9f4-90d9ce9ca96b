import { useHandleSignInCallback, useLogto } from '@logto/react'
import { useNavigate } from 'react-router-dom'
import { useCallback, useEffect } from 'react'
import { coreAPI } from 'services/core/fetcher'
import storeInformation from 'utils/storeInformation'
import { useToast } from '@mz-codes/design-system'
import { env } from '../../../env'
import { LogtoCallbackTemplate } from './logto-callback.template'

export function LogtoCallback() {
  const { isAuthenticated } = useHandleSignInCallback()
  const { getAccessToken } = useLogto()
  const navigate = useNavigate()

  const { createToast } = useToast()

  const handleAuthenticate = useCallback(async () => {
    try {
      const accessToken = await getAccessToken(env.API_BASE_URL)
      if (accessToken == null) {
        throw new Error('No "accessToken" provided')
      }

      const response = await coreAPI.authenticateFromLogto(accessToken)

      const { token } = response
      const {
        id,
        userId,
        name,
        email,
        customers,
        customerProfiles,
        portalCustomizations,
        daysUntilPasswordExpiration,
      } = response.data

      const currentUser = { name, email }

      storeInformation.setCore2UserId(id)
      storeInformation.setCore2Token(token)
      storeInformation.setCore2UserProfiles(JSON.stringify(customerProfiles))
      storeInformation.setCore2UserCustomizations(portalCustomizations)
      storeInformation.setUserEmail(email)
      storeInformation.setUserInformation(currentUser)
      storeInformation.setUserPasswordExpiration(daysUntilPasswordExpiration)
      storeInformation.setCore2UserCustomers(Array.from(new Set(customers)))
      storeInformation.setCore2UserCustomersNumber(Array.from(new Set(customers)).length)
      storeInformation.setUserId(userId)
      storeInformation.setUserInformation(currentUser)

      navigate('/shareholders', { replace: true })
      return undefined
    } catch (error) {
      createToast({
        title: 'Error',
        description: 'Error authenticating',
        type: 'error',
      })
      return undefined
    }
  }, [getAccessToken, navigate, createToast])

  useEffect(() => {
    if (isAuthenticated) {
      handleAuthenticate()
      return undefined
    }
    return undefined
  }, [handleAuthenticate, isAuthenticated])

  return <LogtoCallbackTemplate />
}
