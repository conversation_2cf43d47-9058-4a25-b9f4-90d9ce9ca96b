import { api, MZ_IRM_NEW } from 'globals/api'
import { ExportFailedError } from 'pages/shareholders/errors'
import { ExportShareholdersGroupingProps } from './types'

export const exportShareholdersGrouping = async (params: ExportShareholdersGroupingProps) => {
  const { companyId, language } = params

  const payload = { language }

  try {
    const response = await api.post(`${MZ_IRM_NEW}/companies/${companyId}/shareholders/grouping/export`, payload)

    return response.data
  } catch (err) {
    throw ExportFailedError
  }
}
