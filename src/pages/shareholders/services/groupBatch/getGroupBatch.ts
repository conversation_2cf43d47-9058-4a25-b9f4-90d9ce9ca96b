import { MZ_IRM_NEW as MZ_SHAREHOLDERS, api } from 'globals/api'
import { TGetGroupBatchParams, TGroupBatch, TGetGroupingTemplateParams, TGroupingTemplateResponse } from './types'

export const getGroupBatch = async ({ companyId }: TGetGroupBatchParams): Promise<TGroupBatch | undefined> => {
  const uri = `${MZ_SHAREHOLDERS}/shareholders/companies/${companyId}/grouping-batch/last`
  const response = await api.get(uri)
  return response.status === 200 ? response.data : undefined
}

export const getGroupingTemplate = async ({
  language,
}: TGetGroupingTemplateParams): Promise<TGroupingTemplateResponse> => {
  try {
    const response = await api.get<ArrayBuffer, TGroupingTemplateResponse>(
      `${MZ_SHAREHOLDERS}/shareholders/shareholder-group/template`,
      {
        responseType: 'arraybuffer',
        params: {
          language,
        },
      }
    )

    return response
  } catch (error: unknown) {
    throw new Error('Error getting grouping template')
  }
}
