import { api, MZ_IRM_NEW } from 'globals/api'
import { ExportFailedError } from 'pages/shareholders/errors'
import { ExportShareholdersGroupsParams } from './types'

export const exportShareholdersGroups = async (params: ExportShareholdersGroupsParams) => {
  const { companyId, language, search = '' } = params
  const payload = {
    language,
    search,
  }

  try {
    const response = await api.post(`${MZ_IRM_NEW}/companies/${companyId}/shareholders/groups/export`, payload)

    return response.data
  } catch (err) {
    throw ExportFailedError
  }
}
