import { api, MZ_IRM_NEW } from 'globals/api'
import { ExportFailedError } from 'pages/shareholders/errors'
import { ExportShareholdersParams } from './types'

export const exportShareholders = async (params: ExportShareholdersParams) => {
  const { companyId, shareholderType, groupedType, language, search = '' } = params

  const payload = {
    shareholderType,
    groupedType,
    language,
    search,
  }

  try {
    const response = await api.post(`${MZ_IRM_NEW}/companies/${companyId}/shareholders/export`, payload)

    return response.data
  } catch (err) {
    throw ExportFailedError
  }
}
