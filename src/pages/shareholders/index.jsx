import React, { Component } from 'react'
import { Link } from 'react-router-dom'
import { PATH } from 'consts'

import {
  ActionsDropdown,
  Dropdown,
  Loading,
  Buttons,
  Icons,
  DeleteModal,
  Header,
  theme,
  Table,
  Inputs,
} from '@mz-codes/design-system'

import ReactTooltip from 'react-tooltip'

import { i18n } from 'translate'

import {
  localInformation,
  sessionInformation,
  inputChangedHandler,
  viewsOptions,
  groupedOptions,
  shareholderTypesOptions,
  formatDateToString,
  hasScopePermission,
  debounce,
  doDownload,
} from 'utils'

import { UploadModal, NewGroupModal, GoToHistoryButton, LinkButton, EditModal, GroupModal } from 'components'
import {
  deleteSpreadsheetGrouping,
  postSpreadsheetGrouping,
  getClassificationsList,
  deleteClassification,
  hocWrapper,
  editClassification,
  postCreateGroupAndGroupShareholder,
  getGroups,
  postGroupShareholder,
} from 'hooks'
import { StoreContext } from 'contexts/store'

import 'assets/styles/pages/_shareholders.scss'

import { getShareholdersNew, createShareholder, createGroup, getTickers } from 'client'

import {
  getGroupBatch,
  getGroupingTemplate,
  groupBatchStatus,
  exportShareholders,
  exportShareholdersGroups,
  exportShareholdersGrouping,
} from './services'

import { ExportFailedError, CreateNewGroupError } from './errors'
import { BaseError } from 'errors'
import { SHAREHOLDER_TYPES } from 'types/shareholders'
import { NewShareholderModal } from 'components/modals/new-shareholder-modal'
import { GroupIcon } from 'components'

const fiveSeconds = 5000
const acceptedFiles = '.txt, .csv, .xls, .xlsx, .zip, .7z'

class BaseShareholderListing extends Component {
  static cpfCnpj = (value) => {
    let newValue = value.replace(/\D/g, '')

    if (value.length <= 11) {
      // CPF
      newValue = newValue.replace(/(\d{3})(\d)/, '$1.$2')
      newValue = newValue.replace(/(\d{3})(\d)/, '$1.$2')
      newValue = newValue.replace(/(\d{3})(\d{1,2})$/, '$1-$2')
    } else {
      // CNPJ
      newValue = newValue.replace(/^(\d{2})(\d)/, '$1.$2')
      newValue = newValue.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
      newValue = newValue.replace(/\.(\d{3})(\d)/, '.$1/$2')
      newValue = newValue.replace(/(\d{4})(\d)/, '$1-$2')
    }
    return newValue
  }

  searchDebouncer = debounce(() => {
    this.getShareholders(0, this.state.startQuantity, this.state.startQuantity)
  }, 1000)

  constructor(props) {
    super(props)
    this.state = {
      companyId: localInformation.getCompanyId(),
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      currentShareholderType: shareholderTypesOptions[0],
      currentGroupedType: groupedOptions[0],
      currentView: viewsOptions[0],
      groupList: [],
      isLoading: true,
      listLoading: false,
      offset: 0,
      quantity: 10,
      startQuantity: 20,
      limit: 10,
      searchInputForm: {
        id: 'searchInputForm',
        searchTerm: {
          id: 'searchTerm',
          inputType: 'text',
          value: '',
        },
      },
      shareholders: [],
      isShareholderModalOpen: false,
      isGroupModalOpen: false,
      shareholderInputForm: {
        id: 'shareholderInputForm',
        name: {
          id: 'name',
          inputType: 'text',
          value: '',
        },
        document: {
          id: 'document',
          inputType: 'text',
          value: '',
        },
        documentType: [
          {
            id: 0,
            group: 'documentType',
            idiomKey: 'filterByText',
            name: i18n.t('cnpj'),
            selected: true,
          },
          {
            id: 1,
            group: 'documentType',
            idiomKey: 'filterByTag',
            name: i18n.t('cpf'),
            selected: false,
          },
        ],
      },
      groupInputForm: {
        id: 'groupInputForm',
        name: {
          id: 'name',
          inputType: 'text',
          value: '',
        },
      },
      hasGroup: false,
      uploadModalVisibility: false,
      deleteClassificationModalVisibility: false,
      editClassificationModalVisibility: false,
      intervalId: 0,
      showGroupBatchWarning: false,
      groupBatchWarningMessage: '',
      lastGroupBatchStatus: -1,
      templateUrl: '',
      selectedFile: undefined,
      uploadProgress: 0,
      classificationsList: [],
      classificationId: '',
      importType: 'replace',
    }
    this.actionsDropdownOptions = [
      {
        name: i18n.t('components.actionsDropdownOptions.newGroup'),
        key: 0,
        action: this.openGroupModal,
        type: 'update',
      },
      {
        name: i18n.t('components.actionsDropdownOptions.update'),
        key: 1,
        action: this.onHandleUploadModal,
        type: 'update',
        requiredAdmin: true,
      },
      {
        name: i18n.t('components.actionsDropdownOptions.exportScreen'),
        key: 2,
        action: this.onHandleExportScreen,
        type: 'export',
      },
      {
        name: i18n.t('components.actionsDropdownOptions.exportGrouping'),
        key: 3,
        action: this.onHandleExportGrouping,
        type: 'export',
      },
      {
        name: i18n.t('components.actionsDropdownOptions.deleteGrouping'),
        key: 4,
        action: this.onHandleDeleteModal,
        type: 'delete',
        requiredAdmin: true,
      },
    ]
    const hasSpreadSheetPermission = hasScopePermission([
      'mzshareholders:shareholders:companies:id:spreadsheet:grouping:post',
      'mzshareholders:shareholders:companies:id:spreadsheet:grouping:delete',
      'mzshareholders:shareholders:companies:id:spreadsheet:grouping:backup:post',
      'mzshareholders:shareholders:companies:id:spreadsheet:grouping:process:post',
      'mzshareholders:shareholders:companies:id:spreadsheet:grouping:post',
    ])

    this.hasMergePermission = hasScopePermission(
      'mzshareholders:shareholders:companies:id:spreadsheet:grouping:merge:post'
    )

    this.filteredActionsDropdownOptions = this.actionsDropdownOptions.filter(
      (option) => hasSpreadSheetPermission || !option.requiredAdmin
    )
  }

  componentWillMount() {
    this.getTickers()
  }

  componentDidMount() {
    this.checkGroupBatchStatus()
    this.handleGetGroups()
    this.setPageAmount()
  }

  componentWillUnmount() {
    clearInterval(this.state.intervalId)
  }

  onFileUploaded = () => {
    this.setState({ showGroupBatchWarning: true }, () => {
      this.checkGroupBatchStatus()
    })
  }

  getGroupingTemplate = async () => {
    const { idiom } = this.state
    const response = await getGroupingTemplate({ language: idiom })

    doDownload(response)
  }

  getWillMount = () => {
    this.componentDidMount()
  }

  setPageAmount() {
    const { offset, quantity } = this.state
    let globalItemsAmount = localInformation.getPageItemsAmount()
    let globalSessionItem = JSON.parse(sessionInformation.getShareholderListingViewType())
    if (!globalItemsAmount) {
      globalItemsAmount = quantity
      localInformation.setPageItemsAmount(globalItemsAmount)
    }

    if (!globalSessionItem) {
      globalSessionItem = this.state.currentView

      sessionInformation.setShareholderListingViewType(JSON.stringify(globalSessionItem))
    }

    this.setState({ quantity: globalItemsAmount }, () =>
      this.getShareholders(offset, this.state.startQuantity, this.state.startQuantity)
    )
  }

  handleScroll = () => {
    const { offset, quantity, shareholders, limit } = this.state
    const scroll = document.querySelector('.scroll')

    const newOffsset = offset + 10
    const newQuantity = Number(quantity) + 10

    if (scroll.scrollTop + scroll.offsetHeight !== scroll.scrollHeight || Number(quantity) > shareholders.length) {
      return
    }
    this.setState({ offset: newOffsset, quantity: newQuantity, listLoading: true }, () => {
      scroll.removeEventListener('scroll', this.handleScroll)
      this.getShareholders(this.state.offset, limit, this.state.quantity)
    })
  }

  handleGroupModalTitleShareholderType = () => {
    const { currentFund } = this.state
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.FUND) return i18n.t('ownership.fund').toLowerCase()
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.INDIVIDUAL)
      return i18n.t('ownership.individual').toLowerCase()
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.UNKNOW) return ''

    return
  }

  onChangeViewsMode = (value) => {
    if (value.value === this.state.currentView.value) return

    sessionInformation.setShareholderListingViewType(JSON.stringify(value))

    this.setState(
      {
        currentView: value,
        isLoading: true,
      },
      () =>
        this.state.currentView.value === 'classification'
          ? this.getClassification(0, this.state.startQuantity)
          : this.getShareholders(0, this.state.startQuantity, this.state.startQuantity)
    )
  }

  onChangeShareholderType = (value) => {
    if (this.state.currentShareholderType.value === value.value) return
    this.setState(
      {
        currentShareholderType: value,
        isLoading: true,
      },
      () => this.getShareholders(0, this.state.startQuantity, this.state.startQuantity)
    )
  }

  onChangeGroupedType = (value) => {
    if (value.value === this.state.currentGroupedType.value) return

    this.setState(
      {
        currentGroupedType: value,
        isLoading: true,
      },
      () => this.getShareholders(0, this.state.startQuantity, this.state.startQuantity)
    )
  }

  inputOnChangeHandler = (event, inputId, formKey) => {
    const currentState = this.state[formKey]
    let inputValue = event.target.value
    if (inputId === 'document') {
      const pattern = /^[^a-z]*[0-9]+[^a-z]*$/g
      if (!pattern.test(inputValue) && inputValue.length > 0) {
        return
      }

      if (currentState.documentType[0].selected) {
        if (inputValue.length > 18) {
          return
        }

        inputValue = inputValue.replace(/^(\d{2})(\d)/, '$1.$2')
        inputValue = inputValue.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
        inputValue = inputValue.replace(/\.(\d{3})(\d)/, '.$1/$2')
        inputValue = inputValue.replace(/(\d{4})(\d)/, '$1-$2')
      } else {
        if (inputValue.length > 14) {
          return
        }

        inputValue = inputValue.replace(/(\d{3})(\d)/, '$1.$2')
        inputValue = inputValue.replace(/(\d{3})(\d)/, '$1.$2')
        inputValue = inputValue.replace(/(\d{3})(\d{1,2})$/, '$1-$2')
      }

      inputValue.replace(' ', '')
    }
    const updatedForm = inputChangedHandler(inputValue, inputId, currentState)

    this.setState(
      {
        [formKey]: updatedForm,
        isLoading: true,
      },
      () => {
        if (formKey === 'searchInputForm') {
          if (this.state[formKey][inputId].value.length >= 2) {
            event.persist()
            this.searchDebouncer()
          } else if (this.state[formKey][inputId].value.length === 0) {
            event.persist()
            this.searchDebouncer()
          }
        }
      }
    )
  }

  getTickers = () => {
    getTickers(this.state.companyId).then((res) => {
      if (res.success) {
        this.setState({
          tickers: res.data,
          currentTicker: res.data[0].tickerId,
        })
      }
    })
  }

  getClassification = async () => {
    const { companyId } = this.state

    try {
      const response = await getClassificationsList({ companyId })

      this.setState({
        classificationsList: response,
        isLoading: false,
      })
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
      }
    }
  }

  onHandleEditClassification = async (description) => {
    const { companyId, classificationId } = this.state

    try {
      await editClassification({ classificationId, companyId, description })

      this.props.createToast({
        title: i18n.t('success'),
        description: i18n.t('shareholders.editClassificationSuccess'),
        duration: 9000,
        type: 'success',
      })

      this.onCloseEditClassificationModal()

      this.getClassification()
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })

        this.onCloseEditClassificationModal()
      }
    }
  }

  onHandleDeleteClassification = async () => {
    const { companyId, classificationId } = this.state

    try {
      await deleteClassification({ classificationId, companyId })

      this.props.createToast({
        title: i18n.t('success'),
        description: i18n.t('shareholders.deleteClassificationSuccess'),
        duration: 9000,
        type: 'success',
      })

      this.onCloseDeleteClassificationModal()

      this.getClassification()
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        this.onCloseDeleteClassificationModal()
      }
    }
  }

  getShareholders = (offset, limit, quantity) => {
    const { companyId, currentShareholderType, currentView, searchInputForm, shareholders, currentGroupedType } =
      this.state

    const searchTerm = searchInputForm.searchTerm.value.length > 0 ? searchInputForm.searchTerm.value : ''

    const type =
      currentShareholderType != null ? (currentShareholderType.value === '0' ? '' : currentShareholderType.value) : ''
    getShareholdersNew(companyId, currentView.value, searchTerm, offset, limit, type, currentGroupedType.value).then(
      (res) => {
        if (offset === 0) {
          this.setState(
            {
              shareholders: res.data,
              isLoading: false,
              listLoading: false,
              offset,
              quantity,
            },
            () => {
              document.querySelector('.scroll').addEventListener('scroll', this.handleScroll)
            }
          )
        } else {
          const newShareholders = shareholders
          newShareholders.push(...res.data)
          this.setState(
            {
              shareholders: newShareholders,
              isLoading: false,
              listLoading: false,
              offset,
              quantity,
            },
            () => {
              ReactTooltip.rebuild()
              document.querySelector('.scroll').addEventListener('scroll', this.handleScroll)
            }
          )
        }
      }
    )
  }

  handleShareholderType = (e, id) => {
    const { shareholderInputForm } = this.state
    const selectedShareholderType = shareholderInputForm.documentType.map((radio) => {
      const newRadio = { ...radio, selected: radio.id === id }
      return newRadio
    })
    shareholderInputForm.document.value = ''
    const updatedShareholderType = {
      ...shareholderInputForm,
      documentType: selectedShareholderType,
    }

    this.setState({
      shareholderInputForm: updatedShareholderType,
    })
  }

  openShareholderModal = () => {
    this.setState({ isShareholderModalOpen: true })
  }

  closeShareholderModal = () => {
    const { shareholderInputForm } = this.state

    shareholderInputForm.document.value = ''
    shareholderInputForm.name.value = ''

    this.setState({ shareholderInputForm, isShareholderModalOpen: false }, () => this.handleShareholderType(null, 0))
  }

  submitShareholderModal = async (shareholderName, shareholderDocument, shareholderDocumentType) => {
    try {
      const { companyId } = this.state
      const document = shareholderDocument.replace(/\D/g, '')

      const { data } = await createShareholder(
        companyId,
        shareholderName,
        shareholderDocumentType,
        shareholderDocumentType,
        document
      )

      const shareholderId = data.shareholderId

      this.setState(
        {
          isLoading: true,
        },
        () => {
          this.getShareholders(0, this.state.startQuantity, this.state.startQuantity)
          this.closeShareholderModal()
        }
      )

      this.props.createToast({
        title: i18n.t('globals.toasts.newShareholder.success.title'),
        description: i18n.t('globals.toasts.newShareholder.success.description'),
        duration: 15000,
        type: 'success',
        buttons: (
          <LinkButton link={`${PATH}/ownership/${shareholderId}/simple/overview`}>
            {i18n.t('shareholderDetail')}
          </LinkButton>
        ),
      })
    } catch {
      this.props.createToast({
        title: i18n.t('globals.toasts.newShareholder.error.title'),
        description: i18n.t('globals.toasts.newShareholder.error.description'),
        duration: 15000,
        type: 'error',
      })
    }
  }

  openGroupModal = () => {
    this.setState({ isGroupModalOpen: true })
  }

  handleGroupedShareholderActionsToast = (shareholderGroupId, shareholderId) => {
    this.props.createToast({
      title: i18n.t('globals.groupedShareholderActionsToastfy.success.title'),
      description: i18n.t('globals.groupedShareholderActionsToastfy.success.message'),
      duration: 15000,
      type: 'success',
      buttons: (
        <>
          <LinkButton link={`${PATH}/ownership/${shareholderGroupId}/grouped/overview`}>
            {i18n.t('groupDetail')}
          </LinkButton>
          <LinkButton link={`${PATH}/ownership/${shareholderId}/simple/overview`}>
            {i18n.t('shareholderDetail')}
          </LinkButton>
        </>
      ),
    })
  }

  closeGroupModal = () => {
    const { groupInputForm } = this.state
    groupInputForm.name.value = ''

    this.setState({
      groupInputForm,
      isGroupModalOpen: false,
      isLoading: false,
      hasGroup: false,
    })
  }

  submitGroupModal = async (groupName) => {
    const { companyId } = this.state

    try {
      const result = await createGroup(companyId, groupName)

      if (!result.success) throw CreateNewGroupError

      this.setState({ isLoading: true, hasGroup: false })

      this.props.createToast({
        title: i18n.t('components.newGroupModal.createGroup.success.title'),
        description: i18n.t('components.newGroupModal.createGroup.success.message'),
        duration: 9000,
        type: 'success',
      })
    } catch (error) {
      this.props.createToast({
        title: i18n.t('globals.errors.createGroup.title'),
        description: i18n.t('globals.errors.createGroup.message'),
        duration: 9000,
        type: 'error',
      })
    } finally {
      this.getShareholders(0, this.state.startQuantity, this.state.startQuantity)
      this.closeGroupModal()
    }
  }

  onOpenAgglutinationModal = (item) => {
    this.setState({
      showAggModal: true,
      currentFund: item,
      currentFundId: item.shareholderId,
    })
  }

  closeAggModal = () => {
    this.setState({ showAggModal: false })
  }

  handleGetGroups = async () => {
    try {
      const { companyId } = this.state
      const shareholderGroups = await getGroups(companyId)

      const formatShareholderGroups = shareholderGroups.data.data.map((shareholderGroup) => ({
        label: shareholderGroup.name,
        value: shareholderGroup.shareholderGroupId,
      }))

      this.setState({
        groupList: formatShareholderGroups,
      })
    } catch {
      return this.props.createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        type: 'error',
      })
    }
  }

  onCreateGroup = async (groupName) => {
    const { companyId, currentFund, currentFundId, startQuantity } = this.state
    try {
      this.setState({ isLoading: true })

      const res = await postCreateGroupAndGroupShareholder({
        companyId,
        groupName,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      this.setState(
        {
          showAggModal: false,
          listLoading: true,
        },
        () => {
          this.getShareholders(0, startQuantity, startQuantity)
          this.handleGetGroups()
        }
      )

      this.handleGroupedShareholderActionsToast(res.data.shareholderGroupId, currentFundId)
    } catch (error) {
      this.props.createToast({
        title: i18n.t('globals.errors.createGroup.title'),
        description: i18n.t('globals.errors.createGroup.message'),
        duration: 9000,
        type: 'error',
      })

      if (error.response.status === 422) {
        this.setState({ showAggModal: false })
      }
    }
  }

  onVinculateGroup = async (shareholderGroup) => {
    const { companyId, currentFund, currentFundId } = this.state
    const shareholderGroupId = shareholderGroup.value
    try {
      await postGroupShareholder({
        companyId,
        shareholderGroupId,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      this.setState({
        showAggModal: false,
        showAggSuccessModal: true,
        listLoading: true,
        currentGroupId: shareholderGroupId,
      })

      this.handleGroupedShareholderActionsToast(shareholderGroupId, currentFundId)

      this.getShareholders(0, this.state.startQuantity, this.state.startQuantity)
    } catch (error) {
      if (error instanceof BaseError) {
        return this.props.createToast({
          type: 'error',
          title: err.title,
          description: err.message,
        })
      }
      this.props.createToast({
        title: i18n.t('globals.errors.vinculateGroup.title'),
        description: i18n.t('globals.errors.vinculateGroup.message'),
        duration: 9000,
        type: 'error',
      })

      if (error.response.status === 422) {
        this.setState({ groupedError: true, showAggModal: false })
      }
    } finally {
      this.setState({ isLoading: false })
    }
  }

  onHandleUploadModal = () => {
    this.setState((prevState) => ({
      uploadModalVisibility: !prevState.uploadModalVisibility,
    }))
  }

  onHandleDeleteModal = () => {
    this.setState((prevState) => ({
      deleteModalVisibility: !prevState.deleteModalVisibility,
    }))
  }

  onOpenEditClassificationModal = (classificationId) => {
    this.setState({
      editClassificationModalVisibility: true,
      classificationId: classificationId,
    })
  }

  onCloseEditClassificationModal = () => {
    this.setState({
      editClassificationModalVisibility: false,
      classificationId: '',
    })
  }

  onOpenDeleteClassificationModal = (classificationId) => {
    this.setState({
      deleteClassificationModalVisibility: true,
      classificationId: classificationId,
    })
  }

  onCloseDeleteClassificationModal = () => {
    this.setState({
      deleteClassificationModalVisibility: false,
      classificationId: '',
    })
  }

  exportShareholdersScreen = async () => {
    const result = await exportShareholders({
      companyId: this.state.companyId,
      shareholderType: this.state.currentShareholderType.value,
      groupedType: this.state.currentGroupedType.value,
      language: this.state.idiom,
      search: this.state.searchInputForm.searchTerm.value,
    })

    return result
  }

  exportShareholdersGroupsScreen = async () => {
    const result = await exportShareholdersGroups({
      companyId: this.state.companyId,
      language: this.state.idiom,
      search: this.state.searchInputForm.searchTerm.value,
    })

    return result
  }

  onHandleExportScreen = async () => {
    try {
      const result =
        this.state.currentView.value === 'shareholder'
          ? await this.exportShareholdersScreen()
          : await this.exportShareholdersGroupsScreen()

      if (!result.success) throw ExportFailedError

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.export.error.title'),
        description: i18n.t('globals.export.error.message'),
      })
    }
  }

  onHandleExportGrouping = async () => {
    try {
      const result = await exportShareholdersGrouping({
        companyId: this.state.companyId,
        language: this.state.idiom,
      })

      if (!result.success) throw ExportFailedError

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.export.error.title'),
        description: i18n.t('globals.export.error.message'),
      })
    }
  }

  onHandleDeleteGrouping = async () => {
    const today = new Date()
    try {
      await deleteSpreadsheetGrouping({
        companyId: this.state.companyId,
        referenceDate: formatDateToString(today),
        language: this.state.idiom,
      })

      this.props.createToast({
        title: i18n.t('globals.backup.success.title'),
        description: i18n.t('globals.backup.success.message'),
        duration: 9000,
        type: 'success',
        buttons: <GoToHistoryButton />,
      })

      this.onHandleDeleteModal()
    } catch (err) {
      this.props.createToast({
        type: 'error',
        title: err.title,
        description: err.message,
      })
    }
  }

  handleProgressFileUpload = (progressEvent) => {
    const { loaded, total } = progressEvent
    const progress = total > 0 ? Math.round((loaded * 100) / total) : 0

    this.setState({
      uploadProgress: progress,
    })
  }

  handleSelectedFile = (file) => {
    if (file === this.state.selectedFile) return
    this.setState({
      selectedFile: file,
    })
  }

  handleImportTypeChange = (e) => {
    this.setState({
      importType: e.target.checked ? 'merge' : 'replace',
    })
  }

  handleUploadFile = async () => {
    const today = new Date()
    try {
      if (!this.state.selectedFile) return
      this.onFileUploaded()
      await postSpreadsheetGrouping({
        file: this.state.selectedFile,
        companyId: this.state.companyId,
        tickerId: this.state.currentTicker,
        referenceDate: formatDateToString(today),
        language: this.state.idiom,
        importType: this.hasMergePermission ? this.state.importType : 'replace',
        onUploadProgress: this.handleProgressFileUpload,
      })

      this.props.createToast({
        title: i18n.t('globals.uploadToastfy.success.title'),
        description: i18n.t('globals.uploadToastfy.success.message'),
        type: 'success',
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      this.props.createToast({
        type: 'error',
        title: err.title,
        description: err.message,
      })
    } finally {
      this.setState({
        selectedFile: undefined,
        uploadProgress: 0,
        uploadModalVisibility: false,
      })
    }
  }

  removeInterval(intervalId, lastGroupBatchStatus, groupBatchWarningMessage = '') {
    clearInterval(intervalId)

    const showGroupBatchWarning = lastGroupBatchStatus === groupBatchStatus.ErrorParsingFile

    const { offset, startQuantity } = this.state
    this.setState(
      (prevState) => ({
        ...prevState,
        showGroupBatchWarning,
        groupBatchWarningMessage,
        intervalId: 0,
        lastGroupBatchStatus,
      }),
      () => {
        this.getShareholders(offset, startQuantity, startQuantity)
      }
    )
  }

  async checkGroupBatchStatus() {
    const { companyId, intervalId } = this.state
    let { lastGroupBatchStatus } = this.state
    const response = await getGroupBatch({ companyId })
    const status = response?.status

    if (lastGroupBatchStatus === status) return

    lastGroupBatchStatus = status

    const getWarningMessage = (statusParam) => {
      const WarningMessage = {
        1: i18n.t('shareholders.uploadBaseWarning'),
        2: i18n.t('shareholders.uploadBaseWarning'),
        3: i18n.t('shareholders.uploadBaseWarning'),
        99: i18n.t('shareholders.uploadBaseError'),
        default: i18n.t('shareholders.uploadBaseError'),
      }
      return WarningMessage[statusParam || 'default']
    }

    const warningMessage = getWarningMessage(status)

    if (Number(status) < groupBatchStatus.Imported) {
      this.createInterval(intervalId, lastGroupBatchStatus, warningMessage)
    } else {
      this.removeInterval(intervalId, lastGroupBatchStatus, warningMessage)
    }
  }

  createInterval(intervalId, lastGroupBatchStatus, groupBatchWarningMessage = '') {
    if (intervalId) return

    const newIntervalId = setInterval(() => this.checkGroupBatchStatus(), fiveSeconds)

    this.setState((prevState) => ({
      ...prevState,
      showGroupBatchWarning: true,
      groupBatchWarningMessage,
      intervalId: newIntervalId,
      lastGroupBatchStatus,
    }))
  }

  render() {
    const {
      currentShareholderType,
      currentView,
      classificationsList,
      groupList,
      isLoading,
      listLoading,
      isShareholderModalOpen,
      isGroupModalOpen,
      searchInputForm,
      shareholderInputForm,
      shareholders,
      currentGroupedType,
      hasGroup,
      uploadModalVisibility,
      deleteModalVisibility,
      deleteClassificationModalVisibility,
      editClassificationModalVisibility,
      selectedFile,
      uploadProgress,
    } = this.state

    const getCurrentViewLink = (view, shareholderId, shareholderGroupId) => {
      const currentViewLinks = {
        classification: `${PATH}/companies/${shareholderId}/classifications/`,
        shareholder: `${PATH}/ownership/${shareholderId}/simple/overview`,
        shareholderGroup: `${PATH}/ownership/${shareholderGroupId}/grouped/overview`,
        default: `${PATH}/ownership/${shareholderGroupId}/grouped/overview`,
      }

      return currentViewLinks[view || 'default']
    }

    return (
      <div className="shareholders-wrapper listing new">
        <div className="shareholders-content dark">
          <Header>
            <Header.Content>
              <Header.Item>
                <Header.Label>{i18n.t('viewType')}</Header.Label>
                <Dropdown options={viewsOptions} onChange={this.onChangeViewsMode} selected={currentView} />
              </Header.Item>
              {currentView.value === 'shareholder' && (
                <Header.Item>
                  <Header.Label>{i18n.t('type')}</Header.Label>
                  <Dropdown
                    options={shareholderTypesOptions}
                    onChange={this.onChangeShareholderType}
                    selected={currentShareholderType || shareholderTypesOptions[0]}
                  />
                </Header.Item>
              )}

              {currentView.value === 'shareholder' && (
                <Header.Item>
                  <Header.Label>{i18n.t('groupedLabel')}</Header.Label>
                  <Dropdown
                    options={groupedOptions}
                    onChange={this.onChangeGroupedType}
                    selected={currentGroupedType || i18n.t('groupedOptions[0]')}
                  />
                </Header.Item>
              )}

              <Header.Item $alignRight style={{ minWidth: '330px', marginRight: '20px' }}>
                <Header.Search
                  name="searchInputForm"
                  type={searchInputForm.searchTerm.inputType}
                  placeholder={i18n.t('search')}
                  value={searchInputForm.searchTerm.value}
                  onChange={(e) => this.inputOnChangeHandler(e, searchInputForm.searchTerm.id, searchInputForm.id)}
                  autoComplete="false"
                />
              </Header.Item>
            </Header.Content>
            <Header.ButtonGroup style={{ marginLeft: 0 }}>
              <Buttons.Primary disabled={isLoading} onClick={() => this.openShareholderModal()}>
                {i18n.t('newShareholder')}
              </Buttons.Primary>
              <ActionsDropdown
                title={i18n.t('components.actionsDropdownOptions.actions')}
                options={this.filteredActionsDropdownOptions}
              />
            </Header.ButtonGroup>
          </Header>

          {isLoading ? (
            <Loading />
          ) : (
            <div className="scroll shareholder-listing-content">
              <Table>
                <Table.THead>
                  <Table.TR>
                    {currentView.value === 'shareholder' && <Table.TH></Table.TH>}
                    <Table.TH>{i18n.t('name')}</Table.TH>
                    {currentView.value === 'shareholder' && <Table.TH>{i18n.t('groupType')}</Table.TH>}
                    {currentView.value === 'shareholder' && <Table.TH>{i18n.t('type')}</Table.TH>}
                    {currentView.value === 'shareholder' && <Table.TH>{i18n.t('document')}</Table.TH>}
                    {currentView.value === 'classification' && <Table.TH>{i18n.t('actions')}</Table.TH>}
                  </Table.TR>
                </Table.THead>
                <Table.TBody>
                  {currentView.value === 'classification' ? (
                    <>
                      {classificationsList.length > 0 ? (
                        <>
                          {classificationsList.map((classification) => {
                            return (
                              <Table.TR key={classification.classificationId}>
                                <Table.TD
                                  onClick={() => this.onOpenEditClassificationModal(classification.classificationId)}
                                  data-tip={classification.description}
                                  data-for="classification-listing"
                                >
                                  {classification.description}
                                </Table.TD>
                                <Table.TD>
                                  <div>
                                    <Icons.Edit
                                      data-tip={i18n.t('edit')}
                                      data-for="classification-actions"
                                      size={20}
                                      onClick={() =>
                                        this.onOpenEditClassificationModal(classification.classificationId)
                                      }
                                    />
                                    <Icons.Trash
                                      data-tip={i18n.t('delete')}
                                      data-for="classification-actions"
                                      size={20}
                                      onClick={() =>
                                        this.onOpenDeleteClassificationModal(classification.classificationId)
                                      }
                                    />
                                  </div>
                                </Table.TD>
                              </Table.TR>
                            )
                          })}
                          <ReactTooltip place="top" delayShow={500} id="classification-listing" />
                          <ReactTooltip place="left" delayShow={200} id="classification-actions" />
                        </>
                      ) : (
                        <Table.TR>
                          <Table.TD colSpan={2}>{i18n.t('noResults')}</Table.TD>
                        </Table.TR>
                      )}
                      {listLoading && (
                        <Table.TR>
                          <Table.TD colSpan={2}>
                            <Loading />
                          </Table.TD>
                        </Table.TR>
                      )}
                    </>
                  ) : (
                    <>
                      {shareholders.length > 0 ? (
                        <>
                          {shareholders.map((shareholder, i) => {
                            return (
                              <Table.TR
                                key={
                                  currentView.value === 'shareholder'
                                    ? `${shareholder.shareholderId}-${i}`
                                    : `${shareholder.shareholderGroupId}-${i}`
                                }
                              >
                                {currentView.value === 'shareholder' && (
                                  <Table.TD>
                                    {!shareholder.shareholderGroupName && (
                                      <GroupIcon
                                        size={18}
                                        opacity={0.3}
                                        onClick={() => this.onOpenAgglutinationModal(shareholder)}
                                        data-tip={i18n.t('groupLabel')}
                                        data-for="shareholder-listing"
                                      />
                                    )}
                                  </Table.TD>
                                )}
                                <Table.TD>
                                  <Link
                                    to={getCurrentViewLink(
                                      currentView.value,
                                      shareholder.shareholderId,
                                      shareholder.shareholderGroupId
                                    )}
                                    data-tip={shareholder.name}
                                    data-for="shareholder-listing"
                                  >
                                    {shareholder.name}
                                  </Link>
                                </Table.TD>
                                {currentView.value === 'shareholder' && (
                                  <Table.TD>
                                    {!shareholder.shareholderGroupName ? (
                                      <GroupIcon
                                        size={18}
                                        opacity={0.3}
                                        onClick={() => this.onOpenAgglutinationModal(shareholder)}
                                        data-tip={i18n.t('groupLabel')}
                                        data-for="shareholder-listing"
                                      />
                                    ) : (
                                      <span>{shareholder.shareholderGroupName}</span>
                                    )}
                                  </Table.TD>
                                )}
                                {currentView.value === 'shareholder' && (
                                  <Table.TD>
                                    {shareholder.shareholderType === 1
                                      ? i18n.t('fund')
                                      : shareholder.shareholderType === 2
                                        ? i18n.t('private')
                                        : i18n.t('unknown')}
                                  </Table.TD>
                                )}
                                {currentView.value === 'shareholder' && (
                                  <Table.TD>{BaseShareholderListing.cpfCnpj(shareholder.document)}</Table.TD>
                                )}
                              </Table.TR>
                            )
                          })}
                          <ReactTooltip place="top" delayShow={500} id="shareholder-listing" />
                        </>
                      ) : (
                        <Table.TR>
                          <Table.TD colSpan={currentView.value === 'shareholder' ? 5 : 1}>
                            {i18n.t('noResults')}
                          </Table.TD>
                        </Table.TR>
                      )}
                      {listLoading && (
                        <Table.TR>
                          <Table.TD colSpan={currentView.value === 'shareholder' ? 5 : 1}>
                            <Loading />
                          </Table.TD>
                        </Table.TR>
                      )}
                    </>
                  )}
                </Table.TBody>
              </Table>
            </div>
          )}
        </div>

        <NewShareholderModal
          title={i18n.t('newShareholder')}
          onConfirm={this.submitShareholderModal}
          show={isShareholderModalOpen}
          onClose={() => this.closeShareholderModal()}
        />

        <NewGroupModal
          visibility={isGroupModalOpen}
          title={i18n.t('components.groupModal.newGroup')}
          onCreateGroup={this.submitGroupModal}
          onClose={this.closeGroupModal}
          groupExists={hasGroup}
        />

        <GroupModal
          title={`${i18n.t('ownership.agroup')} ${this.handleGroupModalTitleShareholderType()}`}
          visibility={this.state.showAggModal}
          options={groupList}
          onClose={this.closeAggModal}
          onCreateGroup={this.onCreateGroup}
          onConfirm={this.onVinculateGroup}
        />

        <UploadModal
          show={uploadModalVisibility}
          onConfirm={this.handleUploadFile}
          onClose={this.onHandleUploadModal}
          selectedFile={selectedFile}
          onSelectedFile={this.handleSelectedFile}
          title={i18n.t('components.uploadModal.Title')}
          templateLabel={i18n.t('components.uploadModal.DownloadWorkSheet')}
          onTemplateClick={this.getGroupingTemplate}
          message={`<p>${i18n.t('components.uploadModal.InfoText')}</p><p>${i18n.t(
            'components.uploadModal.InfoText2'
          )}</p><p>${i18n.t('components.uploadModal.InfoText3')}</p>`}
          progress={uploadProgress}
          acceptedFiles={acceptedFiles}
          confirmButtonLabel={i18n.t('upload')}
        >
          {this.hasMergePermission && (
            <Inputs.Label $horizontalAlignment="center" style={{ padding: '16px 0' }}>
              <Inputs.Checkbox
                checked={this.state.importType === 'merge'}
                handleChange={this.handleImportTypeChange}
                name="mergeData"
                value="merge"
              />
              <Inputs.Text style={{ color: theme.colors.neutral.white, fontSize: '16px' }}>
                {i18n.t('shareholders.mergeDataLabel')}
              </Inputs.Text>
            </Inputs.Label>
          )}
        </UploadModal>

        <DeleteModal
          show={deleteModalVisibility}
          onClose={this.onHandleDeleteModal}
          onDelete={this.onHandleDeleteGrouping}
          message={i18n.t('shareholders.deleteGroupingMessage')}
          cancelButtonLabel={i18n.t('components.deleteModal.cancelButton')}
          deleteButtonLabel={i18n.t('components.deleteModal.deleteButton')}
        />
        <DeleteModal
          show={deleteClassificationModalVisibility}
          onClose={this.onCloseDeleteClassificationModal}
          width="532px"
          onDelete={this.onHandleDeleteClassification}
          message={i18n.t('shareholders.deleteClassificationMessage')}
          cancelButtonLabel={i18n.t('components.deleteModal.cancelButton')}
          deleteButtonLabel={i18n.t('components.deleteModal.deleteButton')}
        />
        <EditModal
          show={editClassificationModalVisibility}
          onClose={this.onCloseEditClassificationModal}
          onConfirm={this.onHandleEditClassification}
          label={i18n.t('name')}
          title={i18n.t('shareholders.editClassificationTitle')}
          placeholder={i18n.t('shareholders.classificationName')}
        />
      </div>
    )
  }
}

BaseShareholderListing.contextType = StoreContext

const ShareholderListing = hocWrapper(BaseShareholderListing)

export default ShareholderListing
