import React, { Component } from 'react'

import { i18n } from 'translate'

import SummaryBarChart from './SummaryBarChart'

class SummaryTopSellersCountry extends Component {
  renderChart = () => {
    const { idiom, base, categories } = this.props
    return base ? (
      <SummaryBarChart
        categories={categories}
        data={base}
        seriesName={i18n.t('topSellersPerCountrySeriesName')}
        title={i18n.t('topSellersPerCountry')}
        yAxisText={i18n.t('topSellersPerCountryYAxis')}
        idiom={idiom}
        height={this.props.height}
      />
    ) : null
  }

  render() {
    const { isLoading } = this.props

    if (isLoading) {
      return (
        <div className="top-sellers-chart">
          <div className="lds-dual-ring">
            <div />
          </div>
        </div>
      )
    }

    return <div className="top-sellers-chart">{this.renderChart()}</div>
  }
}

export default SummaryTopSellersCountry
