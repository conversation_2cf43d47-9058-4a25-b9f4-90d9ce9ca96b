import React, { Component } from 'react'

import { i18n } from 'translate'

import SummaryBarChart from './SummaryBarChart'

class SummaryTopHoldersCountry extends Component {
  renderChart = () => {
    const { idiom, base, categories } = this.props
    return base ? (
      <SummaryBarChart
        categories={categories}
        data={base}
        seriesName={i18n.t('stockQuantity')}
        title={i18n.t('topShareholdersPerCountry')}
        yAxisText={i18n.t('topShareholdersPerCountryYAxis')}
        idiom={idiom}
        height={this.props.height}
      />
    ) : null
  }

  render() {
    const { isLoading } = this.props

    if (isLoading) {
      return (
        <div className="top-holders-chart">
          <div className="lds-dual-ring">
            <div />
          </div>
        </div>
      )
    }

    return <div className="top-holders-chart">{this.renderChart()}</div>
  }
}

export default SummaryTopHoldersCountry
