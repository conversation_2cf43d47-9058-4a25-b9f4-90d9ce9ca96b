import React, { Component } from 'react'

import { i18n } from 'translate'

import 'assets/styles/pages/_summary.scss'
import { Buttons, theme } from '@mz-codes/design-system'

import {
  getTickers,
  getShareholdersGroupedByType,
  getNationalityPercentage,
  getTopNewHolders,
  getZeroedOut,
  getSummaryTopBuyers,
  getSummaryTopSellers,
  getSummaryTopHolders,
  getTopBuyersByCountry,
  getTopSellersByCountry,
  getTopShareholderByCountry,
  generateSummaryReport,
} from 'client'
import { localInformation, defineTheme, dateFromUTC, formatDateToString } from 'utils'
import { COMMON, PATH } from 'consts'

import { GroupModal, UploadModal } from 'components/modals'
import { GoToHistoryButton, LinkButton } from 'components'

import {
  // getSmartGroupingSummary,
  retrieveBaseSummary,
  getAvailableDates,
  getGroups,
  hocWrapper,
  postCreateGroupAndGroupShareholder,
  postGroupShareholder,
  postUploadShareholderBase,
} from 'hooks'

import { StoreContext } from 'contexts/store'
import { getCustomerId } from 'globals/storages/locals'
import { SummaryShareholdersBase } from './shareholder-base'
import SummaryShareholderByType from './SummaryShareholderByType'
import SummaryStocksByNationality from './SummaryStocksByNationality'
import SummaryHeader from './SummaryHeader'
import SummaryNewHolders from './SummaryNewHolders'
import SummaryZeroedOut from './SummaryZeroedOut'
import SummaryTopBuyers from './SummaryTopBuyers'
import SummaryTopBuyersCountry from './SummaryTopBuyersCountry'
import SummaryTopSellers from './SummaryTopSellers'
import SummaryTopSellersCountry from './SummaryTopSellersCountry'
import SummaryTopHolders from './SummaryTopHolders'
import SummaryTopHoldersCountry from './SummaryTopHoldersCountry'
import { SHAREHOLDER_TYPES } from 'types/shareholders'

class BaseSummary extends Component {
  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      companyId: localInformation.getCompanyId(),
      vision: {
        value: 1,
        label: i18n.t('grouped'),
      },
      tickers: [],
      noData: false,
      currentTicker: null,
      customerId: null,
      groupList: [],
      showMapModal: false,
      isPageLoading: true,
      isNewHoldersLoading: true,
      fromAlbert: false,
      currentNewHolders: [],
      currentZeroedOut: [],
      isZeroedOutLoading: true,
      currentFund: null,
      showAggModal: false,
      nationalityChartData: null,
      nationalityChartDataLoading: true,
      shareholderChartData: null,
      shareholderChartDataLoading: true,
      shareholderBase: null,
      shareholderBaseLoading: true,
      seriesNameTopHoldersCountry: [],
      seriesDataTopHoldersCountry: [],
      seriesTopHoldersCountryLoading: true,
      seriesNameTopBuyersCountry: [],
      seriesDataTopBuyersCountry: [],
      seriesTopBuyersCountryLoading: true,
      seriesNameTopSellersCountry: [],
      seriesDataTopSellersCountry: [],
      seriesTopSellersCountryLoading: true,
      currentTopBuyers: [],
      isTopBuyersLoading: true,
      currentTopSellers: [],
      isTopSellersLoading: true,
      currentTopHolders: [],
      isTopHoldersLoading: true,
      topQuantityOptions: COMMON.topQuantitySummary,
      selectedTopQuantity: COMMON.topQuantitySummary[0],
      seeAll: false,
      selectedFile: undefined,
      uploadProgress: 0,
      // smartGroupingData: null,
      datesAvailable: [],
    }
  }

  componentDidMount() {
    const customerId = getCustomerId()
    // const screenWidth = document.documentElement.clientWidth

    this.setState({
      customerId,
      // screenWidth
    })

    if (!this.state.companyId) return

    this.getTickers()
    this.handleGetGroups()
  }

  async componentDidUpdate(prevProps, prevState) {
    if (prevState.currentTicker !== this.state.currentTicker) {
      this.getAvailableDates(this.state.currentTicker.value)
      // .then(() => {
      //   !this.state.noData && this.getSmartGroupingData()
      // })
    }

    if (
      (prevState.currentDate !== this.state.currentDate ||
        prevState.vision !== this.state.vision ||
        prevState.selectedTopQuantity !== this.state.selectedTopQuantity) &&
      prevState.datesAvailable === this.state.datesAvailable
    ) {
      if (!this.state.seeAll) {
        this.getStartInfos()
      } else {
        this.getAllInfos()
      }
    }

    if (
      prevState.nationalityChartData !== this.state.nationalityChartData ||
      prevState.shareholderChartData !== this.state.shareholderChartData
    ) {
      this.setState({
        nationalityChartDataLoading: false,
        shareholderChartDataLoading: false,
      })
    }
  }

  toggleSummaryLoadingState = () => {
    this.setState({
      isNewHoldersLoading: !this.isNewHoldersLoading,
      isZeroedOutLoading: !this.isZeroedOutLoading,
      nationalityChartDataLoading: !this.nationalityChartDataLoading,
      shareholderChartDataLoading: !this.shareholderChartDataLoading,
      shareholderBaseLoading: !this.shareholderBaseLoading,
      isTopBuyersLoading: !this.isTopBuyersLoading,
      isTopSellersLoading: !this.isTopSellersLoading,
      isTopHoldersLoading: !this.isTopHoldersLoading,
      seriesTopBuyersCountryLoading: !this.seriesTopBuyersCountryLoading,
      seriesTopSellersCountryLoading: !this.seriesTopSellersCountryLoading,
      seriesTopHoldersCountryLoading: !this.seriesTopHoldersCountryLoading,
    })
  }

  // async getSmartGroupingData() {
  //   try {
  //     const smartGroupingData = await getSmartGroupingSummary({ companyId: this.state.companyId })
  //     this.setState({ smartGroupingData: smartGroupingData.data })
  //   } catch (e) {
  //     console.log(e)
  //   }
  // }

  getWillMount = () => {
    this.componentDidMount()
  }

  getStartInfos = () => {
    this.getBaseSummary(this.state.currentTicker.value)
    this.getShareholdersGroupedByType(this.state.currentTicker.value)
    this.getNationalityPercentage(this.state.currentTicker.value)
  }

  getMoreInfos = () => {
    this.getNewHolders(this.state.currentTicker.value, this.state.startDate, this.state.endDate)
    this.getZeroedOut(this.state.currentTicker.value, this.state.startDate, this.state.endDate)
    this.getSummaryTopBuyers(this.state.currentTicker.value)
    this.getSummaryTopSellers(this.state.currentTicker.value)
    this.getSummaryTopHolders(this.state.currentTicker.value)
    this.getTopBuyersByCountry(this.state.currentTicker.value)
    this.getTopSellersByCountry(this.state.currentTicker.value)
    this.getTopShareholderByCountry(this.state.currentTicker.value)
  }

  getAllInfos = () => {
    this.getBaseSummary(this.state.currentTicker.value)
    this.getShareholdersGroupedByType(this.state.currentTicker.value)
    this.getNationalityPercentage(this.state.currentTicker.value)
    this.getNewHolders(this.state.currentTicker.value, this.state.startDate, this.state.endDate)
    this.getZeroedOut(this.state.currentTicker.value, this.state.startDate, this.state.endDate)
    this.getSummaryTopBuyers(this.state.currentTicker.value)
    this.getSummaryTopSellers(this.state.currentTicker.value)
    this.getSummaryTopHolders(this.state.currentTicker.value)
    this.getTopBuyersByCountry(this.state.currentTicker.value)
    this.getTopSellersByCountry(this.state.currentTicker.value)
    this.getTopShareholderByCountry(this.state.currentTicker.value)
  }

  getTickers = () => {
    getTickers(this.state.companyId).then((res) => {
      if (res.success) {
        const options = res.data.map((ticker) => ({
          label: ticker.label,
          value: ticker.tickerId,
        }))

        this.setState({
          tickers: options,
          currentTicker: res.data.length > 0 ? options[0] : null,
        })
      }
    })
  }

  getAvailableDates = (tickerId) => {
    const { companyId } = this.state
    return getAvailableDates({ companyId, tickerId }).then((res) => {
      const parsedDates = res.sort((a, b) => (a > b ? 1 : -1)).map((date) => dateFromUTC(date))

      const lastAvailableDate = parsedDates[parsedDates.length - 1]

      if (!lastAvailableDate) {
        this.props.createToast({
          type: 'alert',
          title: i18n.t('globals.infos.noBaseListFound.title'),
          description: i18n.t('globals.infos.noBaseListFound.message'),
        })

        return this.setState({
          datesAvailable: parsedDates,
          isPageLoading: false,
          showMapModal: true,
          noData: true,
        })
      }

      const formatCurrentDate = formatDateToString(lastAvailableDate)

      return this.setState(
        {
          currentDate: formatCurrentDate,
          datesAvailable: parsedDates,
          startDate: this.calcStartDate(lastAvailableDate, parsedDates),
          endDate: lastAvailableDate,
          isPageLoading: false,
          noData: false,
        },
        () => {
          if (!this.state.seeAll) {
            this.getStartInfos()
          } else {
            this.getAllInfos()
          }
        }
      )
    })
  }

  onChangeDate = (date) => {
    const { datesAvailable, endDate } = this.state
    if (date === endDate) return

    const formatCurrentDate = formatDateToString(date)

    this.toggleSummaryLoadingState()

    this.setState(
      {
        currentDate: formatCurrentDate,
        startDate: this.calcStartDate(date, datesAvailable),
        endDate: date,
      },
      () => {
        if (!this.state.seeAll) {
          this.getStartInfos()
        } else {
          this.getAllInfos()
        }
      }
    )
  }

  onChangeTopQuantity = (selectedTopQuantity) => {
    if (selectedTopQuantity.value === this.state.selectedTopQuantity.value) {
      return
    }

    this.toggleSummaryLoadingState()
    this.setState({ selectedTopQuantity })
  }

  onChangeStockType = (value) => {
    if (this.state.currentTicker === value) return
    this.toggleSummaryLoadingState()
    this.setState({ currentTicker: value })
  }

  onChangeBaseMode = (viewType) => {
    const { value } = viewType
    if (this.state.vision.value === value) return

    this.toggleSummaryLoadingState()

    const fromAlbert = value === 2

    this.setState({ vision: viewType, fromAlbert })
  }

  onOpenUploaderModal = () => {
    this.setState({ showMapModal: true })
  }

  getBaseSummary = async (tickerId) => {
    const { companyId } = this.state

    try {
      const { data } = await retrieveBaseSummary({ companyId, tickerId, referenceDate: this.state.currentDate })
      this.setState({ shareholderBase: data })
    } catch (error) {
      this.setState({ shareholderBase: null })
    } finally {
      this.setState({ shareholderBaseLoading: false })
    }
  }

  getShareholdersGroupedByType = (tickerId) => {
    getShareholdersGroupedByType(this.state.companyId, tickerId, this.state.currentDate).then((res) => {
      const filter = res.data.filter((x) => x.count !== '0')
      const data = filter.map((item) => {
        return {
          y: parseInt(item.count, 10),
          name:
            item.shareholderType === 'FUND'
              ? i18n.t('fund')
              : item.shareholderType === 'PRIVATE'
                ? i18n.t('private')
                : i18n.t('unknown'),
          color:
            item.shareholderType === 'FUND'
              ? theme.colors.neutral.grey[500]
              : item.shareholderType === 'PRIVATE'
                ? theme.colors.primary.base
                : theme.colors.neutral.grey[300],
        }
      })
      this.setState({ shareholderChartData: data })
    })
  }

  getNationalityPercentage = (tickerId) => {
    getNationalityPercentage(this.state.companyId, tickerId, this.state.currentDate).then((res) => {
      const filter = res.filter((x) => x.count !== '0')
      const chartData = filter.map((item) => {
        return {
          y: parseInt(item.count, 10),
          name:
            item.type === 'br' ? i18n.t('national') : item.type === 'foreign' ? i18n.t('foreign') : i18n.t('unknown'),
          color:
            item.type === 'br'
              ? theme.colors.primary.base
              : item.type === 'foreign'
                ? theme.colors.neutral.grey[500]
                : theme.colors.neutral.grey[300],
        }
      })

      this.setState({ nationalityChartData: chartData })
    })
  }

  getNewHolders = (tickerId, firstDate, secondDate) => {
    const viewType = {
      1: 'grouped',
      2: 'albert',
      3: 'simple',
    }

    getTopNewHolders({
      companyId: this.state.companyId,
      tickerId,
      referenceDateStart: formatDateToString(firstDate),
      referenceDateEnd: formatDateToString(secondDate),
      limit: this.state.selectedTopQuantity.value,
      fromAlbert: this.state.fromAlbert,
      shareholderType: undefined,
      viewType: viewType[this.state.vision.value],
    }).then((res) => {
      this.setState({
        currentNewHolders: res.positions,
        isNewHoldersLoading: false,
      })
    })
  }

  getZeroedOut = (tickerId, referenceDateStart, referenceDateEnd) => {
    const {
      companyId,
      vision,
      selectedTopQuantity: { value: limit },
    } = this.state

    const viewType = {
      1: 'grouped',
      2: 'albert',
      3: 'simple',
    }

    getZeroedOut({
      companyId,
      tickerId,
      referenceDateStart: formatDateToString(referenceDateStart),
      referenceDateEnd: formatDateToString(referenceDateEnd),
      limit,
      viewType: viewType[vision.value],
    }).then((res) => {
      this.setState({
        currentZeroedOut: res.positions,
        isZeroedOutLoading: false,
      })
    })
  }

  getSummaryTopBuyers = (tickerId) => {
    const viewType = {
      1: 'grouped',
      2: 'albert',
      3: 'simple',
    }

    getSummaryTopBuyers(
      this.state.companyId,
      tickerId,
      this.state.currentDate,
      this.state.selectedTopQuantity.value,
      viewType[this.state.vision.value]
    )
      .then((res) => {
        this.setState({ currentTopBuyers: res, isTopBuyersLoading: false })
      })
      .catch(() => {
        this.setState({ currentTopBuyers: [], isTopBuyersLoading: false })
      })
  }

  getSummaryTopSellers = (tickerId) => {
    const viewType = {
      1: 'grouped',
      2: 'albert',
      3: 'simple',
    }

    getSummaryTopSellers(
      this.state.companyId,
      tickerId,
      this.state.currentDate,
      this.state.selectedTopQuantity.value,
      viewType[this.state.vision.value]
    )
      .then((res) => {
        this.setState({
          currentTopSellers: res,
          isTopSellersLoading: false,
        })
      })
      .catch(() => {
        this.setState({
          currentTopSellers: [],
          isTopSellersLoading: false,
        })
      })
  }

  getSummaryTopHolders = (tickerId) => {
    const viewType = {
      1: 'grouped',
      2: 'albert',
      3: 'simple',
    }

    getSummaryTopHolders(
      this.state.companyId,
      tickerId,
      this.state.currentDate,
      this.state.selectedTopQuantity.value,
      viewType[this.state.vision.value]
    )
      .then((res) => {
        this.setState({
          currentTopHolders: res,
          isTopHoldersLoading: false,
        })
      })
      .catch(() => {
        this.setState({
          currentTopHolders: [],
          isTopHoldersLoading: false,
        })
      })
  }

  getTopBuyersByCountry = (tickerId) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    getTopBuyersByCountry(
      this.state.companyId,
      tickerId,
      this.state.currentDate,
      this.state.selectedTopQuantity.value
    ).then((res) => {
      const seriesName = res.map((item) => item.country)
      const seriesData = res.map((item) => {
        return {
          y: Number(item.percentage),
          total: Number(item.total).toLocaleString(language),
        }
      })

      this.setState({
        seriesNameTopBuyersCountry: seriesName,
        seriesDataTopBuyersCountry: seriesData,
        seriesTopBuyersCountryLoading: false,
      })
    })
  }

  getTopSellersByCountry = (tickerId) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    getTopSellersByCountry(
      this.state.companyId,
      tickerId,
      this.state.currentDate,
      this.state.selectedTopQuantity.value
    ).then((res) => {
      const seriesName = res.map((item) => item.country)
      const seriesData = res.map((item) => {
        return {
          y: Number(item.percentage),
          total: Number(item.total).toLocaleString(language),
        }
      })

      this.setState({
        seriesNameTopSellersCountry: seriesName,
        seriesDataTopSellersCountry: seriesData,
        seriesTopSellersCountryLoading: false,
      })
    })
  }

  getTopShareholderByCountry = (tickerId) => {
    const { companyId } = this.state
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    getTopShareholderByCountry(companyId, tickerId, this.state.currentDate, this.state.selectedTopQuantity.value).then(
      (res) => {
        const seriesName = res.map((item) => item.country)
        const seriesData = res.map((item) => {
          return {
            y: Number(item.percentage),
            total: Number(item.total).toLocaleString(language),
          }
        })

        this.setState({
          seriesNameTopHoldersCountry: seriesName,
          seriesDataTopHoldersCountry: seriesData,
          seriesTopHoldersCountryLoading: false,
        })
      }
    )
  }

  exportCurrentSummary = async () => {
    try {
      const viewType = {
        1: 'grouped',
        2: 'albert',
        3: 'simple',
      }

      const response = await generateSummaryReport({
        companyId: this.state.companyId,
        quantity: this.state.selectedTopQuantity.value,
        referenceDateEnd: formatDateToString(this.state.endDate),
        referenceDateStart: formatDateToString(this.state.startDate),
        tickerId: this.state.currentTicker.value,
        viewType: viewType[this.state.vision.value],
      })

      if (!response.success)
        return this.props.createToast({
          type: 'error',
          title: i18n.t('globals.export.error.title'),
          description: i18n.t('globals.export.error.message'),
        })

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      this.setState({ isLoading: false })
    }
  }

  onOpenAgglutinationModal = (item) => {
    this.setState({ showAggModal: true, currentFund: item })
  }

  closeAggModal = () => {
    this.setState({ showAggModal: false })
  }

  handleGroupedShareholderActionsToast = (shareholderGroupId, shareholderId) => {
    this.props.createToast({
      title: i18n.t('globals.groupedShareholderActionsToastfy.success.title'),
      description: i18n.t('globals.groupedShareholderActionsToastfy.success.message'),
      duration: 15000,
      type: 'success',
      buttons: (
        <>
          <LinkButton link={`${PATH}/ownership/${shareholderGroupId}/grouped/overview`}>
            {i18n.t('groupDetail')}
          </LinkButton>
          <LinkButton link={`${PATH}/ownership/${shareholderId}/simple/overview`}>
            {i18n.t('shareholderDetail')}
          </LinkButton>
        </>
      ),
    })
  }

  handleGroupModalTitleShareholderType = () => {
    const { currentFund } = this.state
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.FUND) return i18n.t('ownership.fund').toLowerCase()
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.INDIVIDUAL)
      return i18n.t('ownership.individual').toLowerCase()
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.UNKNOW) return ''

    return
  }

  handleGetGroups = async () => {
    try {
      const { companyId } = this.state
      const shareholderGroups = await getGroups(companyId)

      const formatShareholderGroups = shareholderGroups.data.data.map((shareholderGroup) => ({
        label: shareholderGroup.name,
        value: shareholderGroup.shareholderGroupId,
      }))

      this.setState({
        groupList: formatShareholderGroups,
      })
    } catch {
      return this.props.createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        type: 'error',
      })
    }
  }

  onCreateGroup = async (groupName) => {
    const { companyId, currentFund } = this.state
    try {
      const res = await postCreateGroupAndGroupShareholder({
        companyId,
        groupName,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      this.setState({ showAggModal: false }, () => {
        this.toggleSummaryLoadingState()
        this.getAllInfos()
        this.handleGetGroups()
      })

      this.handleGroupedShareholderActionsToast(res.data.shareholderGroupId, this.state.currentFund.shareholderId)
    } catch (error) {
      this.props.createToast({
        title: i18n.t('globals.errors.createGroup.title'),
        description: i18n.t('globals.errors.createGroup.message'),
        duration: 9000,
        type: 'error',
      })

      this.setState({ showAggModal: false })
    }
  }

  onVinculateGroup = async (shareholderGroup) => {
    const { companyId, currentFund } = this.state
    const shareholderGroupId = shareholderGroup.value
    try {
      await postGroupShareholder({
        companyId,
        shareholderGroupId,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      this.setState({ showAggModal: false }, () => {
        this.toggleSummaryLoadingState()
        this.getAllInfos()
      })

      this.handleGroupedShareholderActionsToast(shareholderGroupId, this.state.currentFund.shareholderId)
    } catch (error) {
      this.props.createToast({
        title: i18n.t('globals.errors.vinculateGroup.title'),
        description: i18n.t('globals.errors.vinculateGroup.message'),
        duration: 9000,
        type: 'error',
      })

      this.setState({ showAggModal: false })
    }
  }

  seeAllInfos = () => {
    this.setState(
      (prevState) => {
        return { seeAll: !prevState.seeAll }
      },
      () => {
        this.getMoreInfos()
      }
    )
  }

  handleProgressFileUpload = (progressEvent) => {
    const { loaded, total } = progressEvent
    const progress = total > 0 ? Math.round((loaded * 100) / total) : 0

    this.setState({
      uploadProgress: progress,
    })
  }

  handleCloseUploadModal = () => {
    this.setState({ selectedFile: undefined, showMapModal: false })
  }

  handleSelectedFile = (file) => {
    if (file === this.state.selectedFile) return
    this.setState({
      selectedFile: file,
    })
  }

  handleUploadFile = async () => {
    try {
      if (!this.state.selectedFile) return
      await postUploadShareholderBase({
        file: this.state.selectedFile,
        companyId: this.state.companyId,
        tickerId: this.state.currentTicker.value,
        idiom: this.state.idiom,
        onUploadProgress: this.handleProgressFileUpload,
      })
    } catch (err) {
      throw new Error(err)
    } finally {
      this.setState({
        selectedFile: undefined,
        uploadProgress: 0,
        showMapModal: false,
      })
    }
  }

  calcStartDate = (date, datesAvailable) => {
    const previousDateIndex =
      datesAvailable.findIndex((availableDate) => formatDateToString(availableDate) === formatDateToString(date)) - 1
    const hasPreviousDate = previousDateIndex > -1
    const startDate = hasPreviousDate ? datesAvailable[previousDateIndex] : date

    return startDate
  }

  render() {
    const {
      noData,
      tickers,
      vision,
      datesAvailable,
      currentDate,
      groupList,
      endDate,
      isPageLoading,
      showMapModal,
      isNewHoldersLoading,
      currentNewHolders,
      currentZeroedOut,
      isZeroedOutLoading,
      nationalityChartData,
      nationalityChartDataLoading,
      shareholderChartData,
      shareholderChartDataLoading,
      shareholderBase,
      shareholderBaseLoading,
      seriesNameTopHoldersCountry,
      seriesDataTopHoldersCountry,
      seriesTopHoldersCountryLoading,
      seriesNameTopBuyersCountry,
      seriesDataTopBuyersCountry,
      seriesTopBuyersCountryLoading,
      seriesNameTopSellersCountry,
      seriesDataTopSellersCountry,
      seriesTopSellersCountryLoading,
      currentTopBuyers,
      isTopBuyersLoading,
      currentTopSellers,
      isTopSellersLoading,
      currentTopHolders,
      isTopHoldersLoading,
      topQuantityOptions,
      selectedTopQuantity,
      seeAll,
      selectedFile,
      uploadProgress,
    } = this.state

    const acceptedFiles = '.txt, .csv, .xls, .xlsx, .zip, .7z'

    return (
      <div className="shareholders-wrapper">
        <div className={defineTheme('shareholders-content')}>
          {isPageLoading ? (
            <div>
              <div className="lds-dual-ring">
                <div />
              </div>
            </div>
          ) : (
            <div>
              <SummaryHeader
                tickers={tickers}
                datesAvailable={datesAvailable}
                currentDate={endDate}
                onChangeDate={this.onChangeDate}
                onChangeTopQuantity={this.onChangeTopQuantity}
                onChangeStockType={this.onChangeStockType}
                onChangeBaseMode={this.onChangeBaseMode}
                onOpenUploaderModal={this.onOpenUploaderModal}
                exportCurrentSummary={this.exportCurrentSummary}
                selectedTopQuantity={selectedTopQuantity}
                topQuantityOptions={topQuantityOptions}
              />
              {/* { !noData && (
                <>
                  <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                    <ProgressBar
                      progress={Math.floor(this.state.smartGroupingData?.pctGroupedShareholders) || 0}
                      barWidth={50}
                      path={`${PATH}/summary/smartGrouping`}
                      screenWidth={screenWidth}
                    ></ProgressBar>
                  </div>
                  <div style={{ display: 'flex', width: '100%', padding: '20px 0', gap: 20 }}>
                    <Card
                      title={i18n.t(pageSmartGrouping.groupedShareholders)}
                      subtitle={this.state.smartGroupingData?.countGroupedShareholders}
                      disabled
                    />
                    <Card
                      title={i18n.t(pageSmartGrouping.ungroupedShareholders)}
                      subtitle={this.state.smartGroupingData?.countUngroupedShareholders}
                      disabled
                    />
                  </div>
                </>
              )} */}
              <div className="scroll shareholder-summary">
                {noData ? (
                  <div className="no-data">{i18n.t('noResults')}</div>
                ) : (
                  <div className={selectedTopQuantity.value >= 10 ? 'summary-content top-10' : 'summary-content'}>
                    <SummaryShareholdersBase
                      summary={shareholderBase}
                      isLoading={shareholderBaseLoading}
                      referenceDate={currentDate}
                    />

                    <SummaryShareholderByType base={shareholderChartData} isLoading={nationalityChartDataLoading} />

                    <SummaryStocksByNationality base={nationalityChartData} isLoading={shareholderChartDataLoading} />

                    {!seeAll ? (
                      <div className="see-more">
                        <Buttons.Primary onClick={this.seeAllInfos} $margin="0 auto" $height="35px">
                          {i18n.t('overview')}
                        </Buttons.Primary>
                      </div>
                    ) : null}

                    {seeAll ? (
                      <>
                        <SummaryNewHolders
                          isLoading={isNewHoldersLoading}
                          base={currentNewHolders}
                          companyId={this.state.companyId}
                          tickerId={this.state.currentTicker.value}
                          referenceDate={currentDate}
                          onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                          vision={vision}
                        />

                        <SummaryZeroedOut
                          isLoading={isZeroedOutLoading}
                          base={currentZeroedOut}
                          companyId={this.state.companyId}
                          tickerId={this.state.currentTicker.value}
                          referenceDate={currentDate}
                          onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                          vision={vision}
                        />

                        <SummaryTopBuyers
                          isLoading={isTopBuyersLoading}
                          base={currentTopBuyers}
                          companyId={this.state.companyId}
                          tickerId={this.state.currentTicker.value}
                          referenceDate={currentDate}
                          onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                          vision={vision}
                        />

                        <SummaryTopBuyersCountry
                          categories={seriesNameTopBuyersCountry}
                          base={seriesDataTopBuyersCountry}
                          isLoading={seriesTopBuyersCountryLoading}
                          height={selectedTopQuantity.value >= 10 ? 577 : 337}
                        />

                        <SummaryTopSellers
                          isLoading={isTopSellersLoading}
                          base={currentTopSellers}
                          companyId={this.state.companyId}
                          tickerId={this.state.currentTicker.value}
                          referenceDate={currentDate}
                          onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                          vision={vision}
                        />

                        <SummaryTopSellersCountry
                          categories={seriesNameTopSellersCountry}
                          base={seriesDataTopSellersCountry}
                          isLoading={seriesTopSellersCountryLoading}
                          height={selectedTopQuantity.value >= 10 ? 577 : 337}
                        />

                        <SummaryTopHolders
                          isLoading={isTopHoldersLoading}
                          base={currentTopHolders}
                          companyId={this.state.companyId}
                          tickerId={this.state.currentTicker.value}
                          referenceDate={currentDate}
                          onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                          vision={vision}
                        />

                        <SummaryTopHoldersCountry
                          categories={seriesNameTopHoldersCountry}
                          base={seriesDataTopHoldersCountry}
                          isLoading={seriesTopHoldersCountryLoading}
                          height={selectedTopQuantity.value >= 10 ? 577 : 337}
                        />
                      </>
                    ) : null}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <UploadModal
          show={showMapModal}
          onConfirm={this.handleUploadFile}
          onClose={this.handleCloseUploadModal}
          selectedFile={selectedFile}
          onSelectedFile={this.handleSelectedFile}
          title={i18n.t('uploadShareholderBase')}
          progress={uploadProgress}
          acceptedFiles={acceptedFiles}
          confirmButtonLabel={i18n.t('upload')}
        />

        <GroupModal
          title={`${i18n.t('ownership.agroup')} ${this.handleGroupModalTitleShareholderType()}`}
          visibility={this.state.showAggModal}
          options={groupList}
          onClose={this.closeAggModal}
          onCreateGroup={this.onCreateGroup}
          onConfirm={this.onVinculateGroup}
        />
      </div>
    )
  }
}

BaseSummary.contextType = StoreContext

const Summary = hocWrapper(BaseSummary)

export default Summary
