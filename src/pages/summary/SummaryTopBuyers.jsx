import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import ReactTooltip from 'react-tooltip'
import { Collapse } from 'react-collapse'

import { i18n } from 'translate'
import { getSummaryPosition } from 'client'
import { PATH } from 'consts'
import { localInformation, generateUniqueId } from 'utils'

class SummaryTopBuyers extends Component {
  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      companyId: localInformation.getCompanyId(),
      childItems: [],
      expandedItems: [],
      pointerEvents: false,
      idList: '',
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.vision !== this.props.vision) {
      this.setState({
        childItems: [],
        expandedItems: [],
        pointerEvents: false,
        idList: '',
      })
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  onShowDetail = (id) => {
    const { childItems, expandedItems } = this.state
    const { vision } = this.props
    const index = expandedItems.indexOf(id)

    if (index === -1) {
      expandedItems.push(id)
    } else {
      expandedItems.splice(index, 1)
    }

    if (childItems[id]) {
      this.setState({ expandedItems, pointerEvents: false })
    } else {
      const albert = vision.value === 2
      getSummaryPosition(
        this.state.companyId,
        this.props.tickerId,
        id,
        this.props.referenceDate,
        albert,
        'topBuyers'
      ).then((res) => {
        childItems[id] = res
        this.setState({ expandedItems, childItems, pointerEvents: false })
      })
    }
  }

  onClickPointerEvents = (item, index) => {
    this.setState(
      {
        idList: index,
      },
      () => {
        if (index === this.state.idList) {
          const { pointerEvents } = this.state
          this.setState({ pointerEvents: !pointerEvents })
          setTimeout(() => {
            this.onShowDetail(item)
          }, 1500)
        }
      }
    )
  }

  render() {
    const { idiom } = this.state
    const { isLoading, base, vision } = this.props

    const language = idiom === 1 ? 'pt-BR' : 'en-US'

    return (
      <div className="top-buyers">
        <div className="title">
          <h6>{i18n.t('topBuyers')}</h6>
        </div>
        {isLoading ? (
          <div className="lds-dual-ring">
            <div />
          </div>
        ) : (
          <div className="scroll">
            <ul className="base-list summary-list top-buyer dark">
              <li className="header-title">
                <span className="num" />
                <span className="name">{i18n.t('name')}</span>
                <span className="country">{i18n.t('country')}</span>
                <span className="quantity">&Delta; {i18n.t('summaryDay')}</span>
                <span className="quantity">&Delta; {i18n.t('summaryWeek')}</span>
                <span className="quantity">&Delta; {i18n.t('summaryMonth')}</span>
                <span className="quantity">&Delta; {i18n.t('summaryYear')}</span>
                <span className="quantity">{i18n.t('summaryShares')}</span>
                <span className="total">% Total</span>
              </li>
              {base.length > 0 ? (
                <>
                  {base.map((item, i) => {
                    const key = generateUniqueId()
                    return (
                      <li key={key}>
                        {item.isExpandable ? (
                          <span
                            onClick={() => this.onClickPointerEvents(item.shareholderGroupId, i)}
                            className={`num ${this.state.pointerEvents ? 'pointerEvents' : ''}`}
                            onKeyDown={() => this.onClickPointerEvents(item.shareholderGroupId, i)}
                            role="button"
                            tabIndex={0}
                          >
                            {this.state.pointerEvents ? (
                              this.state.idList === i ? (
                                <div className="lds-dual-ring">
                                  <div />
                                </div>
                              ) : (
                                <span
                                  className={
                                    this.state.expandedItems.includes(item.shareholderGroupId) ? 'close' : 'open'
                                  }
                                />
                              )
                            ) : (
                              <span
                                className={
                                  this.state.expandedItems.includes(item.shareholderGroupId) ? 'close' : 'open'
                                }
                              />
                            )}
                          </span>
                        ) : (
                          <span className="num">
                            {!item.shareholderGroupId && vision.value !== 2 ? (
                              <i
                                title={i18n.t('groupLabel')}
                                onClick={() => this.props.onOpenAgglutinationModal(item)}
                                aria-hidden
                              />
                            ) : null}
                          </span>
                        )}
                        <span className="name" data-tip={item.name} data-for="top-buyers">
                          {item.shareholderType === 88 ? (
                            <Link to={`${PATH}/ownership/${item.shareholderGroupId}/albert/overview`}>{item.name}</Link>
                          ) : item.shareholderType === 99 ? (
                            <Link to={`${PATH}/ownership/${item.shareholderGroupId}/grouped/overview`}>
                              {item.name}
                            </Link>
                          ) : (
                            <Link to={`${PATH}/ownership/${item.shareholderId}/simple/overview`}>{item.name}</Link>
                          )}
                        </span>
                        <span
                          className="country"
                          data-tip={item.countryCode && item.countryCode.replace(/,/g, ', ')}
                          data-for="top-buyers"
                        >
                          {item.countryCode ? item.countryCode.replace(/,/g, ', ') : 'N/A'}
                        </span>
                        <span
                          className={
                            item.stockDeltaDay > 0
                              ? 'quantity up'
                              : item.stockDeltaDay < 0
                                ? 'quantity down'
                                : 'quantity'
                          }
                        >
                          {item.stockDeltaDay ? this.formatted(item.stockDeltaDay) : '--'}
                        </span>
                        <span
                          className={
                            item.stockDeltaWeek > 0
                              ? 'quantity up'
                              : item.stockDeltaWeek < 0
                                ? 'quantity down'
                                : 'quantity'
                          }
                        >
                          {item.stockDeltaWeek ? this.formatted(item.stockDeltaWeek) : '--'}
                        </span>
                        <span
                          className={
                            item.stockDeltaMonth > 0
                              ? 'quantity up'
                              : item.stockDeltaMonth < 0
                                ? 'quantity down'
                                : 'quantity'
                          }
                        >
                          {item.stockDeltaMonth ? this.formatted(item.stockDeltaMonth) : '--'}
                        </span>
                        <span
                          className={
                            item.stockDeltaYear > 0
                              ? 'quantity up'
                              : item.stockDeltaYear < 0
                                ? 'quantity down'
                                : 'quantity'
                          }
                        >
                          {item.stockDeltaYear ? this.formatted(item.stockDeltaYear) : '--'}
                        </span>
                        <span className="quantity">{this.formatted(item.stockAmountEdited)}</span>
                        <span className="total">
                          {`${Number(item.representatively).toLocaleString(language, {
                            maximumFractionDigits: 2,
                            minimumFractionDigits: 2,
                          })}%`}
                        </span>

                        <Collapse
                          isOpened={this.state.expandedItems.includes(item.shareholderGroupId)}
                          className={`summary-detail ${
                            this.state.expandedItems.includes(item.shareholderGroupId) ? 'open' : 'close'
                          }`}
                        >
                          <ul>
                            {this.state.childItems[item.shareholderGroupId] ? (
                              <>
                                {this.state.childItems[item.shareholderGroupId].map((subItem) => {
                                  const subKey = generateUniqueId()
                                  return (
                                    <li className="sub-item" key={subKey}>
                                      <span className="name" data-tip={subItem.name} data-for="top-buyers-child">
                                        <Link to={`${PATH}/ownership/${subItem.shareholderId}/simple/overview`}>
                                          {subItem.name}
                                        </Link>
                                      </span>
                                      <span className="country">
                                        {subItem.countryCode ? subItem.countryCode : 'N/A'}
                                      </span>
                                      <span
                                        className={
                                          subItem.deltaDay > 0
                                            ? 'quantity up'
                                            : subItem.deltaDay < 0
                                              ? 'quantity down'
                                              : 'quantity'
                                        }
                                      >
                                        {this.formatted(subItem.deltaDay)}
                                      </span>
                                      <span
                                        className={
                                          subItem.deltaWeek > 0
                                            ? 'quantity up'
                                            : subItem.deltaWeek < 0
                                              ? 'quantity down'
                                              : 'quantity'
                                        }
                                      >
                                        {this.formatted(subItem.deltaWeek)}
                                      </span>
                                      <span
                                        className={
                                          subItem.deltaMonth > 0
                                            ? 'quantity up'
                                            : subItem.deltaMonth < 0
                                              ? 'quantity down'
                                              : 'quantity'
                                        }
                                      >
                                        {this.formatted(subItem.deltaMonth)}
                                      </span>
                                      <span
                                        className={
                                          subItem.deltaYear > 0
                                            ? 'quantity up'
                                            : subItem.deltaYear < 0
                                              ? 'quantity down'
                                              : 'quantity'
                                        }
                                      >
                                        {this.formatted(subItem.deltaYear)}
                                      </span>
                                      <span
                                        className={
                                          subItem.total > 0
                                            ? 'quantity up'
                                            : subItem.total < 0
                                              ? 'quantity down'
                                              : 'quantity'
                                        }
                                      >
                                        {this.formatted(subItem.total)}
                                      </span>
                                      <span className="total">
                                        {`${Number(subItem.percentage).toLocaleString(language, {
                                          maximumFractionDigits: 2,
                                          minimumFractionDigits: 2,
                                        })}%`}
                                      </span>
                                    </li>
                                  )
                                })}
                                <ReactTooltip place="top" delayShow={500} id="top-buyers-child" />
                              </>
                            ) : null}
                          </ul>
                        </Collapse>
                      </li>
                    )
                  })}
                  <ReactTooltip place="top" delayShow={500} id="top-buyers" />
                </>
              ) : (
                <li className="no-results">{i18n.t('noResults')}</li>
              )}
            </ul>
          </div>
        )}
      </div>
    )
  }
}

export default SummaryTopBuyers
