import React, { Component } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@mz-codes/design-system'
import { i18n } from 'translate'

import { hasScopePermission } from 'utils'
import { ExternalLink } from 'components'
import { IMAGES } from 'assets'
import { EXTERNAL_PRODUCT } from 'consts/external-products'

class SummaryHeader extends Component {
  constructor(props) {
    super(props)
    this.state = {
      stockType: null,
      baseMode: null,
      topQuantityOptions: this.props.topQuantityOptions,
      selectedTopQuantity: this.props.selectedTopQuantity,
      viewType: [
        {
          value: 1,
          label: i18n.t('grouped'),
        },
        {
          value: 3,
          label: i18n.t('simple'),
        },
        {
          value: 2,
          label: 'Beta',
        },
      ],
      hasNotification: false,
    }
  }

  componentDidMount() {
    const hasNotification = hasScopePermission([
      'mzcorelegacy:responsible:create',
      'mzcorelegacy:responsible:delete',
      'mzcorelegacy:responsible:get',
      'mzcorelegacy:responsible:list',
      'mzcorelegacy:responsible:update',
      'mzcorelegacy:notification:get',
      'mzcorelegacy:notification:update',
    ])

    this.setState({ hasNotification })
  }

  onChangeBaseMode = (value) => {
    const { baseMode } = this.state
    if (baseMode === value) return
    this.setState({ baseMode: value })
    this.props.onChangeBaseMode(value)
  }

  onChangeStockType = (value) => {
    const { tickers } = this.props

    const newTicker = tickers.filter((ticker) => {
      if (ticker.label === value.label) {
        return ticker
      }
      return null
    })
    this.setState({ stockType: value })

    this.props.onChangeStockType(newTicker[0])
  }

  onChangeTopQuantity = (selectedTopQuantity) => {
    this.setState({ selectedTopQuantity })
    this.props.onChangeTopQuantity(selectedTopQuantity)
  }

  render() {
    const today = new Date()
    const { tickers, currentDate = today, datesAvailable, onChangeDate } = this.props
    const { stockType, viewType, baseMode, topQuantityOptions, selectedTopQuantity, hasNotification } = this.state
    const disableHeaderContents = !tickers || !datesAvailable.length

    return (
      <Header>
        <Header.Content style={{ alignItems: 'flex-end' }}>
          <Header.Item>
            <Header.Label>{i18n.t('stockType')}</Header.Label>
            <Dropdown
              options={tickers}
              minWidth={150}
              onChange={this.onChangeStockType}
              selected={stockType || tickers[0]}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{i18n.t('viewType')}</Header.Label>
            <Dropdown
              options={viewType}
              minWidth={150}
              onChange={this.onChangeBaseMode}
              selected={baseMode || viewType[0]}
              disabled={disableHeaderContents}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{i18n.t('quantity')}</Header.Label>
            <Dropdown
              options={topQuantityOptions}
              minWidth={150}
              onChange={this.onChangeTopQuantity}
              selected={selectedTopQuantity}
              disabled={disableHeaderContents}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{i18n.t('date')}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={currentDate}
              onChange={onChangeDate}
              blocked={disableHeaderContents}
              availableDates={datesAvailable}
              hint={i18n.t('globals.referenceDate')}
            />
          </Header.Item>
          {hasNotification ? (
            <Tooltip
              $wrapperStyle={{ margin: 'auto 16px auto auto' }}
              $tooltipStyle={{ width: '300px', textOverflow: 'initial', whiteSpace: 'normal', textAlign: 'center' }}
              text={i18n.t('emailAlert')}
            >
              <ExternalLink product={EXTERNAL_PRODUCT.SETTINGS} pagePath="/responsible">
                <img src={IMAGES.EMAIL_ALERT} alt="" />
              </ExternalLink>
            </Tooltip>
          ) : null}
        </Header.Content>
        <Header.ButtonGroup style={{ margin: hasNotification ? 0 : '0 0 0 auto' }}>
          <Buttons.Primary onClick={() => this.props.onOpenUploaderModal()}>{i18n.t('btnAddBase')}</Buttons.Primary>
          <Buttons.Export onClick={() => this.props.exportCurrentSummary()} disabled={disableHeaderContents}>
            {i18n.t('btnExport')}
          </Buttons.Export>
        </Header.ButtonGroup>
      </Header>
    )
  }
}

export default SummaryHeader
