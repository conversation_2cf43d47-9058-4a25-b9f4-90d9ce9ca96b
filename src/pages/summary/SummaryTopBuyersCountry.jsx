import React, { Component } from 'react'

import { i18n } from 'translate'

import SummaryBar<PERSON>hart from './SummaryBarChart'

class SummaryTopBuyersCountry extends Component {
  renderChart = () => {
    const { idiom, base, categories } = this.props
    return base ? (
      <SummaryBarChart
        categories={categories}
        data={base}
        seriesName={i18n.t('topBuyersPerCountrySeriesName')}
        title={i18n.t('topBuyersPerCountry')}
        yAxisText={i18n.t('topBuyersPerCountryYAxis')}
        idiom={idiom}
        height={this.props.height}
      />
    ) : null
  }

  render() {
    const { isLoading } = this.props

    if (isLoading) {
      return (
        <div className="top-buyers-chart">
          <div className="lds-dual-ring">
            <div />
          </div>
        </div>
      )
    }

    return <div className="top-buyers-chart">{this.renderChart()}</div>
  }
}

export default SummaryTopBuyersCountry
