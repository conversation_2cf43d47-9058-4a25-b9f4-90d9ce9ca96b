import styled from 'styled-components'
import { Table } from '@mz-codes/design-system'

export const TableTBody = styled(Table.TBody)`
  border-radius: ${({ theme }) => theme.units.borderRadius.lg};

  > tr:not(:last-of-type) {
    border-bottom: 1px solid ${({ theme }) => theme.colors.neutral.grey[400]};
  }

  > tr:first-of-type :hover {
    border-radius: ${({ theme }) => theme.units.borderRadius.lg} ${({ theme }) => theme.units.borderRadius.lg} 0 0;
  }

  &:last-child :hover,
  &:last-child td {
    border-radius: 0 0 ${({ theme }) => theme.units.borderRadius.lg} ${({ theme }) => theme.units.borderRadius.lg};
  }
`
