import React from 'react'

import { Loading, Table } from '@mz-codes/design-system'

import { translations } from './shareholder-base.translations'
import { TSummaryShareholdersBaseTemplateProps } from './shareholder-base.types'
import { NoData } from './no-data'
import { LoadingWrapper } from './loading-wrapper'
import { TableTD } from './table-td'
import { TableTBody } from './table-tbody'
import { VariationItem } from './variation-item'
import { Container } from './container'
import { TableItem } from './table-item'

export function SummaryShareholderBaseTemplate(props: TSummaryShareholdersBaseTemplateProps) {
  const { summary, isLoading } = props

  const hasSummary = !isLoading && summary
  const hasNoSummary = !isLoading && !summary

  return (
    <Container>
      {isLoading && (
        <LoadingWrapper>
          <Loading />
        </LoadingWrapper>
      )}

      {hasSummary && (
        <Table>
          <TableTBody>
            <Table.TR>
              <TableTD colSpan={2} $bold>
                {translations.shareholderBaseIn} {summary.referenceDate}
              </TableTD>
            </Table.TR>
            <Table.TR>
              <TableTD colSpan={2}>
                {translations.variationsIn} {summary.prevDay}
              </TableTD>
            </Table.TR>
            <Table.TR>
              <TableTD>{translations.stocks}</TableTD>
              <TableTD $textAlign="right">{summary.stockAmount}</TableTD>
            </Table.TR>
            <Table.TR>
              <TableTD>{translations.shareholders}</TableTD>
              <TableTD $textAlign="right">
                <TableItem>{summary.shareholdersTotal} </TableItem>
                <TableItem>
                  (<VariationItem $type="entry">{summary.shareholdersEntry}</VariationItem>
                  <TableItem>; </TableItem>
                  <VariationItem $type="exit">{summary.shareholdersExit}</VariationItem>)
                </TableItem>
              </TableTD>
            </Table.TR>
            <Table.TR>
              <TableTD $nest>{translations.funds}</TableTD>
              <TableTD $textAlign="right">
                <TableItem>{summary.fundsTotal} </TableItem>
                <TableItem>
                  (<VariationItem $type="entry">{summary.fundsEntry}</VariationItem>
                  <TableItem>; </TableItem>
                  <VariationItem $type="exit">{summary.fundsExit}</VariationItem>)
                </TableItem>
              </TableTD>
            </Table.TR>
            <Table.TR>
              <TableTD $nest>{translations.individuals}</TableTD>
              <TableTD $textAlign="right">
                <TableItem>{summary.individualsTotal} </TableItem>
                <TableItem>
                  (<VariationItem $type="entry">{summary.individualsEntry}</VariationItem>
                  <TableItem>; </TableItem>
                  <VariationItem $type="exit">{summary.individualsExit}</VariationItem>)
                </TableItem>
              </TableTD>
            </Table.TR>
            <Table.TR>
              <TableTD>{translations.volume}</TableTD>
              <TableTD $textAlign="right">{summary.volume}</TableTD>
            </Table.TR>
          </TableTBody>
        </Table>
      )}

      {hasNoSummary && <NoData>{translations.noResults}</NoData>}
    </Container>
  )
}
