import React from 'react'

import { i18n } from 'translate'

import { formatDate } from 'utils'
import { TSummaryShareholdersBaseProps, TSummaryTemplate } from './shareholder-base.types'
import { SummaryShareholderBaseTemplate } from './shareholder-base.template'

export function SummaryShareholdersBase(props: TSummaryShareholdersBaseProps) {
  const { isLoading, summary, referenceDate } = props

  const formatNumberToString = (value: number, prefix: string = '', suffix: string = '') => {
    return `${prefix}${value.toLocaleString(i18n.language)}${suffix}`
  }

  const formatVolume = (volume: number) => {
    const [volMagnitude, divisionValue] = volume < 1000000 ? ['K', 1000] : ['M', 1000000]
    const divisionResult = volume / divisionValue
    const resultFormatted = Math.round((divisionResult + Number.EPSILON) * 10) / 10
    return `${formatNumberToString(resultFormatted)}${volMagnitude}`
  }

  let summaryParsed: TSummaryTemplate | null = null
  if (summary) {
    const { prevDay, volume, ...rest } = summary

    const entries = Object.entries(rest).map(([key, value]) => [key, formatNumberToString(value)])
    const summaryFormatted: TSummaryTemplate = Object.fromEntries(entries)

    summaryParsed = {
      ...summaryFormatted,
      shareholdersEntry: `+${summaryFormatted.shareholdersEntry}`,
      individualsEntry: `+${summaryFormatted.individualsEntry}`,
      fundsEntry: `+${summaryFormatted.fundsEntry}`,
      prevDay: formatDate(new Date(`${prevDay}T00:00:00.000`)),
      referenceDate: formatDate(new Date(`${referenceDate}T00:00:00.000`)),
      volume: formatVolume(volume),
    }
  }

  return <SummaryShareholderBaseTemplate isLoading={isLoading} summary={summaryParsed} />
}
