import { ConvertProps } from 'types/convert-props'

type Summary = {
  prevDay: string
  stockAmount: number
  volume: number
  shareholdersTotal: number
  shareholdersEntry: number
  shareholdersExit: number
  fundsTotal: number
  fundsEntry: number
  fundsExit: number
  individualsTotal: number
  individualsEntry: number
  individualsExit: number
}

export type TSummaryShareholdersBaseProps = {
  isLoading: boolean
  summary: Summary | null
  referenceDate: string
}

export type TSummaryTemplate = ConvertProps<Summary, number, string> & {
  referenceDate: string
}

export type TSummaryShareholdersBaseTemplateProps = {
  summary: TSummaryTemplate | null
  isLoading: boolean
}
