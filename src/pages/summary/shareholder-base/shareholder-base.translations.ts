import { i18n } from 'translate'

export const translations = {
  shareholderBaseIn: i18n.t('shareholderBaseIn'),
  variationsIn: i18n.t('variationsPositions'),
  dateFormat: i18n.t('dateFormat'),
  stocks: i18n.t('stockQuantity'),
  shareholders: i18n.t('shareholdersQuantity'),
  funds: i18n.t('fund'),
  individuals: i18n.t('private'),
  volume: i18n.t('calculatedVolume'),
  noResults: i18n.t('noResults'),
}
