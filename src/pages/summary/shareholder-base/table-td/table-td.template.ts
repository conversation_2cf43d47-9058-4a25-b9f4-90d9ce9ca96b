import styled, { css } from 'styled-components'
import { Table } from '@mz-codes/design-system'
import { TTableTD } from './table-td.types'

const tableTDNestCss = css`
  padding-left: ${({ theme }) => theme.units.spacing.xxl};
`

const tableTDBold = css`
  font-weight: 700;
`

export const TableTD = styled(Table.TD)<TTableTD>`
  padding-top: ${({ theme }) => theme.units.spacing.sm};
  padding-bottom: ${({ theme }) => theme.units.spacing.sm};

  ${({ $nest }) => !!$nest && tableTDNestCss}
  ${({ $bold }) => !!$bold && tableTDBold}
`
