import React, { Component } from 'react'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { i18n } from 'translate'
import { theme } from '@mz-codes/design-system'
import { utils } from 'utils'

class ColumnChart extends Component {
  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
    }
  }

  render() {
    if (this.state.idiom === 0) {
      Highcharts.setOptions({
        lang: {
          decimalPoint: '.',
          thousandsSep: ',',
        },
      })
    } else {
      Highcharts.setOptions({
        lang: {
          decimalPoint: ',',
          thousandsSep: '.',
        },
      })
    }

    const optionsGraph = {
      chart: {
        type: 'bar',
        backgroundColor: theme.legacy.colors.neutral.contentBackground,
        borderRadius: utils.convertPxToNumber(theme.legacy.units.md),
        height: this.props.height ? this.props.height : 337,
      },
      title: {
        align: 'center',
        text: this.props.title,
        style: {
          color: '#ffffff',
          fontSize: '14px',
          fontWeight: 'bold',
        },
        y: 18,
      },
      xAxis: {
        categories: this.props.categories,
        crosshair: true,
        max: this.props.data.length > 10 ? 9 : null,
        labels: {
          style: {
            color: 'white',
          },
        },
      },
      yAxis: {
        min: 0,
        max: 100,
        title: {
          text: this.props.yAxisText,
          style: {
            color: '#717379',
          },
          y: 8,
        },
        labels: {
          format: '{value}%',
          style: {
            color: 'white',
          },
        },
      },
      legend: {
        enabled: false,
        itemStyle: {
          color: '#ffffff',
          fontWeight: 'bold',
        },
      },
      credits: {
        enabled: false,
      },
      tooltip: {
        headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
        pointFormat:
          '<tr><td style="padding:0 3px 0 0">{series.name} </td><td> {point.total} </td><td style="padding:0 0 0 3px;text-align:right"><b>{point.y:,.2f}%</b></td></tr>',
        footerFormat: '</table>',
        shared: true,
        useHTML: true,
      },
      plotOptions: {
        column: {
          pointPadding: 0.2,
          borderWidth: 0,
        },
      },
      series: [
        {
          name: this.props.seriesName,
          data: this.props.data,
        },
      ],
    }

    return <HighchartsReact highcharts={Highcharts} options={optionsGraph} />
  }
}

export default ColumnChart
