import React, { Component } from 'react'
import { Link } from 'react-router-dom'
import ReactTooltip from 'react-tooltip'
import { Collapse } from 'react-collapse'

import { i18n } from 'translate'
import { getCurrentShareholderAlbertChildPosition, getCurrentShareholderChildPosition } from 'client'
import { PATH } from 'consts'
import { generateUniqueId } from 'utils'

class SummaryNewHolders extends Component {
  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      childItems: [],
      expandedItems: [],
      pointerEvents: false,
      idList: '',
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.vision !== this.props.vision) {
      this.setState({
        childItems: [],
        expandedItems: [],
        pointerEvents: false,
        idList: '',
      })
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  onShowDetail = (id) => {
    const { childItems, expandedItems } = this.state
    const { vision } = this.props
    const index = expandedItems.indexOf(id)

    if (index === -1) {
      expandedItems.push(id)
    } else {
      expandedItems.splice(index, 1)
    }

    if (childItems[id]) {
      this.setState({ expandedItems, pointerEvents: false })
    } else if (vision.value === 2) {
      getCurrentShareholderAlbertChildPosition(
        this.props.companyId,
        this.props.tickerId,
        this.props.referenceDate,
        id
      ).then((res) => {
        childItems[id] = res.data.positions
        this.setState({ expandedItems, childItems, pointerEvents: false })
      })
    } else {
      getCurrentShareholderChildPosition(this.props.companyId, this.props.tickerId, this.props.referenceDate, id).then(
        (res) => {
          childItems[id] = res.data.positions
          this.setState({ expandedItems, childItems, pointerEvents: false })
        }
      )
    }
  }

  onClickPointerEvents = (item, index) => {
    this.setState(
      {
        idList: index,
      },
      () => {
        if (index === this.state.idList) {
          const { pointerEvents } = this.state
          this.setState({ pointerEvents: !pointerEvents })
          setTimeout(() => {
            this.onShowDetail(item)
          }, 1500)
        }
      }
    )
  }

  render() {
    const { isLoading, base, vision } = this.props

    return (
      <div className="new-holders">
        <div className="title">
          <h6>{i18n.t('newHolders')}</h6>
        </div>
        {isLoading ? (
          <div className="lds-dual-ring">
            <div />
          </div>
        ) : (
          <div className="scroll">
            <ul className="base-list summary-list new-holder dark">
              <li className="header-title">
                <span className="num" />
                <span className="name">{i18n.t('name')}</span>
                <span className="country">{i18n.t('country')}</span>
                <span className="quantity">{i18n.t('quantityPurchased')}</span>
              </li>
              {base.length > 0 ? (
                <>
                  {base.map((item, i) => (
                    <li key={item.entityId}>
                      {item.isExpandable ? (
                        <span
                          onClick={() => this.onClickPointerEvents(item.shareholderGroupId, i)}
                          onKeyDown={() => this.onClickPointerEvents(item.shareholderGroupId, i)}
                          role="button"
                          tabIndex={0}
                          className={`num ${this.state.pointerEvents ? 'pointerEvents' : ''}`}
                        >
                          {this.state.pointerEvents ? (
                            this.state.idList === i ? (
                              <div className="lds-dual-ring">
                                <div />
                              </div>
                            ) : (
                              <span
                                className={
                                  this.state.expandedItems.includes(item.shareholderGroupId) ? 'close' : 'open'
                                }
                              />
                            )
                          ) : (
                            <span
                              className={this.state.expandedItems.includes(item.shareholderGroupId) ? 'close' : 'open'}
                            />
                          )}
                        </span>
                      ) : (
                        <span className="num">
                          {!item.shareholderGroupId && vision.value !== 2 ? (
                            <i
                              title={i18n.t('groupLabel')}
                              onClick={() => this.props.onOpenAgglutinationModal(item)}
                              onKeyDown={() => this.props.onOpenAgglutinationModal(item)}
                              role="button"
                              aria-hidden
                            />
                          ) : null}
                        </span>
                      )}
                      <span className="name" data-tip={item.name} data-for="new-holders">
                        {item.shareholderType === 88 ? (
                          <Link
                            to={`${PATH}/ownership/${item.shareholderGroupId ?? item.shareholderId}/albert/overview`}
                          >
                            {item.name}
                          </Link>
                        ) : item.shareholderType === 99 ? (
                          <Link
                            to={`${PATH}/ownership/${item.shareholderGroupId ?? item.shareholderId}/grouped/overview`}
                          >
                            {item.name}
                          </Link>
                        ) : (
                          <Link
                            to={`${PATH}/ownership/${item.shareholderGroupId ?? item.shareholderId}/simple/overview`}
                          >
                            {item.name}
                          </Link>
                        )}
                      </span>
                      <span className="country" data-tip={item.countryCode} data-for="new-holders">
                        {item.countryCode ? item.countryCode : 'N/A'}
                      </span>
                      <span
                        className={
                          item.difference > 0 ? 'quantity up' : item.difference < 0 ? 'quantity down' : 'quantity'
                        }
                      >
                        {this.formatted(item.difference)}
                      </span>

                      <Collapse
                        isOpened={this.state.expandedItems.includes(item.shareholderGroupId)}
                        className={`summary-detail ${
                          this.state.expandedItems.includes(item.shareholderGroupId) ? 'open' : 'close'
                        }`}
                      >
                        <ul>
                          {this.state.childItems[item.shareholderGroupId] ? (
                            <>
                              {this.state.childItems[item.shareholderGroupId].map((subItem) => {
                                const key = generateUniqueId()
                                return (
                                  <li className="sub-item" key={key}>
                                    <span className="name" data-tip={subItem.displayName} data-for="new-holders-child">
                                      <Link to={`${PATH}/ownership/${subItem.shareholderId}/simple/overview`}>
                                        {subItem.displayName}
                                      </Link>
                                    </span>
                                    <span className="country">{subItem.countryCode ? subItem.countryCode : 'N/A'}</span>
                                    <span
                                      className={
                                        subItem.stockAmountEdited > 0
                                          ? 'quantity up'
                                          : subItem.stockAmountEdited < 0
                                            ? 'quantity down'
                                            : 'quantity'
                                      }
                                    >
                                      {this.formatted(subItem.stockAmountEdited)}
                                    </span>
                                  </li>
                                )
                              })}
                              <ReactTooltip place="top" delayShow={500} id="new-holders-child" />
                            </>
                          ) : null}
                        </ul>
                      </Collapse>
                    </li>
                  ))}
                  <ReactTooltip place="top" delayShow={500} id="new-holders" />
                </>
              ) : (
                <li className="no-results">{i18n.t('noResults')}</li>
              )}
            </ul>
          </div>
        )}
      </div>
    )
  }
}

export default SummaryNewHolders
