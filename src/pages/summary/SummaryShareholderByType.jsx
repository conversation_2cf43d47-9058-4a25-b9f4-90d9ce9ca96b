import React, { Component } from 'react'

import { i18n } from 'translate'
import Summary<PERSON><PERSON><PERSON>hart from './SummaryPieChart'

class SummaryShareholderByType extends Component {
  render() {
    const { isLoading, idiom, base } = this.props
    if (isLoading) {
      return (
        <div className="shareholder-type-chart">
          <div className="lds-dual-ring">
            <div />
          </div>
        </div>
      )
    }

    return (
      <div className="shareholder-type-chart">
        {base && (
          <SummaryPieChart
            data={base}
            name={i18n.t('numberShareholders')}
            title={i18n.t('shareholderByType')}
            idiom={idiom}
          />
        )}
      </div>
    )
  }
}

export default SummaryShareholderByType
