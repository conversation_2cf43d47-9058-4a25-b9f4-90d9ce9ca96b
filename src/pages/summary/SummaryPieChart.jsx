import React, { Component } from 'react'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { i18n } from 'translate'
import { theme } from '@mz-codes/design-system'
import { utils } from 'utils'

class SummaryPieChart extends Component {
  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
    }
  }

  getBorderRadius() {
    const borderRadiusValue = utils.convertPxToNumber(theme.legacy.units.md)
    if (isNaN(borderRadiusValue)) {
      console.error(
        'Erro: Border radius retornou NaN. Verifique o valor de theme.legacy.units.md:',
        theme.legacy.units.md
      )
      return 0
    }
    return borderRadiusValue
  }

  render() {
    if (this.state.idiom === 0) {
      Highcharts.setOptions({
        lang: {
          decimalPoint: '.',
          thousandsSep: ',',
        },
      })
    } else {
      Highcharts.setOptions({
        lang: {
          decimalPoint: ',',
          thousandsSep: '.',
        },
      })
    }

    const borderRadius = this.getBorderRadius()

    const optionsGraph = {
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        type: 'pie',
        backgroundColor: theme.legacy.colors.neutral.contentBackground || '#ffffff',
        borderRadius,
        height: 337,
      },
      title: {
        text: this.props.title,
        style: {
          color: '#ffffff',
          fontWeight: 'bold',
          fontSize: '14px',
        },
      },
      tooltip: {
        pointFormat: '{series.name}: {point.y} <b>{point.percentage:.1f}%</b>',
      },
      plotOptions: {
        pie: {
          allowPointSelect: false,
          cursor: 'pointer',
          dataLabels: {
            enabled: false,
            format: '{point.percentage:.1f}%',
            style: {
              color: '#ffffff',
              fontWeight: 'bold',
              fontSize: '10px',
            },
          },
          showInLegend: true,
        },
      },
      legend: {
        itemStyle: {
          color: '#ffffff',
          fontWeight: 'bold',
        },
        labelFormat: '{name}: {percentage:.1f}%',
      },
      credits: {
        enabled: false,
      },
      series: [
        {
          name: this.props.name,
          colorByPoint: true,
          data: this.props.data,
        },
      ],
    }

    return <HighchartsReact highcharts={Highcharts} options={optionsGraph} />
  }
}

export default SummaryPieChart
