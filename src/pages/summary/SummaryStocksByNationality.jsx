import React, { Component } from 'react'

import { i18n } from 'translate'

import Summary<PERSON><PERSON><PERSON>hart from './SummaryPieChart'

class SummaryStocksByNationality extends Component {
  renderChart = () => {
    const { idiom, base } = this.props
    return base ? (
      <SummaryPieChart data={base} name={i18n.t('numberShares')} title={i18n.t('stocksByNationality')} idiom={idiom} />
    ) : null
  }

  render() {
    const { isLoading } = this.props

    if (isLoading) {
      return (
        <div className="stock-chart">
          <div className="lds-dual-ring">
            <div />
          </div>
        </div>
      )
    }

    return <div className="stock-chart">{this.renderChart()}</div>
  }
}

export default SummaryStocksByNationality
