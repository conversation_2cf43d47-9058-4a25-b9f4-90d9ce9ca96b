import React, { Component } from 'react'
import { Buttons, Dropdown, New<PERSON><PERSON><PERSON><PERSON>, Head<PERSON> } from '@mz-codes/design-system'

import { i18n } from 'translate'

export default class ChartHeader extends Component {
  onProcess = (e) => {
    if (e) {
      e.preventDefault()
    }
    this.props.onProcess()
  }

  onProcessExport = () => {
    return this.props.onProcessExport()
  }

  render() {
    const { currentTicker, tickers, currentDate, datesAvailable, onChangeTicker, onChangeDate } = this.props
    const disableHeaderOptions = !tickers || !datesAvailable?.length || !currentDate

    return (
      <Header>
        <Header.Content>
          <Header.Item>
            <Header.Label>{i18n.t('stockType')}</Header.Label>
            <Dropdown
              disabled={disableHeaderOptions}
              options={tickers}
              minWidth={150}
              onChange={onChangeTicker}
              selected={currentTicker}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{i18n.t('ownership.date')}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              blocked={disableHeaderOptions}
              selected={currentDate}
              onChange={onChangeDate}
              minDate={datesAvailable?.[0]}
              maxDate={datesAvailable?.[datesAvailable.length - 1]}
              hint={i18n.t('globals.referenceDate')}
            />
          </Header.Item>
        </Header.Content>
        <Header.ButtonGroup>
          <Buttons.Primary disabled={disableHeaderOptions} onClick={this.onProcess}>
            {i18n.t('btnLoad')}
          </Buttons.Primary>
          <Buttons.Export disabled={disableHeaderOptions} onClick={this.onProcessExport}>
            {i18n.t('ownership.export')}
          </Buttons.Export>
        </Header.ButtonGroup>
      </Header>
    )
  }
}
