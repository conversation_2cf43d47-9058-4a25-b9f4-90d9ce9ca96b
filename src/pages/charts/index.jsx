import React, { Component } from 'react'
import { Loading, Dropdown, theme } from '@mz-codes/design-system'

import {
  getTickers,
  getShareholdersGroupedByCountry,
  getShareholdersGroupedByType,
  getShareholdersGroupedBySumCountry,
  getShareholdersGroupedBySumType,
  generateChartsReport,
} from 'client'

import { getAvailableDates, hocWrapper } from 'hooks'

import { i18n } from 'translate'
import { dateFromUTC, defineTheme, generateUniqueId, localInformation, formatDateToString } from 'utils'

import { ColumnChart, GoToHistoryButton, PieChart } from 'components'

import { ContentViewType } from 'consts'

import 'assets/styles/pages/_charts.scss'
import { StoreContext } from 'contexts/store'
import ChartHeader from './ChartHeader'

class BaseShareholderCharts extends Component {
  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      companyId: localInformation.getCompanyId(),
      tickers: [],
      currentTicker: null,
      datesAvailable: [],
      currentDate: null,
      mustRenderCountry: true,
      mustRenderCountrySum: true,
      mustRenderType: true,
      mustRenderTypeSum: true,
      currentTypeView: ContentViewType[0],
      currentTypeSumView: ContentViewType[0],
      currentCountryView: ContentViewType[0],
      currentCountrySumView: ContentViewType[0],
      countries: [],
      countriesSum: [],
      types: [],
      typesSum: [],
      totalStocks: 0,
      totalHolders: 0,
      isLoading: false,
    }
  }

  componentWillMount() {
    this.getTickers()
  }

  getTickers = () => {
    const { companyId } = this.state
    getTickers(companyId).then((res) => {
      if (res.success) {
        const options = res.data.map((ticker) => ({
          label: ticker.label,
          value: ticker.tickerId,
        }))

        const currentTicker = options[0] ?? null

        this.setState(
          {
            tickers: options,
            currentTicker,
          },
          () => currentTicker && this.getAvailableDates(currentTicker.value)
        )
      }
    })
  }

  getAvailableDates = (tickerId) => {
    const { companyId } = this.state
    getAvailableDates({ companyId, tickerId }).then((res) => {
      const parsedDates = res.sort((a, b) => (a > b ? 1 : -1)).map((date) => dateFromUTC(date))

      const lastAvaiableDate = parsedDates[parsedDates.length - 1]

      if (!lastAvaiableDate) {
        return this.setState({ isLoading: false })
      }

      return this.setState({
        datesAvailable: parsedDates,
        currentDate: lastAvaiableDate,
      })
    })
  }

  onChangeStockType = (ticker) => {
    this.setState(
      {
        currentTicker: ticker,
        mustRenderCountry: true,
        mustRenderCountrySum: true,
        mustRenderType: true,
        mustRenderTypeSum: true,
      },
      () => this.getAvailableDates(ticker.value)
    )
  }

  onChangeDate = (date) => {
    const { currentDate } = this.state
    if (date === currentDate) return

    this.setState({
      currentDate: date,
      mustRenderCountry: true,
      mustRenderCountrySum: true,
      mustRenderType: true,
      mustRenderTypeSum: true,
    })
  }

  onProcess = () => {
    this.setState({ isLoading: true }, () => {
      this.getShareholdersGroupedByCountry()
      this.getShareholdersGroupedByType()
      this.getShareholdersGroupedBySumCountry()
      this.getShareholdersGroupedBySumType()
    })
  }

  onProcessExport = async () => {
    try {
      await generateChartsReport({
        companyId: this.state.companyId,
        tickerId: this.state.currentTicker.value,
        referenceDate: formatDateToString(this.state.currentDate),
      })

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.export.error.title'),
        description: i18n.t('globals.export.error.message'),
      })
    }
  }

  getShareholdersGroupedByCountry = () => {
    getShareholdersGroupedByCountry(
      this.state.companyId,
      this.state.currentTicker.value,
      formatDateToString(this.state.currentDate)
    ).then((res) => {
      const data = res.data.map((item) => parseInt(item.count, 10))
      const names = res.data.map((item) => item.country)
      const totalHolders = data.reduce((accumulator, current) => accumulator + current, 0)
      this.setState({
        countryData: data,
        countryNames: names,
        mustRenderCountry: false,
        countries: res.data,
        totalHolders,
        isLoading: false,
      })
    })
  }

  getShareholdersGroupedByType = () => {
    getShareholdersGroupedByType(
      this.state.companyId,
      this.state.currentTicker.value,
      formatDateToString(this.state.currentDate)
    ).then((res) => {
      const data = res.data.map((item) => {
        return {
          y: parseInt(item.count, 10),
          name:
            item.shareholderType === 'FUND'
              ? i18n.t('fund').toUpperCase()
              : item.shareholderType === 'PRIVATE'
                ? i18n.t('private').toUpperCase()
                : i18n.t('unknown').toUpperCase(),
          color:
            item.shareholderType === 'FUND'
              ? theme.colors.neutral.grey[500]
              : item.shareholderType === 'PRIVATE'
                ? theme.colors.primary.base
                : theme.colors.neutral.grey[300],
        }
      })
      this.setState({
        groupedData: data,
        mustRenderCountrySum: false,
        types: res.data,
        isLoading: false,
      })
    })
  }

  getShareholdersGroupedBySumCountry = () => {
    getShareholdersGroupedBySumCountry(
      this.state.companyId,
      this.state.currentTicker.value,
      formatDateToString(this.state.currentDate)
    ).then((res) => {
      const data = res.data.map((item) => parseInt(item.sumStocks, 10))
      const totalStocks = data.reduce((accumulator, current) => accumulator + current, 0)
      const names = res.data.map((item) => item.country)
      this.setState({
        countrySumData: data,
        countrySumNames: names,
        mustRenderType: false,
        countriesSum: res.data,
        totalStocks,
        isLoading: false,
      })
    })
  }

  getShareholdersGroupedBySumType = () => {
    getShareholdersGroupedBySumType(
      this.state.companyId,
      this.state.currentTicker.value,
      formatDateToString(this.state.currentDate)
    ).then((res) => {
      const data = res.data.map((item) => {
        return {
          y: parseInt(item.sumStocks, 10),
          name:
            item.shareholderType === 'FUND'
              ? i18n.t('fund').toUpperCase()
              : item.shareholderType === 'PRIVATE'
                ? i18n.t('private').toUpperCase()
                : i18n.t('unknown').toUpperCase(),
          color:
            item.shareholderType === 'FUND'
              ? theme.colors.neutral.grey[500]
              : item.shareholderType === 'PRIVATE'
                ? theme.colors.primary.base
                : theme.colors.neutral.grey[300],
        }
      })
      this.setState({
        groupedSumData: data,
        mustRenderTypeSum: false,
        typesSum: res.data,
        isLoading: false,
      })
    })
  }

  onChangeTypeView = (viewType) => {
    const { currentTypeView } = this.state
    if (currentTypeView === viewType) return

    this.setState({ currentTypeView: viewType })
  }

  onChangeTypeSumView = (viewType) => {
    const { currentTypeSumView } = this.state
    if (currentTypeSumView === viewType) return

    this.setState({ currentTypeSumView: viewType })
  }

  onChangeTypeCountryView = (viewType) => {
    const { currentCountryView } = this.state
    if (currentCountryView === viewType) return

    this.setState({ currentCountryView: viewType })
  }

  onChangeTypeCountrySumView = (viewType) => {
    const { currentCountrySumView } = this.state
    if (currentCountrySumView === viewType) return

    this.setState({ currentCountrySumView: viewType })
  }

  render() {
    const {
      idiom,
      tickers,
      currentDate,
      datesAvailable,
      mustRenderCountry,
      mustRenderCountrySum,
      mustRenderType,
      mustRenderTypeSum,
      currentTicker,
      countryData,
      countryNames,
      countrySumData,
      countrySumNames,
      groupedData,
      groupedSumData,
      currentTypeView,
      currentTypeSumView,
      currentCountryView,
      currentCountrySumView,
      countries,
      countriesSum,
      types,
      typesSum,
      totalStocks,
      totalHolders,
      isLoading,
    } = this.state

    const formatted = (val, pref, suf) => {
      const language = idiom === 1 ? 'pt-BR' : 'en-US'
      return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
    }

    if (tickers && tickers.length === 0) {
      return (
        <div className="shareholders-wrapper">
          <div className={defineTheme('shareholders-content')}>
            <div className="lds-dual-ring">
              <div />
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className="shareholders-wrapper">
        <div className={defineTheme('shareholders-content')}>
          <div>
            <ChartHeader
              tickers={tickers}
              datesAvailable={datesAvailable}
              currentDate={currentDate}
              onChangeTicker={this.onChangeStockType}
              onChangeDate={this.onChangeDate}
              currentTicker={currentTicker}
              onProcess={this.onProcess}
              onProcessExport={this.onProcessExport}
            />

            {isLoading ? (
              <Loading />
            ) : !tickers || !datesAvailable || !currentDate ? (
              <div className="scroll">
                <div className="pie-chart">
                  <p>{i18n.t('uploadIntro')}</p>
                </div>
              </div>
            ) : !mustRenderCountry && !mustRenderCountrySum && !mustRenderType && !mustRenderTypeSum ? (
              <div className="scroll">
                <div className="charts-container">
                  <div className="pie-chart">
                    <div className="pie-chart-header">
                      <Dropdown selected={currentTypeView} options={ContentViewType} onChange={this.onChangeTypeView} />
                    </div>
                    {currentTypeView.value === '1' ? (
                      <PieChart
                        data={groupedData}
                        name={i18n.t('shareholderQuantity')}
                        title={i18n.t('shareholderQuantityByType')}
                      />
                    ) : (
                      <div className="table-content">
                        <h2 className="titulo">{i18n.t('shareholderQuantityByType')}</h2>
                        <div className="header-basic-list">
                          <ul className="basic-list dark">
                            <li className="header">
                              <span className="name">{i18n.t('type')}</span>
                              <span className="value">{i18n.t('value')}</span>
                            </li>
                          </ul>
                        </div>
                        <ul className="basic-list dark">
                          {types.map((item) => {
                            const key = generateUniqueId()
                            return (
                              <li key={key}>
                                <span className="name">
                                  {item.shareholderType === 'FUND'
                                    ? i18n.t('fund').toUpperCase()
                                    : item.shareholderType === 'PRIVATE'
                                      ? i18n.t('private').toUpperCase()
                                      : i18n.t('unknown').toUpperCase()}
                                </span>
                                <span className="value">{formatted(item.count)}</span>
                              </li>
                            )
                          })}
                        </ul>
                      </div>
                    )}
                  </div>
                  <div className="pie-chart">
                    <div className="pie-chart-header">
                      <Dropdown
                        selected={currentTypeSumView}
                        options={ContentViewType}
                        onChange={this.onChangeTypeSumView}
                      />
                    </div>
                    {currentTypeSumView.value === '1' ? (
                      <PieChart
                        data={groupedSumData}
                        name={i18n.t('sharesAmount')}
                        title={i18n.t('sharesAmountByType')}
                      />
                    ) : (
                      <div className="table-content">
                        <h2 className="titulo">{i18n.t('sharesAmountByType')}</h2>
                        <div className="header-basic-list">
                          <ul className="basic-list dark">
                            <li className="header">
                              <span className="name">{i18n.t('type')}</span>
                              <span className="value">{i18n.t('value')}</span>
                            </li>
                          </ul>
                        </div>
                        <ul className="basic-list dark">
                          {typesSum.map((item) => {
                            const key = generateUniqueId()
                            return (
                              <li key={key}>
                                <span className="name">
                                  {item.shareholderType === 'FUND'
                                    ? i18n.t('fund').toUpperCase()
                                    : item.shareholderType === 'PRIVATE'
                                      ? i18n.t('private').toUpperCase()
                                      : i18n.t('unknown').toUpperCase()}
                                </span>
                                <span className="value">{formatted(item.sumStocks)}</span>
                              </li>
                            )
                          })}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
                <div className="charts-container">
                  <div className="pie-chart">
                    <div className="pie-chart-header">
                      <Dropdown
                        selected={currentCountryView}
                        options={ContentViewType}
                        onChange={this.onChangeTypeCountryView}
                      />
                    </div>
                    {currentCountryView.value === '1' ? (
                      <ColumnChart
                        categories={countryNames}
                        data={countryData}
                        seriesName={i18n.t('holders')}
                        title={i18n.t('holdersByCountry')}
                        yAxisText={i18n.t('holders').toLowerCase()}
                      />
                    ) : (
                      <div className="table-content">
                        <h2 className="titulo">{i18n.t('holdersByCountry')}</h2>
                        <div className="header-basic-list">
                          <ul className="basic-list dark">
                            <li className="header">
                              <span className="name">{i18n.t('country')}</span>
                              <span className="value">{i18n.t('value')}</span>
                            </li>
                          </ul>
                        </div>
                        <ul className="basic-list dark">
                          {countries.map((item) => {
                            const key = generateUniqueId()
                            return (
                              <li key={key}>
                                <span className="name">{item.country}</span>
                                <span className="value">
                                  {`${formatted(item.count)} (${formatted(
                                    ((item.count * 100) / totalHolders).toFixed(2),
                                    '',
                                    '%'
                                  )})`}
                                </span>
                              </li>
                            )
                          })}
                        </ul>
                      </div>
                    )}
                  </div>
                  <div className="pie-chart">
                    <div className="pie-chart-header">
                      <Dropdown
                        selected={currentCountrySumView}
                        options={ContentViewType}
                        onChange={this.onChangeTypeCountrySumView}
                      />
                    </div>
                    {currentCountrySumView.value === '1' ? (
                      <ColumnChart
                        categories={countrySumNames}
                        data={countrySumData}
                        seriesName={i18n.t('shares')}
                        title={i18n.t('sharesByCountry')}
                        yAxisText={i18n.t('shares').toLowerCase()}
                      />
                    ) : (
                      <div className="table-content">
                        <h2 className="titulo">{i18n.t('sharesByCountry')}</h2>
                        <div className="header-basic-list">
                          <ul className="basic-list dark">
                            <li className="header">
                              <span className="name">{i18n.t('country')}</span>
                              <span className="value">{i18n.t('value')}</span>
                            </li>
                          </ul>
                        </div>
                        <ul className="basic-list dark">
                          {countriesSum.map((item) => {
                            const key = generateUniqueId()
                            return (
                              <li key={key}>
                                <span className="name">{item.country}</span>
                                <span className="value">{`${formatted(item.sumStocks)} (${formatted(
                                  ((item.sumStocks * 100) / totalStocks).toFixed(2),
                                  '',
                                  '%'
                                )})`}</span>
                              </li>
                            )
                          })}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="scroll">
                <div className="pie-chart">
                  <p>{i18n.t('loadChart')}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }
}

BaseShareholderCharts.contextType = StoreContext

const ShareholderCharts = hocWrapper(BaseShareholderCharts)

export default ShareholderCharts
