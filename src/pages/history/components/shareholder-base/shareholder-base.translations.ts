import { i18n } from 'translate'

export const translations = {
  stockType: i18n.t('globals.headerContent.stockType'),
  status: i18n.t('globals.status'),
  tickerLabel: i18n.t('tickerPriceHistory.Ticker'),
  dateLabel: i18n.t('tickerPriceHistory.Date'),
  startDate: i18n.t('tickerPriceHistory.StartDateLabel'),
  endDate: i18n.t('tickerPriceHistory.EndDateLabel'),
  quantity: i18n.t('globals.headerContent.quantity'),
  uploadedAt: i18n.t('globals.uploadedAt'),
  referenceDate: i18n.t('globals.referenceDate'),
  fileName: i18n.t('exportHistory.fileName'),
  totalAmount: i18n.t('totalAmount'),
  actions: i18n.t('globals.actions'),
  failToUpload: i18n.t('statusError.failToUpload'),
  noHistoryFound: i18n.t('exportHistory.noHistoryFound'),
  deleteModalTitle: i18n.t('shareholderBaseHistory.deleteModal.title'),
  deleteModalLabel: i18n.t('shareholderBaseHistory.deleteModal.label'),
  toasts: {
    deleteShareholderBase: {
      successTitle: i18n.t('globals.toasts.deleteShareholdersBase.success.title'),
      successDescription: i18n.t('globals.toasts.deleteShareholdersBase.success.description'),
      errorTitle: i18n.t('globals.toasts.deleteShareholdersBase.error.title'),
      errorDescription: i18n.t('globals.toasts.deleteShareholdersBase.error.description'),
    },
    error: {
      title: i18n.t('globals.errors.requestFail.title'),
      description: i18n.t('globals.errors.requestFail.message'),
    },
  },
}
