import { Dropdown, Loading, NewDatepicker, Header, Table } from '@mz-codes/design-system'

import { Page, PageContent, ReasonModal } from 'components'
import { COMMON } from 'consts'
import { i18n } from 'translate'
import { ShareholderBaseListItem } from '../shareholder-base-list-item'
import { THistoryTemplateProps, TPositionType } from './shareholder-base.types'

export function ShareholderBaseTemplate(props: THistoryTemplateProps): React.ReactElement {
  const {
    translations,
    handleTickerValue,
    handleStatusValue,
    handleQuantityValue,
    startDate,
    endDate,
    handleStartDateValue,
    handleEndDateValue,
    availableDates,
    currentTicker,
    statusLabel,
    statusLabels,
    setOrderBy,
    orderBy,
    setTableDirection,
    tableDirection,
    rows,
    isLoading,
    tickers,
    positions,
    handleReprocessBase,
    handleGetBatchFile,
    handleToggleOrder,
    handleDeleteBase,
    handleDeleteBaseModal,
    showDeleteModal,
    hasPermissionToDeleteBase,
    reasonDeleteBaseConfig,
  } = props

  return (
    <Page>
      <Header>
        <Header.Content width="100%">
          <Header.Item>
            <Header.Label>{translations.stockType}</Header.Label>
            <Dropdown options={tickers || []} onChange={handleTickerValue} selected={currentTicker} />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.status}</Header.Label>
            <Dropdown options={statusLabels} onChange={handleStatusValue} selected={statusLabel} />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.quantity}</Header.Label>
            <Dropdown options={COMMON.topQuantity} onChange={handleQuantityValue} selected={rows} />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.startDate}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={startDate}
              onChange={handleStartDateValue}
              availableDates={availableDates?.filter((date) => (endDate ? date <= endDate : true))}
              hint={i18n.t('startDate')}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.endDate}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={endDate}
              onChange={handleEndDateValue}
              availableDates={availableDates?.filter((date) => (startDate ? date >= startDate : true))}
              hint={i18n.t('endDate')}
            />
          </Header.Item>
        </Header.Content>
      </Header>
      <PageContent>
        <Table>
          <Table.THead>
            <Table.TR>
              <Table.TH
                onClick={() => {
                  handleToggleOrder({
                    orderBy,
                    clickedTable: 'uploaded',
                    tableDirection,
                    setOrderBy,
                    setTableDirection,
                  })
                }}
              >
                {translations.uploadedAt}
              </Table.TH>
              <Table.TH
                onClick={() => {
                  handleToggleOrder({
                    orderBy,
                    clickedTable: 'referenceDate',
                    tableDirection,
                    setOrderBy,
                    setTableDirection,
                  })
                }}
              >
                {translations.referenceDate}
              </Table.TH>
              <Table.TH>{translations.fileName}</Table.TH>
              <Table.TH $textAlign="right">{translations.totalAmount}</Table.TH>
              <Table.TH>{translations.status}</Table.TH>
              <Table.TH $textAlign="center">{translations.actions}</Table.TH>
            </Table.TR>
          </Table.THead>
          {isLoading && (
            <div>
              <Loading />
            </div>
          )}
          <Table.TBody>
            {!isLoading &&
              positions &&
              positions?.map((value: TPositionType) => {
                return (
                  <ShareholderBaseListItem
                    value={value}
                    handleGetBatchFile={handleGetBatchFile}
                    handleReprocessBase={handleReprocessBase}
                    hasPermissionToDeleteBase={hasPermissionToDeleteBase}
                    handleDeleteBaseModal={handleDeleteBaseModal}
                    key={value.positionBatchId}
                  />
                )
              })}
            {!isLoading && positions?.length === 0 && <p>{translations.noHistoryFound}</p>}
          </Table.TBody>
        </Table>
        <ReasonModal
          title={translations.deleteModalTitle}
          label={translations.deleteModalLabel}
          show={showDeleteModal}
          onClose={handleDeleteBaseModal}
          onConfirm={handleDeleteBase}
          maxLength={reasonDeleteBaseConfig.maxLength}
          minLength={reasonDeleteBaseConfig.minLength}
        />
      </PageContent>
    </Page>
  )
}
