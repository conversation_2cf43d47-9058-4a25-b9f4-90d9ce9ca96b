import { useCallback, useEffect, useState } from 'react'
import { TOption, useToast } from '@mz-codes/design-system'

import { getCompany } from 'globals/storages/locals'
import { getTickers } from 'globals/services/tickers'

import { COMMON } from 'consts'
import {
  subtractDays,
  formatDateToString,
  shareholderBaseStatus,
  dateFromUTC,
  hasScopePermission,
  doDownload,
} from 'utils'
import {
  getAvailableDates,
  getHistoryPositions,
  historyReprocessBase,
  getHistoryPositionFile,
  deleteShareholderBase,
} from 'hooks'

import {
  THandleGetBatchFileArgs,
  THandleReprocessBase,
  TPositionType,
  TToggleOrderProps,
} from './shareholder-base.types'
import { ShareholderBaseTemplate } from './shareholder-base.template'
import { translations } from './shareholder-base.translations'

export function ShareholderBase() {
  const statusLabels: Array<TOption> = shareholderBaseStatus
  const { id: companyId } = getCompany()

  const { createToast } = useToast()

  const [positions, setPositions] = useState<Array<TPositionType>>([])

  const [tickersState, setTickersState] = useState<Array<TOption>>([])
  const [currentTickerState, setCurrentTickerState] = useState<TOption>({} as TOption)

  const [orderByState, setOrderByState] = useState<'uploaded' | 'referenceDate'>('uploaded')

  const [tableDirectionState, setTableDirectionState] = useState<'DESC' | 'ASC'>('DESC')

  const [rowsState, setRowsState] = useState<TOption>(COMMON.topQuantity[1])
  const [statusLabel, setStatusLabel] = useState({
    label: statusLabels[0].label,
    value: statusLabels[0].value,
  })

  const [startDateState, setStartDateState] = useState(subtractDays(new Date(), 365))
  const [endDateState, setEndDateState] = useState(new Date())
  const [availableDates, setAvailableDates] = useState<Array<Date>>([])

  const [isLoading, setIsLoading] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [batchIdToDelete, setBatchIdToDelete] = useState<null | string>(null)

  const reasonDeleteBaseConfig = {
    maxLength: 255,
    minLength: 40,
  }
  const hasPermissionToDeleteBase: boolean = hasScopePermission(
    'mzshareholders:position:companies:id:position:batch:id:delete'
  )

  const handleDeleteBaseModal = (batchId?: string) => {
    if (!hasPermissionToDeleteBase) return

    setBatchIdToDelete(batchId || null)
    setShowDeleteModal(!showDeleteModal)
  }

  const handleTickerValue = (option: TOption) => {
    if (option === currentTickerState) return
    setCurrentTickerState(option)
  }

  const handleStatusValue = (option: TOption) => {
    if (option === statusLabel) return
    setStatusLabel(option)
  }

  const handleQuantityValue = (option: TOption) => {
    if (option === rowsState) return
    setRowsState(option)
  }

  const handleStartDateValue = (value: Date | null) => {
    if (value === startDateState || !value) return
    setStartDateState(value)
  }

  const handleEndDateValue = (value: Date | null) => {
    if (value === endDateState || !value) return

    setEndDateState(value)
  }

  const handleDeleteBase = async (reason: string) => {
    setShowDeleteModal(false)
    setIsLoading(true)
    try {
      if (!batchIdToDelete) return

      await deleteShareholderBase({ companyId, positionBatchId: batchIdToDelete, reason })
      createToast({
        type: 'success',
        title: translations.toasts.deleteShareholderBase.successTitle,
        description: translations.toasts.deleteShareholderBase.successDescription,
      })
      handleGetPositions()
    } catch (err: unknown) {
      createToast({
        type: 'error',
        title: translations.toasts.deleteShareholderBase.errorTitle,
        description: translations.toasts.deleteShareholderBase.errorDescription,
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleGetPositions = useCallback(async () => {
    setIsLoading(true)
    try {
      const response = await getHistoryPositions({
        tickerId: currentTickerState.value,
        limit: rowsState.value as number,
        orderBy: orderByState,
        direction: tableDirectionState,
        status: statusLabel.value,
        startDate: formatDateToString(startDateState),
        endDate: formatDateToString(endDateState),
      })
      setPositions(response.data)
    } catch (err: unknown) {
      createToast({
        type: 'error',
        title: translations.toasts.error.title,
        description: translations.toasts.error.description,
      })
    } finally {
      setIsLoading(false)
    }
  }, [
    createToast,
    currentTickerState.value,
    endDateState,
    orderByState,
    rowsState.value,
    startDateState,
    statusLabel.value,
    tableDirectionState,
  ])

  const handleReprocessBase = async ({ positionBatchId, setIsReprocessing }: THandleReprocessBase) => {
    setIsReprocessing(true)
    await historyReprocessBase({ companyId, positionBatchId })
    setIsReprocessing(false)
  }

  const handleGetBatchFile = async ({ positionBatchId, fileName, setIsDownloading }: THandleGetBatchFileArgs) => {
    setIsDownloading(true)
    const response = await getHistoryPositionFile({
      tickerId: currentTickerState.value,
      positionBatchId,
    })
    doDownload(response, fileName)
    setIsDownloading(false)
  }

  const handleToggleOrder = ({
    orderBy,
    clickedTable,
    tableDirection,
    setOrderBy,
    setTableDirection,
  }: TToggleOrderProps) => {
    if (orderBy === clickedTable) {
      setTableDirection(tableDirection !== 'DESC' ? 'DESC' : 'ASC')
    } else {
      setOrderBy(orderBy !== 'uploaded' ? 'uploaded' : 'referenceDate')
      setTableDirection('DESC')
    }
  }

  const handleGetTickers = useCallback(async () => {
    setIsLoading(true)
    const tickersToMap = await getTickers(companyId)
    const options = tickersToMap.map<TOption>((ticker) => ({
      label: ticker.label,
      value: ticker.tickerId,
    }))
    setTickersState(options)
    setCurrentTickerState(options[0])
  }, [companyId])

  useEffect(() => {
    handleGetTickers()
  }, [handleGetTickers])

  useEffect(() => {
    if (!currentTickerState.value) return

    const fetchAvailableDates = async () => {
      setIsLoading(true)

      const response = await getAvailableDates({ companyId, tickerId: currentTickerState.value as string })
      const parsedDates: Date[] = response
        .sort((a: string, b: string) => (a > b ? 1 : -1))
        .map((date: string) => dateFromUTC(date))

      const lastAvailableDate = parsedDates[parsedDates.length - 1]

      setAvailableDates(parsedDates)

      if (!lastAvailableDate) return

      setStartDateState(parsedDates[0])
      setEndDateState(lastAvailableDate)
      setIsLoading(false)
    }
    fetchAvailableDates()
  }, [companyId, currentTickerState])

  useEffect(() => {
    if (!currentTickerState?.value) return
    handleGetPositions()
  }, [
    currentTickerState,
    rowsState.value,
    orderByState,
    tableDirectionState,
    statusLabel.value,
    startDateState,
    endDateState,
    handleGetPositions,
  ])

  return (
    <ShareholderBaseTemplate
      translations={translations}
      handleTickerValue={handleTickerValue}
      handleStatusValue={handleStatusValue}
      handleQuantityValue={handleQuantityValue}
      startDate={startDateState}
      endDate={endDateState}
      handleStartDateValue={handleStartDateValue}
      handleEndDateValue={handleEndDateValue}
      availableDates={availableDates}
      setCurrentTicker={setCurrentTickerState}
      currentTicker={currentTickerState}
      setStatusLabel={setStatusLabel}
      statusLabel={statusLabel}
      statusLabels={statusLabels}
      setRows={setRowsState}
      rows={rowsState}
      setOrderBy={setOrderByState}
      orderBy={orderByState}
      setTableDirection={setTableDirectionState}
      tableDirection={tableDirectionState}
      isLoading={isLoading}
      tickers={tickersState}
      positions={positions}
      handleReprocessBase={handleReprocessBase}
      handleGetBatchFile={handleGetBatchFile}
      handleToggleOrder={handleToggleOrder}
      handleDeleteBaseModal={handleDeleteBaseModal}
      showDeleteModal={showDeleteModal}
      hasPermissionToDeleteBase={hasPermissionToDeleteBase}
      handleDeleteBase={handleDeleteBase}
      reasonDeleteBaseConfig={reasonDeleteBaseConfig}
    />
  )
}
