import { TOption } from '@mz-codes/design-system'
import { Dispatch } from 'react'
import { translations } from './shareholder-base.translations'

type TTranslation = typeof translations

export type TPositionType = {
  fileName: string
  isMigration: boolean
  positionBatchId: string
  possibleDateMismatch: boolean
  processedAt: string
  referenceDate: string
  skipAutoClosingPrice: boolean
  status: number
  totalShareholders: string
  totalShareholdersUnidentified: string
  totalStocks: number
  uploadedAt: string
}

export type TTableDirection = 'ASC' | 'DESC'

export type TToggleOrderProps = {
  orderBy: 'uploaded' | 'referenceDate'
  clickedTable: 'uploaded' | 'referenceDate'
  tableDirection: TTableDirection
  setOrderBy: (arg: 'uploaded' | 'referenceDate') => void
  setTableDirection: (arg: TTableDirection) => void
}

export type THandleGetBatchFileArgs = {
  positionBatchId: string
  fileName: string
  setIsDownloading: (arg: boolean) => void
}

export type THandleReprocessBase = {
  positionBatchId: string
  setIsReprocessing: (arg: boolean) => void
}

export type THistoryTemplateProps = {
  translations: TTranslation
  handleTickerValue(option: TOption): void
  handleStatusValue(option: TOption): void
  handleQuantityValue(option: TOption): void
  startDate: Date
  endDate: Date
  handleStartDateValue(args: Date | null): void
  handleEndDateValue(args: Date | null): void
  availableDates?: Array<Date>
  setCurrentTicker: (args: TOption) => void
  currentTicker: TOption
  setStatusLabel: (args: TOption) => void
  statusLabel: TOption
  statusLabels: Array<TOption>
  setOrderBy: (args: 'uploaded' | 'referenceDate') => void
  orderBy: 'uploaded' | 'referenceDate'
  setTableDirection: (args: 'DESC' | 'ASC') => void
  tableDirection: 'DESC' | 'ASC'
  setRows: Dispatch<TOption>
  rows: TOption
  isLoading: boolean
  tickers: Array<TOption>
  positions?: Array<TPositionType>
  handleReprocessBase: (args: THandleReprocessBase) => void
  handleGetBatchFile: (args: THandleGetBatchFileArgs) => void
  handleToggleOrder: (params: TToggleOrderProps) => void
  showDeleteModal: boolean
  hasPermissionToDeleteBase: boolean
  handleDeleteBaseModal: (batchId?: string) => void
  handleDeleteBase: (reason: string) => void
  reasonDeleteBaseConfig: TReasonDeleteBaseConfig
}

type TReasonDeleteBaseConfig = {
  maxLength: number
  minLength: number
}

export type THandleGetPositionsArgs = {
  currentTicker: string | number
  rows: number
  column: string
  direction: string
  statusValue: string | number
  startDate: Date
  endDate: Date
}
