import {
  TPositionType,
  THandleGetBatchFileArgs,
  THandleReprocessBase,
} from '../shareholder-base/shareholder-base.types'

export type TStatusTypes = 'partialSuccess' | 'success' | 'error'

export type TStatusTextProps = {
  status: TStatusTypes
}

export type TShareholderBaseListItem = {
  value: TPositionType
  handleGetBatchFile: (args: THandleGetBatchFileArgs) => void
  handleReprocessBase: (args: THandleReprocessBase) => void
  hasPermissionToDeleteBase: boolean
  handleDeleteBaseModal: (batchId?: string) => void
}

export type TShareholderBaseListItemTemplate = {
  handleStatusMessage(statusCode: number): string
  getStatusColor(statusCode: number): TStatusTypes
  value: TPositionType
  handleConfirmReprocessBase(): void
  isReprocessing: boolean
  isDownloading: boolean
  disableReprocess: boolean
  metadata: Array<string>
  handleDownloadList(): void
  dateFormat: string
  dateTimeAmPmFormat: string
  showModal: boolean
  handleCloseModal(): void
  handleOpenModal(): void
  formatTotalStocks(val: number | string): string
  isDownloadAllowed: boolean
  showDeleteButton: boolean
  deleteBase: () => void
}

export enum StatusCode {
  Deleted = 0,
  Uploading = 1,
  Processing = 2,
  Success = 3,
  AwaitingDeletion = 4,
  InProcessing = 5,
}
