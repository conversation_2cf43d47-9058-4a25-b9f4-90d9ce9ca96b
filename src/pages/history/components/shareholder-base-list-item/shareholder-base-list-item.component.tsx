import { useState } from 'react'
import { format } from 'date-fns'

import { i18n } from 'translate'
import { dateTimeFromUTC } from 'utils'

import { StatusCode, TShareholderBaseListItem, TStatusTypes } from './shareholder-base-list-item.types'
import { ShareholderBaseListItemTemplate } from './shareholder-base-list-item.template'
import { translations } from './shareholder-base-list-item.translations'

const { dateTimeAmPmFormat } = translations
const { dateFormat } = translations

function formatTotalStocks(val: number | string) {
  return `${Number(val).toLocaleString(i18n.language)}`
}

export function ShareholderBaseListItem(props: TShareholderBaseListItem) {
  const { value, handleGetBatchFile, handleReprocessBase, handleDeleteBaseModal, hasPermissionToDeleteBase } = props
  const [isDownloading, setIsDownloading] = useState(false)
  const [isReprocessing, setIsReprocessing] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [positionBatchId, setPositionBatchId] = useState('')

  const statusToDisableReprocess = [StatusCode.Uploading, StatusCode.Processing, StatusCode.AwaitingDeletion]
  const disableReprocess = value.status === 900 || statusToDisableReprocess.includes(value.status)

  const getStatusColor = (statusCode: number) => {
    const statusClass: { [key: number]: TStatusTypes } = {
      1: 'partialSuccess',
      2: 'partialSuccess',
      3: 'success',
      4: 'partialSuccess',
      5: 'partialSuccess',
      99: 'error',
    }
    return statusClass[statusCode] || (statusClass[99] as TStatusTypes)
  }

  const handleStatusMessage = (statusCode: number) => {
    const statusMessage: { [key: number]: string } = {
      1: translations.loading,
      2: translations.processing,
      3: translations.imported,
      4: translations.awaitingDeletion,
      5: translations.inProcessing,
      80: translations.overwritten,
      90: translations.errorCountries,
      91: translations.errorPositionInfo,
      92: translations.errorCustodianBank,
      93: translations.errorDownloadingFiles,
      94: translations.errorExtractingDates,
      95: translations.errorReadingFiles,
      96: translations.errorStockAmount,
      97: translations.errorDiffDates,
      98: translations.errorLoadingHolidays,
      99: translations.failToUpload,
      900: translations.invalidDocuments,
    }
    return (statusMessage[statusCode] || statusMessage[99]) as string
  }

  const metadata = [
    `${i18n.t('processedAt')}:  ${
      value?.processedAt ? format(dateTimeFromUTC(value?.processedAt), dateTimeAmPmFormat) : '  --'
    }`,
    `${i18n.t('totalShareholders')}:  ${formatTotalStocks(value?.totalShareholders)}`,
    `${i18n.t('totalShareholdersUnidentified')}:  ${formatTotalStocks(value?.totalShareholdersUnidentified)}`,
  ]

  if (value.status === 900) {
    metadata.push(translations.tooltipMessageError)
  }

  const handleDownloadList = () => {
    handleGetBatchFile({
      positionBatchId: value.positionBatchId,
      fileName: value.fileName,
      setIsDownloading,
    })
  }

  const handleConfirmReprocessBase = () => {
    handleReprocessBase({ positionBatchId, setIsReprocessing })
    setShowModal(false)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleOpenModal = () => {
    setShowModal(true)
    setPositionBatchId(value?.positionBatchId)
  }

  const isDateAfterOneYearAgo = (dateString: string) => {
    const inputDate = new Date(dateString)
    const oneYearAgo = new Date()
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
    return inputDate > oneYearAgo
  }

  const canDeleteBase = () => {
    const { status, uploadedAt } = value

    const statusCannotDelete = [StatusCode.AwaitingDeletion, StatusCode.Deleted].includes(status)
    if (!hasPermissionToDeleteBase || statusCannotDelete) return false

    const statusCanDeleteWithTimeout = [StatusCode.Uploading, StatusCode.Processing].includes(status)
    if (statusCanDeleteWithTimeout) {
      const diffMs = new Date().getTime() - new Date(uploadedAt).getTime()
      const sixHoursInMs = 6 * 60 * 60 * 1000

      const isMoreThanSixHours = diffMs > sixHoursInMs

      return isMoreThanSixHours
    }

    return true
  }

  const showDeleteButton = canDeleteBase()

  const handleDeleteBase = () => {
    if (!value.positionBatchId || !hasPermissionToDeleteBase) return

    handleDeleteBaseModal(value.positionBatchId)
  }

  return (
    <ShareholderBaseListItemTemplate
      handleStatusMessage={handleStatusMessage}
      getStatusColor={getStatusColor}
      value={value}
      handleConfirmReprocessBase={handleConfirmReprocessBase}
      isReprocessing={isReprocessing}
      isDownloading={isDownloading}
      disableReprocess={disableReprocess}
      metadata={metadata}
      handleDownloadList={handleDownloadList}
      dateFormat={dateFormat}
      showModal={showModal}
      dateTimeAmPmFormat={dateTimeAmPmFormat}
      handleCloseModal={handleCloseModal}
      handleOpenModal={handleOpenModal}
      formatTotalStocks={formatTotalStocks}
      isDownloadAllowed={isDateAfterOneYearAgo(value.uploadedAt)}
      showDeleteButton={showDeleteButton}
      deleteBase={handleDeleteBase}
    />
  )
}
