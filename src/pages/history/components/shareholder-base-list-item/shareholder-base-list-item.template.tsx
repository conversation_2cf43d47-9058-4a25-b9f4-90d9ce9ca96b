import { format } from 'date-fns'

import { Loading, Icons, ConfirmationModal, Tooltip, ALIGNMENTS, IconButton, Table } from '@mz-codes/design-system'
import { dateFromUTC, dateTimeFromUTC } from 'utils'
import { translations } from './shareholder-base-list-item.translations'
import { TShareholderBaseListItemTemplate } from './shareholder-base-list-item.types'
import { StatusText, ContainerActions } from './shareholder-base-list-item.styled'

export function ShareholderBaseListItemTemplate(props: TShareholderBaseListItemTemplate) {
  const {
    handleStatusMessage,
    getStatusColor,
    value,
    handleConfirmReprocessBase,
    isReprocessing,
    isDownloading,
    disableReprocess,
    metadata,
    handleDownloadList,
    dateFormat,
    dateTimeAmPmFormat,
    showModal,
    handleCloseModal,
    handleOpenModal,
    formatTotalStocks,
    isDownloadAllowed,
    deleteBase,
    showDeleteButton,
  } = props
  return (
    <>
      <Table.TR key={value.positionBatchId}>
        <Table.TD style={{ width: '185px' }}>{format(dateTimeFromUTC(value.uploadedAt), dateTimeAmPmFormat)}</Table.TD>
        <Table.TD style={{ width: '160px' }}>
          {value.referenceDate ? format(dateFromUTC(value.referenceDate), dateFormat) : '--'}
        </Table.TD>
        <Table.TD>{value.fileName}</Table.TD>
        <Table.TD style={{ width: '200px' }}>{formatTotalStocks(value.totalStocks)}</Table.TD>
        <Table.TD style={{ position: 'initial', overflow: 'initial', width: '185px' }}>
          <Tooltip
            $alignment={ALIGNMENTS.BOTTOM_LEFT}
            $width="auto"
            $tooltipStyle={{
              width: 'max-content',
              whiteSpace: 'break-spaces',
              padding: '15px',
              lineHeight: '20px',
            }}
            $wrapperStyle={{}}
            text={metadata.join('\n\n')}
          >
            <StatusText status={getStatusColor(value.status)}>{handleStatusMessage(value.status)}</StatusText>
          </Tooltip>
        </Table.TD>
        <Table.TD style={{ position: 'initial', overflow: 'initial', width: '130px' }}>
          <ContainerActions>
            {!disableReprocess && !isReprocessing && (
              <Tooltip $alignment={ALIGNMENTS.BOTTOM_CENTER} $width="auto" text={translations.reprocess as string}>
                <IconButton onClick={handleOpenModal}>
                  <Icons.Reload size={18} />
                </IconButton>
              </Tooltip>
            )}
            {isReprocessing && <Loading />}
            {!isDownloading && isDownloadAllowed && (
              <Tooltip $alignment={ALIGNMENTS.BOTTOM_CENTER} $width="auto" text={translations.download as string}>
                <IconButton onClick={handleDownloadList}>
                  <Icons.Download size={18} />
                </IconButton>
              </Tooltip>
            )}
            {isDownloading && <Loading />}
            {showDeleteButton && (
              <Tooltip $alignment={ALIGNMENTS.BOTTOM_LEFT} $width="auto" text={translations.delete as string}>
                <IconButton onClick={deleteBase}>
                  <Icons.Trash size={18} />
                </IconButton>
              </Tooltip>
            )}
          </ContainerActions>
        </Table.TD>
      </Table.TR>
      <ConfirmationModal
        message={translations.confirmMessage as string}
        onClose={handleCloseModal}
        onConfirm={handleConfirmReprocessBase}
        show={showModal}
        cancelButtonLabel={translations.cancelButtonLabel}
        confirmButtonLabel={translations.confirmButtonLabel}
      />
    </>
  )
}
