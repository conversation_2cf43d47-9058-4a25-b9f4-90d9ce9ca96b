import styled from 'styled-components'
import { theme } from '@mz-codes/design-system'
import { TStatusTextProps } from './shareholder-base-list-item.types'

const StatusColor: Record<TStatusTextProps['status'], string> = {
  partialSuccess: theme.colors.semantic.yellow[500],
  success: theme.colors.semantic.green[500],
  error: theme.colors.semantic.red[500],
}

export const ContainerActions = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
`

export const IconContainer = styled.div`
  cursor: pointer;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  color: ${theme.colors.neutral.white}
  justify-content: center;
`

export const StatusText = styled.span<TStatusTextProps>`
  color: ${(props) => props.status && StatusColor[props.status]};
  margin-left: 4px;
`
