import { i18n } from 'translate'

export const translations = {
  loading: i18n.t('globals.loading'),
  processing: i18n.t('exportHistory.Status.Processing'),
  imported: i18n.t('globals.imported'),
  overwritten: i18n.t('globals.overwritten'),
  awaitingDeletion: i18n.t('globals.awaitingDeletion'),
  inProcessing: i18n.t('exportHistory.Status.InProcessing'),
  errorCountries: i18n.t('statusError.errorCountries'),
  errorPositionInfo: i18n.t('statusError.errorPositionInfo'),
  errorCustodianBank: i18n.t('statusError.errorCustodianBank'),
  errorDownloadingFiles: i18n.t('statusError.errorDownloadingFiles'),
  errorExtractingDates: i18n.t('statusError.errorExtractingDates'),
  errorReadingFiles: i18n.t('statusError.errorReadingFiles'),
  errorStockAmount: i18n.t('statusError.errorStockAmount'),
  errorDiffDates: i18n.t('statusError.errorDiffDates'),
  errorLoadingHolidays: i18n.t('statusError.errorLoadingHolidays'),
  failToUpload: i18n.t('statusError.failToUpload'),
  invalidDocuments: i18n.t('statusError.invalidDocuments'),
  dateTimeAmPmFormat: i18n.t('globals.dateFormatAmPm'),
  dateFormat: i18n.t('globals.DatePickerDateFormat'),
  confirmMessage: i18n.t('exportHistory.reprocess.confirmMessage'),
  confirmTitle: i18n.t('exportHistory.reprocess.confirmTitle'),
  download: i18n.t('exportHistory.actions.download'),
  reprocess: i18n.t('exportHistory.actions.reprocess'),
  delete: i18n.t('exportHistory.actions.delete'),
  tooltipMessageError: i18n.t('exportHistory.tooltipMessageError'),
  cancelButtonLabel: i18n.t('components.confirmModal.cancelButton'),
  confirmButtonLabel: i18n.t('components.confirmModal.confirmButton'),
}
