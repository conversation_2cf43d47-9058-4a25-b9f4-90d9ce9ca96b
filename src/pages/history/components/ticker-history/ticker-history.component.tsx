import { useState, useEffect, useCallback } from 'react'
import moment from 'moment'
import { getCompany } from 'globals/storages/locals'

import { getTickers, ticker } from 'globals/services/tickers'

import { getSerializedTickerPriceHistory, TPriceHistory, postTickerPriceHistoryExport } from 'hooks'
import { GoToHistoryButton } from 'components'
import { TOption, useToast } from '@mz-codes/design-system'

import { i18n } from 'translate'
import { formatDateToString } from 'utils'
import { BaseError, SelectedTickerNotFound } from 'errors'
import { translations } from './ticker-history.translations'
import { TickerHistoryTemplate } from './ticker-history.template'

export function TickerHistory() {
  const company = getCompany()
  const currentLanguage = i18n.language.startsWith('pt') ? 'pt-BR' : 'en-US'
  const startDate = moment().subtract(7, 'days').toDate()
  const endDate = moment().subtract(1, 'days').toDate()

  const { createToast } = useToast()
  const [tickers, setTickers] = useState<ticker[]>([])
  const [currentTicker, setCurrentTicker] = useState<TOption>()
  const [tickerOptions, setTickerOptions] = useState<TOption[]>()
  const [selectedStartDate, setSelectedStartDate] = useState(startDate)
  const [selectedEndDate, setSelectedEndDate] = useState(endDate)
  const [listItems, setListItems] = useState<TPriceHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const selectedTicker = tickers.find((tickerParam) => tickerParam.tickerId === currentTicker?.value)

  const handleGetTickers = useCallback(async () => {
    const response = await getTickers(company.id)

    const tickersFiltered = response?.filter((item) => item.label !== 'TOTAL')

    const options = tickersFiltered.map<TOption>((item) => ({
      label: item.label,
      value: item.tickerId,
    }))
    setTickers(tickersFiltered)
    setCurrentTicker(options[0])
    setTickerOptions(options)
  }, [company.id])

  useEffect(() => {
    handleGetTickers()
  }, [handleGetTickers])

  const handleLoadTickerPriceHistory = useCallback(async () => {
    setIsLoading(true)
    try {
      if (!selectedTicker) throw new SelectedTickerNotFound()

      const data = await getSerializedTickerPriceHistory({
        ticker: selectedTicker.xigniteTicker,
        startDate: formatDateToString(selectedStartDate),
        endDate: formatDateToString(selectedEndDate),
      })
      setListItems(data)
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 20000,
          type: 'error',
        })
        return
      }
      createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        duration: 20000,
        type: 'error',
      })
    } finally {
      setIsLoading(false)
    }
  }, [createToast, selectedEndDate, selectedStartDate, selectedTicker])

  useEffect(() => {
    if (!currentTicker?.value) return
    handleLoadTickerPriceHistory()
  }, [currentTicker, selectedStartDate, selectedEndDate, handleLoadTickerPriceHistory])

  const handleTickerValue = (selectedTickerParam: TOption) => {
    if (currentTicker?.value === selectedTickerParam.value) return
    setCurrentTicker(selectedTickerParam)
  }

  const handleStartDateValue = (value: Date) => {
    if (!value || value === selectedStartDate) return
    setSelectedStartDate(value)
  }

  const handleEndDateValue = (value: Date) => {
    if (!value || value === selectedEndDate) return
    setSelectedEndDate(value)
  }

  const onExportClick = async () => {
    try {
      if (!selectedTicker) throw new SelectedTickerNotFound()

      await postTickerPriceHistoryExport({
        companyId: company.id,
        companyName: company.displayName,
        tickerId: selectedTicker.tickerId,
        ticker: selectedTicker.xigniteTicker,
        stockType: selectedTicker.label.toString(),
        startDate: formatDateToString(selectedStartDate),
        endDate: formatDateToString(selectedEndDate),
        language: currentLanguage,
      })
      createToast({
        title: translations.exportSuccess as string,
        description: translations.exportMessage as string,
        duration: 9000,
        type: 'success',
        buttons: <GoToHistoryButton />,
      })
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
      createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        duration: 9000,
        type: 'error',
      })
    }
  }

  return (
    <TickerHistoryTemplate
      translations={translations}
      isLoading={isLoading}
      listItems={listItems}
      tickerOptions={tickerOptions}
      handleTickerValue={handleTickerValue}
      currentTicker={currentTicker}
      handleStartDateValue={handleStartDateValue}
      selectedStartDate={selectedStartDate}
      handleEndDateValue={handleEndDateValue}
      selectedEndDate={selectedEndDate}
      onExportClick={onExportClick}
      currentLanguage={currentLanguage}
    />
  )
}
