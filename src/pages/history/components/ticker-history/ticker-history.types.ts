import { TPriceHistory } from 'hooks'
import { TOption } from '@mz-codes/design-system'
import { translations } from './ticker-history.translations'

export type TTranslations = typeof translations

export type TTickerHistoryTemplate = {
  translations: TTranslations
  isLoading: boolean
  currentTicker?: TOption
  tickerOptions?: TOption[]
  handleTickerValue: (option: TOption) => void
  handleStartDateValue(date: Date | null): void
  selectedStartDate: Date
  handleEndDateValue(date: Date | null): void
  selectedEndDate: Date
  onExportClick: () => void
  listItems: TPriceHistory[]
  currentLanguage: string
}
