import { Buttons, NewDatepicker, Dropdown, Loading, Header, Table } from '@mz-codes/design-system'
import { Page, PageContent, DataNotFound } from 'components'

import { generateUniqueId } from 'utils'
import { i18n } from 'translate'
import { TTickerHistoryTemplate } from './ticker-history.types'

import { translations } from './ticker-history.translations'

export function TickerHistoryTemplate(props: TTickerHistoryTemplate) {
  const {
    handleTickerValue,
    currentTicker,
    tickerOptions = [],
    handleStartDateValue,
    selectedStartDate,
    selectedEndDate,
    handleEndDateValue,
    onExportClick,
    isLoading,
    listItems,
  } = props
  return (
    <Page>
      <Header>
        <Header.Content>
          <Header.Item>
            <Header.Label>{translations.tickerLabel}</Header.Label>
            <Dropdown options={tickerOptions} selected={currentTicker} onChange={handleTickerValue} />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.startDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedStartDate}
              onChange={handleStartDateValue}
              maxDate={selectedEndDate}
              hint={i18n.t('startDate')}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.endDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedEndDate}
              onChange={handleEndDateValue}
              minDate={selectedStartDate}
              hint={i18n.t('endDate')}
            />
          </Header.Item>
        </Header.Content>
        <Header.ButtonGroup style={{ justifyContent: 'center' }}>
          <Buttons.Export onClick={onExportClick}>{translations.exportButton as string}</Buttons.Export>
        </Header.ButtonGroup>
      </Header>
      <PageContent>
        {isLoading && <Loading />}
        {!isLoading && (
          <Table>
            <Table.THead>
              <Table.TR>
                <Table.TH>{translations.tableTitleDate as string}</Table.TH>
                <Table.TH>{translations.tableTitleOpeningPrice as string}</Table.TH>
                <Table.TH>{translations.tableTitleClosingPrice as string}</Table.TH>
                <Table.TH>{translations.tableTitleLowPrice as string}</Table.TH>
                <Table.TH>{translations.tableTitleHighPrice as string}</Table.TH>
                <Table.TH>{translations.tableTitleVolume as string}</Table.TH>
              </Table.TR>
            </Table.THead>
            <Table.TBody>
              {listItems.length === 0 && <DataNotFound>{translations.dataNotFound}</DataNotFound>}
              {listItems.length !== 0 &&
                listItems.map((tickerPrice) => {
                  const { date, high, low, open, close, volume } = tickerPrice
                  const key = generateUniqueId()
                  return (
                    <Table.TR key={key}>
                      <Table.TD>{date}</Table.TD>
                      <Table.TD>{open}</Table.TD>
                      <Table.TD>{close}</Table.TD>
                      <Table.TD>{low}</Table.TD>
                      <Table.TD>{high}</Table.TD>
                      <Table.TD>{volume}</Table.TD>
                    </Table.TR>
                  )
                })}
            </Table.TBody>
          </Table>
        )}
      </PageContent>
    </Page>
  )
}
