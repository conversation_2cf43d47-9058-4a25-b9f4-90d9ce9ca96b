import { useLog<PERSON> } from '@logto/react'
import { coreAPI } from 'services/core/fetcher'
import { Loading } from '@mz-codes/design-system'
import { useEffect } from 'react'
import storeInformation from 'utils/storeInformation'
import { Container } from './logout.styled'

export function Logout() {
  const { signOut } = useLogto()

  async function handleSignOut() {
    try {
      await coreAPI.logout()
    } finally {
      storeInformation.clear()
      await signOut(window.location.origin)
    }
  }

  useEffect(() => {
    handleSignOut()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <Container>
      <Loading />
    </Container>
  )
}
