import { MZ_IRM_NEW, api } from 'globals/api'
import { getGroupPositionListParams, GroupPositionList } from './types'

export const getGroupPositionList = async (props: getGroupPositionListParams): Promise<GroupPositionList[]> => {
  const { companyId, tickerId, shareholderGroupId, referenceDateStart, referenceDateEnd } = props

  if (!companyId || !tickerId || !shareholderGroupId) {
    return []
  }

  const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/ticker/${tickerId}/group/${shareholderGroupId}/position`
  const params = { referenceDateStart, referenceDateEnd }

  try {
    const res = await api.get(uri, { params })
    return res.data
  } catch (error: unknown) {
    throw new Error(`Error: ${error}`)
  }
}
