import { MZ_IRM_NEW, api } from 'globals/api'
import { getPeriodOverviewParams, PeriodOverviewSummary } from './types'

export const getPeriodOverview = async (props: getPeriodOverviewParams): Promise<PeriodOverviewSummary[]> => {
  const { companyId, tickerId, viewType, entityId, referenceDateStart, referenceDateEnd } = props
  const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/ticker/${tickerId}/${viewType}/${entityId}/position/period/?referenceDateStart=${referenceDateStart}&referenceDateEnd=${referenceDateEnd}`
  const res = await api.get(uri)
  return res.data
}
