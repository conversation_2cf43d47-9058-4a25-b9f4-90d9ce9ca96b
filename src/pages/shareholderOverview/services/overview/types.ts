export type getHistoryOverviewParams = {
  companyId: string
  tickerId: string
  viewType: string
  entityId: string
  referenceDateStart: string
  referenceDateEnd: string
}

export type HistoryOverviewSummary = {
  firstPurchase: string
  lastSale: null | string
  max: string
  maxDate: string
  min: string
  minDate: string
}

export type getPeriodOverviewParams = {
  companyId: string
  tickerId: string
  viewType: string
  entityId: string
  referenceDateStart: string
  referenceDateEnd: string
}

export type PeriodOverviewSummary = {
  lastSale: null | string
  max: string
  maxDate: string
  min: string
  minDate: string
  variation: string
  variationPercentage: string
}

export type getShareholderOverviewParams = {
  companyId: string
  tickerId: string
  viewType: string | number
  entityId: string
  referenceDateStart: string
  referenceDateEnd: string
}

export type ShareholverOverviewSummary = {
  bestCase: string
  movementDays: string
  totalPurchase: string
  totalSales: string
  worstCase: string
}

export type getSimplePositionListParams = {
  companyId: string
  tickerId: string
  shareholderId: string
  startDate: string
  endDate: string
}

export type SimplePositionList = {
  averagePrice: string
  closingPrice: string
  deltaDay: string
  highPrice: string
  lowPrice: string
  maximumMovement: string
  minimumMovement: string
  name: string
  patrimony: string
  percentage: string
  referenceDate: string
  stockAmountEdited: string
  stockAmountInitial: string
  totalStocks: string
}

export type getGroupPositionListParams = {
  companyId: string
  tickerId: string
  shareholderGroupId: string
  referenceDateStart: string
  referenceDateEnd: string
}

export type GroupPositionList = {
  averagePrice: string
  closingPrice: string
  deltaDay: string
  highPrice: string
  lowPrice: string
  maximumMovement: string
  minimumMovement: string
  name: string
  patrimony: string
  percentage: string
  referenceDate: string
  stockAmountEdited: string
  stockAmountInitial: string
  totalStocks: string
}
