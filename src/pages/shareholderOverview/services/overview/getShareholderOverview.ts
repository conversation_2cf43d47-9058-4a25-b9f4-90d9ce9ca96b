import { MZ_IRM_NEW, api } from 'globals/api'
import { getShareholderOverviewParams, ShareholverOverviewSummary } from './types'

export const getShareholderOverview = async (
  props: getShareholderOverviewParams
): Promise<ShareholverOverviewSummary> => {
  const { companyId, tickerId, viewType, entityId, referenceDateStart, referenceDateEnd } = props
  const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/ticker/${tickerId}/${viewType}/${entityId}/position/period/overview/?referenceDateStart=${referenceDateStart}&referenceDateEnd=${referenceDateEnd}`
  const res = await api.get(uri)
  return res.data
}
