import { MZ_IRM_NEW, api } from 'globals/api'
import { getHistoryOverviewParams, HistoryOverviewSummary } from './types'

export const getHistoryOverview = async (props: getHistoryOverviewParams): Promise<HistoryOverviewSummary[]> => {
  const { companyId, tickerId, viewType, entityId, referenceDateStart, referenceDateEnd } = props
  const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/ticker/${tickerId}/${viewType}/${entityId}/position/historic/?referenceDateStart=${referenceDateStart}&referenceDateEnd=${referenceDateEnd}`
  const res = await api.get(uri)
  return res.data
}
