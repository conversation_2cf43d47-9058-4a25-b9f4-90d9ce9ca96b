import { MZ_IRM_NEW, api } from 'globals/api'
import { getSimplePositionListParams, SimplePositionList } from './types'

export const getSimplePositionList = async (props: getSimplePositionListParams): Promise<SimplePositionList[]> => {
  const { companyId, tickerId, shareholderId, startDate, endDate } = props

  if (!companyId || !tickerId || !shareholderId) {
    return []
  }

  const uri = `${MZ_IRM_NEW}/tearsheet/companies/${companyId}/position-history/tickers/${tickerId}/shareholders/${shareholderId}`

  try {
    const res = await api.get(uri, {
      params: {
        startDate,
        endDate,
      },
    })
    return res.data
  } catch (error: unknown) {
    throw new Error(`Error: ${error}`)
  }
}
