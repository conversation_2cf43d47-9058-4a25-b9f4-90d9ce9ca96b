import React, { Component } from 'react'
import moment from 'moment'
import { Buttons, Loading, Dropdown, Charts, Datepicker } from '@mz-codes/design-system'

import {
  localInformation,
  inputChangedHandler,
  defineTheme,
  dateFromUTC,
  formatDateToString,
  subtractDays,
  generateUniqueId,
  hasProductPermission,
  debounce,
  formatNumber,
} from 'utils'

import { ContentViewType } from 'consts'

import { hocWrapper, getAvailableDates } from 'hooks'
// clients
import {
  getIntegrationsBindForShareholder,
  getInstitutionInfo,
  getInstitutionGroupedTearSheetAlbert,
  getTickers,
  getContactsFromGroup,
  getTasksFromGroup,
  getAlbertGroupPositionHistoryOnCompany,
  getInstitutionTopPeers,
  getInstitutionCountries,
  getInstitutionHistory,
  getInstitutionSectors,
  searchAlbertShareholders,
} from 'client'

import { getUserPortfolios } from 'client/portfolioClient'

// components
import { BarChart } from 'components'

// translations
import { i18n } from 'translate'

// assets
import { IMAGES } from 'assets'
import 'assets/styles/layout/_fundHistory.scss'
import { StoreContext } from 'contexts/store'

import { HistorySummaryListContent, HistorySummaryListItem } from '../positionHistorySummary'

import { getPeriodOverview, getShareholderOverview } from '../../services/overview'

export class BaseAlbertOverview extends Component {
  static checkProduct = (requiredProduct) => {
    return hasProductPermission(requiredProduct)
  }

  static parseDate = (dateStr) => {
    const [year, month, day] = dateStr.split('-')
    const date = new Date()
    date.setDate(day)
    date.setMonth(month - 1)
    date.setFullYear(year)

    return date
  }

  searchDebouncer = debounce((searchTerm) => {
    this.getShareholders(searchTerm)
  }, 1000)

  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      companyId: localInformation.getCompanyId(),
      startDate: moment().subtract(1, 'years'),
      endDate: moment(),
      mustRender: true,
      stocks: [],
      stockType: null,
      fundName: '',
      data: [],
      isLoading: false,
      isOpen: false,
      currentTab: 0,
      shareholder: null,
      shareholderFunds: [],
      tickers: [],
      currentTicker: null,
      chartStartDate: null,
      chartEndDate: null,
      chartCategories: [],
      chartData: [],
      tableData: [],
      fundContacts: [],
      fundTasks: [],
      fundNotes: [],
      fundSearchInputForm: {
        id: 'fundSearchInputForm',
        searchTerm: {
          id: 'searchTerm',
          inputType: 'text',
          value: '',
        },
      },
      currentTask: null,
      fundSearch: '',
      typeView: ContentViewType[0],
      name: '',
      institutionId: null,
      institutionSearchTerm: {
        id: 'institutionSearchTerm',
        searchTerm: {
          id: 'searchTerm',
          inputType: 'text',
          value: '',
        },
      },
      isInstitutionModalModalOpen: false,
      hasPortfolioAnalysis: false,
      historyCategories: null,
      historyDataPositions: null,
      historyDataChanges: null,
      historyDataValues: null,
      historyType: 1,
      topCountriesCategories: null,
      topCountriesData: null,
      topPeersData: null,
      topPeersCategories: null,
      renderedInstitutions: null,
      sectorChartType: 1,
      sectorData: null,
      isLoadingPositionHistory: false,
      datesAvailable: [],
      summaryPeriod: null,
      loadingSummaryPeriod: true,
      summaryShareholder: null,
      loadingSummaryShareholder: true,
    }
  }

  componentWillMount() {
    this.getInstitutionGroupedTearSheet()
    this.getTickers()
    this.getContactsFromGroup()
    this.getTasksFromGroup()
  }

  componentDidMount() {
    if (this.constructor.checkProduct('intelligence')) {
      this.getPublicInformation()
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.state.currentTicker !== prevState.currentTicker) {
      this.getAvailableDates()
      this.getInstitutionGroupedTearSheet()
    }

    if (
      this.state.datesAvailable !== prevState.datesAvailable ||
      this.state.chartStartDate !== prevState.chartStartDate ||
      this.state.chartEndDate !== prevState.chartEndDate
    ) {
      this.getFundHistoryOnCompany()
      this.getPeriodSummary()
      this.getShareholderSummary()
    }
  }

  getPublicInformation = () => {
    const { companyId } = this.state
    getIntegrationsBindForShareholder(this.props.match.params.id).then((res) => {
      let id = null
      if (res.data && res.data.mzInstitutionId) {
        id = res.data.mzInstitutionId
      }
      this.setState(
        {
          institutionId: id,
          hasPortfolioAnalysis: id != null,
          mustRender: true,
        },
        () => {
          if (id != null) {
            this.loadInstitutionHistory(id)
            this.loadInstitutionSectors(id)
            this.loadCompanyPeers(id)
            this.loadInstitutionCountries(id)
            getInstitutionInfo(companyId, id).then((response) => {
              this.setState({
                institutionName: response.data[0].investorName,
              })
            })
          }
        }
      )
    })
  }

  getInstitutionGroupedTearSheet = () => {
    getInstitutionGroupedTearSheetAlbert(this.state.companyId, this.props.params.id).then((res) => {
      this.setState({
        name: res.data.shareholderGroup.name,
        originalName: res.data.shareholderGroup.name,
        shareholder: res.data.shareholderGroup,
        shareholderFunds: res.data.shareholders,
      })
    })
  }

  getTickers = () => {
    getTickers(this.state.companyId).then((res) => {
      if (res.success) {
        const mappedTickers = res.data.map((ticker) => {
          return {
            label: ticker.label,
            value: ticker.tickerId,
          }
        })
        this.setState({
          tickers: mappedTickers,
          currentTicker: mappedTickers[0],
        })
      }
    })
  }

  getChartStartDate = (dates, endDate) => {
    const limitDate = subtractDays(endDate, 30)

    const chartStartDate = dates
      .filter((date) => {
        return date <= limitDate
      })
      .at(-1)

    return chartStartDate || dates[dates.length - 1]
  }

  getAvailableDates = () => {
    const { currentTicker, companyId } = this.state

    getAvailableDates({ companyId, tickerId: currentTicker.value }).then((res) => {
      const datesAvailable = res.sort((a, b) => (a > b ? 1 : -1)).map(dateFromUTC)
      const chartEndDate = datesAvailable.at(-1)
      const chartStartDate = this.getChartStartDate(datesAvailable, chartEndDate)
      this.setState({ datesAvailable, chartStartDate, chartEndDate })
    })
  }

  getContactsFromGroup = () => {
    getContactsFromGroup(this.state.companyId, this.props.params.id).then((res) => {
      this.setState({ fundContacts: res.data })
    })
  }

  getTasksFromGroup = () => {
    getTasksFromGroup(this.state.companyId, this.props.params.id).then((res) => {
      const mappedActivities = res.data.map((activity) => {
        const newActivity = { ...activity }
        newActivity.task_type = newActivity.mz_task_type_name ? newActivity.mz_task_type_name : '--'
        newActivity.task_subtype = newActivity.mz_task_subtype_name != null ? newActivity.mz_task_subtype_name : '--'
        if (newActivity.executors != null) {
          const executors = newActivity.executors.split('#| ')
          newActivity.executorsArray = executors.map((name) => {
            const names = name.split(' ')
            const initials = names.map((n) => {
              return n.substring(0, 1).toUpperCase()
            })
            return {
              name,
              initials: initials.join(''),
            }
          })
        }
        return newActivity
      })

      this.setState({ fundTasks: mappedActivities })
    })
  }

  onChangeStockType = (item) => {
    const currentTicker = {
      label: item.label,
      value: item.value,
      tickerId: item.value,
    }

    this.setState({ currentTicker, typeView: ContentViewType[0] })
  }

  onChangeStartDate = (date) => {
    const { chartStartDate } = this.state
    if (date === chartStartDate) return

    this.setState({
      chartStartDate: date,
      typeView: ContentViewType[0],
    })
  }

  onChangeEndDate = (date) => {
    const { chartEndDate } = this.state
    if (date === chartEndDate) return

    this.setState({
      chartEndDate: date,
      typeView: ContentViewType[0],
    })
  }

  onChangeTypeView = (viewType) => {
    const { typeView } = this.state
    if (typeView === viewType) return

    this.setState({ typeView: viewType })
  }

  static renderLoading = () => {
    return (
      <div className="funds-wrapper">
        <div className={defineTheme('funds-content')}>
          <div className="lds-dual-ring">
            <div />
          </div>
        </div>
      </div>
    )
  }

  getFundHistoryOnCompany = async () => {
    const { chartStartDate, chartEndDate, currentTicker, shareholder, companyId } = this.state

    this.setState({ isLoadingPositionHistory: true })

    try {
      const { positions } = await getAlbertGroupPositionHistoryOnCompany(
        companyId,
        currentTicker.value,
        this.props.params.id,
        formatDateToString(chartStartDate),
        formatDateToString(chartEndDate)
      )

      this.setState({
        chartCategories: positions.map(({ referenceDate }) => moment.utc(referenceDate).format(i18n.t('dateFormat'))),
        chartData: [
          {
            type: 'area',
            name: i18n.t('positionLegend'),
            yAxis: {
              index: 0,
              visible: false,
            },
            data: positions.map(({ stockAmountEdited, percentage, totalStocks }) => ({
              y: stockAmountEdited,
              additional: [
                {
                  title: i18n.t('globals.percentage'),
                  value: percentage,
                  formater: (value) => formatNumber(0, value, 2, '', '%'),
                },
                {
                  title: i18n.t('totalAmount'),
                  value: totalStocks,
                  formater: (value) => formatNumber(0, value, 0),
                },
              ],
            })),
            color: 'rgb(51, 160, 255)',
            colorType: 'linear-gradient',
            lineWidth: 1,
            formater: (value) => formatNumber(0, value, 0),
          },
        ],
        tableData: positions,
      })
    } catch (error) {
      this.setState({
        chartCategories: [],
        chartData: [
          {
            name: shareholder.name,
            data: [],
          },
        ],
        tableData: [],
      })
    } finally {
      this.setState({ isLoadingPositionHistory: false, mustRender: false })
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  handleTab = (tabIndex) => {
    this.setState({ currentTab: tabIndex })
  }

  loadCompanyPeers = (investorId) => {
    const currentDealogic = localInformation.getDealogicCurrentInformation()
    const userId = localInformation.getUserId()
    const { companyId } = this.state
    let companyPeers = []

    getUserPortfolios(userId, companyId).then((res) => {
      let data = []
      if (!res.success) return
      data = res.data
      companyPeers =
        data.length === 0
          ? []
          : data[0].tickers.map((item) => {
              return {
                Name: item.tickerCode,
                ListingId: item.mzTickerId,
              }
            })
      companyPeers.unshift({
        Name: currentDealogic.stock_symbol,
        ListingId: currentDealogic.dealogic_listing_id,
      })
      const peers = companyPeers.map((item) => item.ListingId).filter((i) => i !== null)
      getInstitutionTopPeers(peers, investorId).then(() => {
        const { data: dataParam } = res
        const formatedTopPeersCategories = dataParam.map((item) => item.peer)
        const value = dataParam.map((item) => Number(item.marketValueUsd))
        const formatedTopPeersData = [
          {
            name: i18n.t('pageGroupedTearSheet.marketValue'),
            data: value,
            dataLabels: {
              enabled: false,
            },
          },
        ]

        this.setState({
          topPeersCategories: formatedTopPeersCategories,
          topPeersData: formatedTopPeersData,
          peersListingIds: peers,
          mustRender: false,
        })
      })
    })
  }

  loadInstitutionCountries = (id) => {
    getInstitutionCountries(id).then((res) => {
      const { data } = res
      const formatedTopCountriesCategories = data.map((item) => item.countryName)
      const value = data.map((item) => Math.round(item.percentage * 100) / 100)
      const formatedTopCountriesData = [
        {
          name: i18n.t('percentageOfPort'),
          data: value,
          tooltip: {
            valueSuffix: ' %',
          },
          dataLabels: {
            enabled: false,
            format: '{point.y:.1f} %',
          },
        },
      ]
      this.setState({
        topCountriesCategories: formatedTopCountriesCategories,
        topCountriesData: formatedTopCountriesData,
        mustRender: false,
      })
    })
  }

  loadInstitutionHistory = (investorId) => {
    const { dealogic_listing_id: dealogicId } = localInformation.getDealogicCurrentInformation()
    getInstitutionHistory(dealogicId, investorId).then((res) => {
      const { data } = res

      if (data.length < 1) {
        this.setState({
          historyDataPositions: [],
          mustRender: false,
        })
        return
      }

      const positions = data.map((item) => Number(item.Position))
      const changes = data.map((item) => (item.Change ? Number(item.Change) : 0))
      const values = data.map((item) => (item.MarketValueUsd ? Math.round(item.MarketValueUsd) : 0))

      const formatedHistoryCategories = data.map((item) =>
        moment.utc(new Date(item.ReportDate)).format(i18n.t('dateFormat'))
      )

      const formatedHistoryDataPositions = [{ name: i18n.t('position'), data: positions }]
      const formatedHistoryDataChanges = [{ name: i18n.t('pageGroupedTearSheet.sharesChange'), data: changes }]
      const formatedHistoryDataValues = [{ name: i18n.t('value'), data: values }]

      this.setState({
        historyCategories: formatedHistoryCategories,
        historyDataPositions: formatedHistoryDataPositions,
        historyDataChanges: formatedHistoryDataChanges,
        historyDataValues: formatedHistoryDataValues,
        mustRender: false,
      })
    })
  }

  loadInstitutionSectors = (id) => {
    const { companyId } = this.state

    getInstitutionSectors(companyId, id).then((res) => {
      const { data } = res
      const formatedSectorCategories = data.map((item) => item.sector)
      const total = data.map((item) => (Math.round(item.marketValueUsd * 100) / 100) * 1000)
      const percentage = data.map((item) => Math.round(item.percentage * 100) / 100)
      const formatedSectorData = [
        {
          name: i18n.t('pageGroupedTearSheet.valueMM'),
          data: total,
        },
      ]
      const formatedSectorDataPercentage = [
        {
          name: i18n.t('percentageOfPort'),
          data: percentage,
          tooltip: {
            valueSuffix: ' %',
          },
          dataLabels: {
            enabled: false,
            format: '{point.y:.1f} %',
          },
        },
      ]
      this.setState({
        sectorCategories: formatedSectorCategories,
        sectorData: formatedSectorData,
        sectorDataPercentage: formatedSectorDataPercentage,
        mustRender: false,
      })
    })
  }

  onChangeHistoryChart = (e) => {
    const targetValue = Number(e)
    this.setState({ historyType: targetValue }, () => this.renderContactHistory())
  }

  onChangeSectorChart = (e) => {
    const targetValue = Number(e)
    this.setState({ sectorChartType: targetValue }, () => this.renderSector())
  }

  renderContactHistory = () => {
    const { historyCategories, historyDataChanges, historyDataPositions, historyDataValues, historyType } = this.state

    switch (historyType) {
      case 1:
        if (!historyDataPositions || historyDataPositions.length < 1) return null
        break
      case 2:
        if (!historyDataChanges || historyDataChanges.length < 1) return null
        break
      case 3:
        if (!historyDataValues || historyDataValues.length < 1) return null
        break
      default:
        break
    }

    if (
      (historyDataPositions && historyDataPositions[0].data.length > 0) ||
      (historyDataChanges && historyDataChanges[0].data.length > 0) ||
      (historyDataValues && historyDataValues[0].data.length > 0)
    ) {
      return (
        <div className="ContactNew__ContentItem">
          <div className="ContactNew__ContentItem buttons">
            <button
              className={`btn ${historyType === 1 ? 'btn__primary' : 'btn__secondary'}`}
              type="button"
              onClick={() => this.onChangeHistoryChart(1)}
            >
              {i18n.t('position')}
            </button>
            <button
              className={`btn ${historyType === 2 ? 'btn__primary' : 'btn__secondary'}`}
              type="button"
              onClick={() => this.onChangeHistoryChart(2)}
            >
              {i18n.t('variation')}
            </button>
            <button
              className={`btn ${historyType === 3 ? 'btn__primary' : 'btn__secondary'}`}
              type="button"
              onClick={() => this.onChangeHistoryChart(3)}
            >
              {i18n.t('value')}
            </button>
          </div>
          <div className="sector-content">
            {historyType === 1 ? (
              <BarChart
                categories={historyCategories}
                series={historyDataPositions}
                title={i18n.t('position')}
                textTitle={i18n.t('shares')}
                enableLegend={false}
                enableDataLabels={false}
                valueSuffix={i18n.t('shares')}
              />
            ) : null}
            {historyType === 2 ? (
              <BarChart
                categories={historyCategories}
                series={historyDataChanges}
                title={i18n.t('position')}
                textTitle={i18n.t('pageGroupedTearSheet.sharesChange')}
                enableLegend={false}
                enableDataLabels={false}
              />
            ) : null}
            {historyType === 3 ? (
              <BarChart
                categories={historyCategories}
                series={historyDataValues}
                title={i18n.t('position')}
                textTitle={i18n.t('value')}
                enableLegend={false}
                enableDataLabels={false}
                valueSuffix={i18n.t('value')}
                barColor="#4da178"
              />
            ) : null}
          </div>
        </div>
      )
    }
    return (
      <div className="ContactNew__ContentItem">
        <p className="content-text">{i18n.t('pageGroupedTearSheet.noPositoinHistoryFound')}</p>
      </div>
    )
  }

  renderSector = () => {
    const { expandSectors, sectorCategories, sectorChartType, sectorData, sectorDataPercentage } = this.state
    if (sectorChartType === 1) {
      if (!sectorData || sectorData.length < 1) return null
      if (!sectorDataPercentage || sectorDataPercentage.length < 1) return null
    }

    if (sectorData && sectorData[0].data.length > 0) {
      return (
        <div className="ContactNew__ContentItem">
          <div className="ContactNew__ContentItem buttons">
            <Buttons.Primary onClick={() => this.onChangeSectorChart(1)}>{i18n.t('value')}</Buttons.Primary>
            <Buttons.Primary onClick={() => this.onChangeSectorChart(2)}>{i18n.t('percentageOfPort')}</Buttons.Primary>
          </div>
          <div className={expandSectors ? 'sector-content show-all-list' : 'sector-content'}>
            {sectorChartType === 1 ? (
              <BarChart
                categories={sectorCategories}
                series={sectorData}
                title={i18n.t('pageGroupedTearSheet.sectorAnalysis')}
                textTitle={i18n.t('value')}
                enableLegend={false}
                enableDataLabels={false}
                valueSuffix={i18n.t('pageGroupedTearSheet.valueMM')}
              />
            ) : null}
            {sectorChartType === 2 ? (
              <BarChart
                categories={sectorCategories}
                series={sectorDataPercentage}
                title={i18n.t('pageGroupedTearSheet.sectorAnalysis')}
                textTitle={i18n.t('percentageOfPort')}
                enableLegend={false}
                barColor="#4da178"
                valueSufix="%"
              />
            ) : null}
          </div>
        </div>
      )
    }
    return (
      <div className="ContactNew__ContentItem">
        <p className="content-text">{i18n.t('pageGroupedTearSheet.noSectorFound')}</p>
      </div>
    )
  }

  searchInputChangedHandler = (event, inputId, formKey) => {
    const currentState = this.state[formKey]
    const updatedForm = inputChangedHandler(event.target.value, inputId, currentState)

    this.setState(
      {
        [formKey]: updatedForm,
      },
      () => {
        event.persist()
        this.searchDebouncer()
      }
    )
  }

  getShareholders = () => {
    const { fundSearchInputForm, companyId } = this.state

    searchAlbertShareholders(companyId, this.props.params.id, fundSearchInputForm.searchTerm.value).then((res) => {
      this.setState({ shareholderFunds: res.data.shareholders })
    })
  }

  getPeriodSummary = async () => {
    const { companyId, currentTicker, chartStartDate, chartEndDate } = this.state

    this.setState({ loadingSummaryPeriod: true })

    let options = [
      {
        minimum: 0,
        maximum: 0,
        maximumDate: 0,
        minimumDate: 0,
        variation: 0,
        variationPct: 0,
        averagePrice: 0,
        lastSale: 0,
      },
    ]

    try {
      const response = await getPeriodOverview({
        companyId,
        tickerId: currentTicker.value,
        viewType: 'albert',
        entityId: this.props.params.id,
        referenceDateStart: formatDateToString(chartStartDate),
        referenceDateEnd: formatDateToString(chartEndDate),
      })

      if (response) {
        options = response.data.period.map((item) => ({
          minimum: item.min,
          maximum: item.max,
          maximumDate: item.maxDate,
          minimumDate: item.minDate,
          variation: item.variation,
          variationPct: item.variationPercentage,
          averagePrice: item.averagePrice,
          lastSale: item.lastSale,
        }))
      }

      this.setState({ summaryPeriod: options[0], loadingSummaryPeriod: false })
    } catch (error) {
      this.setState({ loadingSummaryPeriod: false })
    }
  }

  getShareholderSummary = () => {
    const { companyId, currentTicker, chartStartDate, chartEndDate } = this.state

    this.setState({ loadingSummaryShareholder: true })
    getShareholderOverview({
      companyId,
      tickerId: currentTicker.value,
      viewType: 'albert',
      entityId: this.props.params.id,
      referenceDateStart: formatDateToString(chartStartDate),
      referenceDateEnd: formatDateToString(chartEndDate),
    }).then((res) => {
      const responseData = res.data.period
      const options = {
        movementDays: `${responseData.movementDays || 0} ${i18n.t('days')}`,
        bestCase: `R$ ${this.formatted(responseData.bestCase || 0)}`,
        worstCase: `R$ ${this.formatted(responseData.worstCase || 0)}`,
        totalPurchases: this.formatted(responseData.totalPurchase || 0),
        totalSales: this.formatted(responseData.totalSales || 0),
        bestCaseProfitLoss: (responseData.bestCase || 0) >= 0 ? i18n.t('tearsheetProfit') : i18n.t('tearsheetLoss'),
        bestCaseClass: (responseData.bestCase || 0) >= 0 ? 'profit' : 'loss',
        worstCaseProfitLoss: (responseData.worstCase || 0) >= 0 ? i18n.t('tearsheetProfit') : i18n.t('tearsheetLoss'),
        worstCaseClass: (responseData.worstCase || 0) >= 0 ? 'profit' : 'loss',
      }
      this.setState({
        summaryShareholder: options,
        loadingSummaryShareholder: false,
      })
    })
  }

  formatRepresentativeness = (value) => {
    const { idiom } = this.state
    return parseFloat(value).toLocaleString(parseInt(idiom, 10) === 1 ? 'pt-BR' : 'en-US', {
      minimumIntegerDigits: 2,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  render() {
    if (this.state.isLoading || !this.state.shareholder || !this.state.currentTicker) {
      return this.constructor.renderLoading()
    }

    const {
      name,
      tickers,
      currentTicker,
      chartStartDate,
      chartEndDate,
      fundSearchInputForm,
      currentTab,
      typeView,
      isLoadingPositionHistory,
      datesAvailable,
      summaryPeriod,
      loadingSummaryPeriod,
      summaryShareholder,
      loadingSummaryShareholder,
    } = this.state

    return (
      <div className="funds-wrapper tearsheet-container">
        <div id="divToPrint" className={defineTheme('funds-content private-tearsheet')}>
          <div className="basic-header-content dark private-tearsheet-header group-header">
            <h2 className="title from-albert">{name}</h2>
            <ul className="more-info" style={{ fontSize: '14px' }}>
              <li>
                <span className="mini-title">{i18n.t('details')}:</span>
                <span className="value">{i18n.t('group')}</span>
              </li>
              {currentTab === 0 && (
                <>
                  <li>
                    <span className="mini-title">{i18n.t('stockType')}</span>
                    <Dropdown
                      options={tickers}
                      onChange={this.onChangeStockType}
                      selected={currentTicker || tickers[0]}
                    />
                  </li>
                  <Datepicker
                    language={i18n.language}
                    label={i18n.t('pageGroupedTearSheet.startDate')}
                    selected={chartStartDate}
                    onChange={(date) => this.onChangeStartDate(date)}
                    $availableDates={datesAvailable?.filter((date) => (chartEndDate ? date <= chartEndDate : true))}
                  />

                  <Datepicker
                    language={i18n.language}
                    label={i18n.t('pageGroupedTearSheet.endDate')}
                    selected={chartEndDate}
                    onChange={(date) => this.onChangeEndDate(date)}
                    $availableDates={datesAvailable?.filter((date) => (chartStartDate ? date >= chartStartDate : true))}
                  />
                </>
              )}
            </ul>
          </div>
          <div className="tearsheet-container__HeaderTabs">
            <ul className="list-tabs">
              <li
                className={currentTab === 0 ? 'tearsheet-container__tab active' : 'tearsheet-container__tab'}
                onClick={() => this.handleTab(0)}
                onKeyDown={() => this.handleTab(0)}
                role="menuitem"
              >
                {i18n.t('positionHistory')}
              </li>
              <li
                className={currentTab === 1 ? 'tearsheet-container__tab active' : 'tearsheet-container__tab'}
                onClick={() => this.handleTab(1)}
                onKeyDown={() => this.handleTab(1)}
                role="menuitem"
              >
                {i18n.t('vinculatedFunds')}
              </li>
            </ul>
          </div>
          {currentTab === 0 ? (
            <div className="tearsheet-container__content private">
              <div className="tearsheet-container__ContentItem private">
                <div id="shareholderOverviewSummary" className="basic-filter-content dark shareholderOverview">
                  <div className="filters">
                    <div className="content">
                      {(loadingSummaryPeriod && <Loading />) || (
                        <HistorySummaryListContent title={i18n.t('tearsheetPeriod')}>
                          <HistorySummaryListItem
                            legend={i18n.t('tearsheetMinimum')}
                            icon={IMAGES.MINIMUM_ICON}
                            value={summaryPeriod.minimum === null ? '--' : this.formatted(summaryPeriod.minimum)}
                            date={
                              summaryPeriod.minimumDate === null
                                ? '--'
                                : moment.utc(summaryPeriod.minimumDate).format(i18n.t('dateFormat'))
                            }
                          />
                          <HistorySummaryListItem
                            legend={i18n.t('tearsheetMaximum')}
                            icon={IMAGES.MAXIMUM_ICON}
                            value={summaryPeriod.maximum === null ? '--' : this.formatted(summaryPeriod.maximum)}
                            date={
                              summaryPeriod.maximumDate === null
                                ? '--'
                                : moment.utc(summaryPeriod.maximumDate).format(i18n.t('dateFormat'))
                            }
                          />
                          <HistorySummaryListItem
                            className={
                              Number(summaryPeriod.variation) > 0
                                ? 'green'
                                : Number(summaryPeriod.variation) < 0
                                  ? 'red'
                                  : ''
                            }
                            legend={i18n.t('tearsheetVariation')}
                            icon={
                              Number(summaryPeriod.variation) > 0
                                ? IMAGES.VARIATIONUP_ICON
                                : Number(summaryPeriod.variation) < 0
                                  ? IMAGES.VARIATIONDOWN_ICON
                                  : IMAGES.VARIATIONNONE_ICON
                            }
                            value={summaryPeriod.variation === null ? '--' : this.formatted(summaryPeriod.variation)}
                            variation={
                              summaryPeriod.variationPct === null
                                ? '--'
                                : `(${this.formatted(summaryPeriod.variationPct)}%)`
                            }
                          />
                          <HistorySummaryListItem
                            legend={i18n.t('tearsheetLastSale')}
                            icon={IMAGES.SALE_ICON}
                            value={
                              summaryPeriod.lastSale === null
                                ? '--'
                                : moment.utc(summaryPeriod.lastSale).format(i18n.t('dateFormat'))
                            }
                          />
                        </HistorySummaryListContent>
                      )}

                      {(loadingSummaryShareholder && <Loading />) || (
                        <HistorySummaryListContent title={i18n.t('tearsheetShareholderOverview')}>
                          <HistorySummaryListItem
                            legend={i18n.t('tearsheetPositionMovement')}
                            icon={IMAGES.CALENDAR_ICON}
                            value={summaryShareholder.movementDays}
                          />
                          <HistorySummaryListItem
                            className={summaryShareholder.bestCaseClass}
                            legend={i18n.t('tearsheetBestCase')}
                            profitLoss={summaryShareholder.bestCaseProfitLoss}
                            icon={IMAGES.CASEUP_ICON}
                            value={summaryShareholder.bestCase}
                          />
                          <HistorySummaryListItem
                            className={summaryShareholder.worstCaseClass}
                            legend={i18n.t('tearsheetWorstCase')}
                            profitLoss={summaryShareholder.worstCaseProfitLoss}
                            icon={IMAGES.CASEDOWN_ICON}
                            value={summaryShareholder.worstCase}
                          />
                          <HistorySummaryListItem
                            legend={i18n.t('tearsheetTotalPurchase')}
                            icon={IMAGES.PURCHASE_ICON}
                            value={summaryShareholder.totalPurchases}
                          />
                          <HistorySummaryListItem
                            legend={i18n.t('tearsheetTotalSales')}
                            icon={IMAGES.SALE_ICON}
                            value={summaryShareholder.totalSales}
                          />
                        </HistorySummaryListContent>
                      )}

                      <div className="filter-by refresh">
                        {this.state.chartData && this.state.chartData.length > 0 ? (
                          this.state.chartData[0].data.length > 0 ? (
                            <Dropdown onChange={this.onChangeTypeView} selected={typeView} options={ContentViewType} />
                          ) : null
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="content-graph">
                  {isLoadingPositionHistory ? (
                    <Loading />
                  ) : this.state.mustRender ? (
                    <p className="content-text">{i18n.t('pageGroupedTearSheet.chartPlaceholder')}</p>
                  ) : (
                    <div>
                      {this.state.typeView.value === '1' ? (
                        this.state.chartData.length > 0 ? (
                          this.state.chartData[0].data.length > 0 ? (
                            <div>
                              <Charts.Line
                                title={i18n.t('pageGroupedTearSheet.position')}
                                series={this.state.chartData}
                                categories={this.state.chartCategories}
                              />
                            </div>
                          ) : (
                            <p className="content-text">{i18n.t('pageGroupedTearSheet.noPositoinHistoryFound')}</p>
                          )
                        ) : (
                          <p className="content-text">{i18n.t('pageGroupedTearSheet.chartPlaceholder')}</p>
                        )
                      ) : this.state.chartData.length > 0 ? (
                        this.state.chartData[0].data.length > 0 ? (
                          <div className="tearsheet-container__listContent">
                            <div className="tearsheet-container__listContent-header">
                              <ul className="list">
                                <li className="list__item list__item-header">
                                  <span className="date title">{i18n.t('date')}</span>
                                  <span className="name title">{i18n.t('value')}</span>
                                  <span className="name title">{i18n.t('globals.percentage')}</span>
                                </li>
                              </ul>
                            </div>
                            <ul className="list list-group scroll full-width">
                              {this.state.tableData.length > 0 ? (
                                this.state.tableData.map((item) => {
                                  const key = generateUniqueId()
                                  return (
                                    <li key={key} className="list__item">
                                      <span className="date">
                                        {moment.utc(item.referenceDate).format(i18n.t('dateFormat'))}
                                      </span>
                                      <span className="name">{this.formatted(item.stockAmountEdited)}</span>
                                      <span className="name">
                                        {parseFloat(item.percentage) === 0
                                          ? '-'
                                          : `${this.formatRepresentativeness(item.percentage)}%`}
                                      </span>
                                    </li>
                                  )
                                })
                              ) : (
                                <li className="list__item">
                                  <span className="notFound">{i18n.t('globals.noInformationFound')}</span>
                                </li>
                              )}
                            </ul>
                          </div>
                        ) : (
                          <p className="content-text">{i18n.t('pageGroupedTearSheet.noPositoinHistoryFound')}</p>
                        )
                      ) : (
                        <p className="content-text">{i18n.t('pageGroupedTearSheet.chartPlaceholder')}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : null}
          {currentTab === 1 ? (
            <div>
              <div className="tearsheet-container__content list irm-tearSheet">
                <div className="tearsheet-container__ContentItem">
                  <div className="tearsheet-container__FlexTitle">
                    <h4 className="content-title">{i18n.t('vinculatedFunds')}</h4>
                    <div className="action__group">
                      <div className="tearsheet-container__search">
                        <input
                          className="search_fund"
                          autoComplete="off"
                          id={fundSearchInputForm.searchTerm.id}
                          name={fundSearchInputForm.searchTerm.id}
                          placeholder={i18n.t('fundName')}
                          onChange={(e) =>
                            this.searchInputChangedHandler(e, fundSearchInputForm.searchTerm.id, fundSearchInputForm.id)
                          }
                          type={fundSearchInputForm.searchTerm.inputType}
                          value={fundSearchInputForm.searchTerm.value}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="tearsheet-container__listContent">
                    <div className="tearsheet-container__listContent-header">
                      <ul className="list">
                        <li className="list__item list__item-header">
                          <span className="name title">{i18n.t('name')}</span>
                        </li>
                      </ul>
                    </div>
                    <ul className="list list-group scroll full-width">
                      {this.state.shareholderFunds.length === 0 ? (
                        <li className="list__item">
                          <span className="name">{i18n.t('noFund')}</span>
                        </li>
                      ) : (
                        this.state.shareholderFunds.map((item) => {
                          const key = generateUniqueId()
                          return (
                            <li className="list__item" key={key}>
                              <span className="name">{item.displayName}</span>
                            </li>
                          )
                        })
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    )
  }
}

BaseAlbertOverview.contextType = StoreContext

const AlbertOverview = hocWrapper(BaseAlbertOverview)

export { AlbertOverview }
