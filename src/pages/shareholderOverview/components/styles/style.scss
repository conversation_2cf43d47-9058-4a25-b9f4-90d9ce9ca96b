@use '../../../../assets/styles/utils/variables';

.tearsheet-container__content {
  .tearsheet-container {
    &__listContent {
      &--tearsheetOverview {
        .list__item-header {
          display: grid;
          grid-template-columns: 86px 1fr 1fr 1fr 0.8fr 145px 0.9fr 0.85fr 0.8fr 0.8fr 125px 125px;
          @media (max-width: 1440px) {
            grid-template-columns: 86px 1.2fr 0.8fr 1fr 1fr 109px 1.1fr 1.2fr 0.8fr 0.8fr 125px 125px;
            span.title {
              font-size: 13px;
            }
          }
        }
        .list-group {
          .list__item {
            display: grid;
            grid-template-columns: 86px 1fr 1fr 1fr 0.8fr 145px 0.9fr 0.85fr 0.8fr 0.8fr 125px 125px;
            @media (max-width: 1440px) {
              grid-template-columns: 86px 1.2fr 0.8fr 1fr 1fr 109px 1.1fr 1.2fr 0.8fr 0.8fr 125px 125px;
              span {
                font-size: 13px;
              }
            }
          }
        }
        .disclaimer {
          font-size: 12px;
          color: variables.$main-light-blue;
          margin: 15px;
          line-height: 1.1em;
        }
      }
    }
  }
}
