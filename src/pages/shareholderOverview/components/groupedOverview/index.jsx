import React, { Component } from 'react'
import { Link, Navigate } from 'react-router-dom'
import moment from 'moment'
import { debounce } from 'utils/debounce'
import ReactTooltip from 'react-tooltip'
import { ExternalLink } from 'components/external-link'
import { EXTERNAL_PRODUCT } from 'hooks/use-external-redirect'

import { CompanyDetailsModal } from '@mz-codes/mz-page-package'

import {
  Buttons,
  Dropdown,
  Icons,
  Loading,
  ConfirmationModal,
  DeleteModal,
  Charts,
  Tooltip,
  ALIGNMENTS,
  Datepicker,
} from '@mz-codes/design-system'

import {
  inputChangedHandler,
  defineTheme,
  localInformation,
  utils,
  formatDateToString,
  dateFromUTC,
  subtractDays,
  hasProductPermission,
  formatNumber,
} from 'utils'

import { ContentViewType } from 'consts'

import { ref, subscribe } from 'valtio/vanilla'

import {
  getInstitutionInfo,
  getInstitutionGroupedTearSheetNew,
  getTickers,
  getContactsFromGroup,
  getTasksFromGroup,
  searchShareholderByName,
  vinculateShareholderWithGroup,
  deleteTask,
  unlinkShareholderFromGroup,
  updateShareholderGroupDisplayName,
  getInstitutionTopPeers,
  getInstitutionCountries,
  getInstitutionHistory,
  getInstitutionSectors,
  deleteShareholderGroup,
  getIntegrationsBindForShareholder,
  removeIntegrationsBind,
  exportShareholderGroup,
} from 'client'

import { getUserPortfolios } from 'client/portfolioClient'
import * as downloadClient from 'client/downloadClient'

import { i18n } from 'translate'

import { IMAGES } from 'assets'
import userImg from 'assets/png/default-user.png'

import 'assets/styles/pages/_teerSheet.scss'
import 'assets/styles/layout/_fundHistory.scss'
import 'assets/styles/pages/_contactsNew.scss'
import '../styles/style.scss'

import { StoreContext } from 'contexts/store'
import {
  BindConfirmationModal,
  BarChart,
  DownloadFileButton,
  GoToHistoryButton,
  TearsheetModal,
  LinkButton,
  VinculateShareholderModal,
} from 'components'
import { shouldOpenBindConfirmationModal } from 'components/modals/bind-confirmation-modal/utils'
import { getAvailableDates, hocWrapper, vinculatedShareholdersExport, getExportHistoricGroupedPositions } from 'hooks'

import { PATH, REPORT_STATUS, ENTITY_SEARCH_CONTEXT, APPLICATION_CONTEXT, APPLICATIONS_ENUM } from 'consts'
import { getPeriodOverview, getGroupPositionList } from '../../services/overview'
import { HistorySummaryListContent, HistorySummaryListItem } from '../positionHistorySummary'
import { tours } from './grouped-overview.tours'

class BaseGroupedOverview extends Component {
  searchDebouncer = debounce(() => {
    this.getInstitutionGroupedTearSheet(0, 15)
  }, 1000)

  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      companyId: localInformation.getCompanyId(),
      mustRender: true,
      stocks: [],
      stockType: null,
      fundName: '',
      data: [],
      isLoading: false,
      isOpen: false,
      currentTab: 0,
      modalFunds: false,
      shareholder: null,
      showExportModal: false,
      showInfoModal: false,
      shareholderFunds: [],
      limit: 15,
      offset: 0,
      tickers: [],
      currentTicker: null,
      chartStartDate: null,
      chartEndDate: null,
      chartCategories: [],
      chartData: [],
      tableData: [],
      closePrice: [],
      fundContacts: [],
      fundTasks: [],
      fundNotes: [],
      fundSearchInputForm: {
        id: 'fundSearchInputForm',
        searchTerm: {
          id: 'searchTerm',
          inputType: 'text',
          value: '',
        },
      },
      currentTask: null,
      fundSearch: '',
      searchableFunds: [],
      typeView: ContentViewType[0],
      name: '',
      originalName: '',
      institutionId: null,
      institutionSearchTerm: {
        id: 'institutionSearchTerm',
        searchTerm: {
          id: 'searchTerm',
          inputType: 'text',
          value: '',
        },
      },
      isDeleting: false,
      isDeleteModalOpen: false,
      isInstitutionModalModalOpen: false,
      hasPortfolioAnalysis: false,
      historyCategories: null,
      historyDataPositions: null,
      historyDataChanges: null,
      historyDataValues: null,
      historyType: 1,
      topCountriesCategories: null,
      topCountriesData: null,
      topPeersData: null,
      topPeersCategories: null,
      renderedInstitutions: null,
      sectorChartType: 1,
      sectorData: null,
      isLoadingPositionHistory: true,
      datesAvailable: [],
      stockAmounts: null,
      isNameModalOpen: false,
      confirmModal: false,
      shareholderRemoveItem: null,
      confirmRemoveTaskModal: false,
      taskRemoveItem: null,
      confirmRemoveContactModal: false,
      contactRemoveItem: null,
      confirmRemoveFundModal: false,
      fundRemoveITem: null,
      summaryPeriod: null,
      loadingSummaryPeriod: true,
      summaryShareholder: null,
      loadingSummaryShareholder: true,
      loadingVinculate: false,
      integrationsBind: null,
      bindConfirmationModal: false,
      hasIRMProductActive: false,
      currentDealogicInformation: null,
      hasLegacyParam: false,
      companyDetailsModal: {
        mode: 'search',
        isOpen: false,
        company: null,
        closeInBack: false,
        entitySearchContext: null,
        applicationContext: null,
        onClose: null,
      },
    }
  }

  componentWillMount() {
    const query = new URLSearchParams(window.location.search)
    if (query.has('legacy')) this.setState({ hasLegacyParam: true })

    this.getTickers()
    this.hasIRMPermission()
    this.getDealogicInformation()
    this.getContactsFromGroup()
    this.getTasksFromGroup()
    this.getIntegrationsBindForShareholder()
    this.props.setPageTours(tours)
  }

  componentDidMount() {
    const store = this.context
    if (store.ContactModal) {
      subscribe(store.ContactModal, this.shouldReloadPage)
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.state.currentTicker !== prevState.currentTicker) {
      this.getAvailableDates(this.state.currentTicker)
      this.getInstitutionGroupedTearSheet(this.state.offset, this.state.limit)
    }

    if (
      this.state.datesAvailable !== prevState.datesAvailable ||
      this.state.chartStartDate !== prevState.chartStartDate ||
      this.state.chartEndDate !== prevState.chartEndDate
    ) {
      this.getPeriodSummary()
      this.getFundHistoryOnCompany()
      this.getInstitutionGroupedTearSheet(this.state.offset, this.state.limit)
    }

    if (this.state.integrationsBind?.firmId) {
      this.props.runTour()
    }
  }

  componentWillUnmount() {
    const store = this.context

    if (store.ActivitiesModalVisibility) {
      store.ActivitiesModalVisibility = {
        ...store.ActivitiesModalVisibility,
        shareholderGroup: ref({
          id: null,
          name: null,
        }),
      }
    }

    if (store.ContactModal) {
      subscribe(store.ContactModal, this.shouldReloadPage)
    }
  }

  getPublicInformation = () => {
    if (!this.checkProduct(APPLICATIONS_ENUM.INTELLIGENCE)) {
      return
    }

    const { companyId, integrationsBind } = this.state

    let id = null
    if (integrationsBind && integrationsBind.mzInstitutionId && utils.stringHasVal(integrationsBind.mzInstitutionId)) {
      id = integrationsBind.mzInstitutionId
    }

    this.setState(
      {
        institutionId: id,
        hasPortfolioAnalysis: id != null,
        mustRender: true,
        bindConfirmationModal: shouldOpenBindConfirmationModal(id, this.props.params.id),
      },
      () => {
        if (id != null) {
          this.loadInstitutionHistory(id)
          this.loadInstitutionSectors(id)
          this.loadCompanyPeers(id)
          this.loadInstitutionCountries(id)
          getInstitutionInfo(companyId, id).then((res) => {
            this.setState({ institutionName: res.data[0].investorName })
          })
        }
      }
    )
  }

  getInstitutionGroupedTearSheet = (offset, limit) => {
    const { currentTicker, chartStartDate, chartEndDate, shareholderFunds, fundSearchInputForm } = this.state
    if (!chartStartDate || !chartEndDate) return

    const formatStartDate = formatDateToString(chartStartDate)
    const formatEndDate = formatDateToString(chartEndDate)
    const searchTerm = fundSearchInputForm.searchTerm.value.length > 0 ? fundSearchInputForm.searchTerm.value : ''

    getInstitutionGroupedTearSheetNew(
      this.state.companyId,
      currentTicker.value,
      this.props.params.id,
      formatStartDate,
      formatEndDate,
      searchTerm,
      offset,
      limit
    )
      .then((res) => {
        if (offset === 0) {
          this.setState(
            {
              name: res.data.shareholderGroup.name,
              originalName: res.data.shareholderGroup.name,
              shareholder: res.data.shareholderGroup.name,
              shareholderFunds: res.data.shareholders,
              containsMore: res.data.containsMore,
              listLoading: false,
              loadingVinculate: false,
            },
            () => {
              if (document.querySelector('.scroll')) {
                document.querySelector('.scroll').scrollTop = 0
                document.querySelector('.scroll').addEventListener('scroll', this.handleScroll)
              }
            }
          )
        } else {
          const newFunds = shareholderFunds
          newFunds.push(...res.data.shareholders)
          this.setState(
            {
              name: res.data.shareholderGroup.name,
              originalName: res.data.shareholderGroup.name,
              shareholder: res.data.shareholderGroup.name,
              shareholderFunds: newFunds,
              containsMore: res.data.containsMore,
              listLoading: false,
              loadingVinculate: false,
            },
            () => {
              if (document.querySelector('.scroll')) {
                document.querySelector('.scroll').addEventListener('scroll', this.handleScroll)
              }
            }
          )
        }
      })
      .finally(() => {
        this.setShareholderGroupGlobalStore()
      })
  }

  shareholderFundsReload = () => {
    this.setState({ shareholderFunds: [] }, () => {
      this.getInstitutionGroupedTearSheet(this.state.offset, this.state.limit)
    })
  }

  setShareholderGroupGlobalStore = () => {
    const store = this.context
    const { name } = this.state
    const { params } = this.props

    if (store.ActivitiesModalVisibility) {
      store.ActivitiesModalVisibility = {
        ...store.ActivitiesModalVisibility,
        shareholderGroup: ref({
          id: params.id,
          name,
        }),
      }
    }
  }

  handleScroll = () => {
    const { offset, limit, containsMore } = this.state
    const scroll = document.querySelector('.scroll')

    const newOffsset = offset + limit

    if (scroll.scrollTop + scroll.offsetHeight < scroll.scrollHeight || !containsMore) return
    this.setState({ offset: newOffsset, listLoading: true }, () => {
      scroll.removeEventListener('scroll', this.handleScroll)
      this.getInstitutionGroupedTearSheet(this.state.offset, limit)
    })
  }

  getTickers = () => {
    getTickers(this.state.companyId).then((res) => {
      if (res.success) {
        const mappedTickers = res.data.map((ticker) => {
          return {
            label: ticker.label,
            value: ticker.tickerId,
          }
        })
        this.setState({
          tickers: mappedTickers,
          currentTicker: mappedTickers[0],
        })
      }
    })
  }

  getChartStartDate = (dates, endDate) => {
    const limitDate = subtractDays(endDate, 30)

    const chartStartDate = dates
      .filter((date) => {
        return date <= limitDate
      })
      .at(-1)

    return chartStartDate || dates[dates.length - 1]
  }

  getAvailableDates = () => {
    const { currentTicker, companyId } = this.state

    getAvailableDates({ companyId, tickerId: currentTicker.value }).then((res) => {
      const today = new Date()
      const datesAvailable = res.sort((a, b) => (a > b ? 1 : -1)).map(dateFromUTC)
      const chartEndDate = datesAvailable.at(-1)
      const chartStartDate = this.getChartStartDate(datesAvailable, chartEndDate)

      if (!datesAvailable.length) return this.setState({ datesAvailable, chartStartDate: today, chartEndDate: today })

      this.setState({ datesAvailable, chartStartDate, chartEndDate })
    })
  }

  getContactsFromGroup = () => {
    getContactsFromGroup(this.state.companyId, this.props.params.id).then((res) => {
      this.setState({ fundContacts: res.data })
    })
  }

  getTasksFromGroup = () => {
    getTasksFromGroup(this.state.companyId, this.props.params.id).then((res) => {
      const mappedActivities = res.data.map((activity) => {
        // eslint-disable-next-line no-param-reassign
        activity.task_type = activity.mz_task_type_name ? activity.mz_task_type_name : '--'
        // eslint-disable-next-line no-param-reassign
        activity.task_subtype = activity.mz_task_subtype_name != null ? activity.mz_task_subtype_name : '--'

        if (activity.executors != null) {
          const executors = activity.executors.split('#| ')
          // eslint-disable-next-line no-param-reassign
          activity.executorsArray = executors.map((name) => {
            const names = name.split(' ')
            const initials = names.map((n) => {
              return n.substring(0, 1).toUpperCase()
            })
            return {
              name,
              initials: initials.join(''),
            }
          })
        }

        return activity
      })

      this.setState({ fundTasks: mappedActivities })
    })
  }

  getIntegrationsBindForShareholder = async () => {
    getIntegrationsBindForShareholder(this.props.params.id).then((res) => {
      this.setState({ integrationsBind: res.data }, () => this.getPublicInformation())
    })
  }

  onChangeStockType = (item) => {
    const currentTicker = {
      label: item.label,
      value: item.value,
      tickerId: item.value,
    }

    this.setState({ currentTicker, typeView: ContentViewType[0], loadingVinculate: true }, () =>
      this.getFundHistoryOnCompany()
    )
  }

  onChangeStartDate = (date) => {
    const { chartEndDate, chartStartDate } = this.state
    if (date === chartStartDate) return

    const adjustedEndDate = date > chartEndDate ? date : chartEndDate

    this.setState({
      chartStartDate: date,
      chartEndDate: adjustedEndDate,
      typeView: ContentViewType[0],
      loadingVinculate: true,
    })
  }

  onChangeEndDate = (date) => {
    const { chartStartDate, chartEndDate } = this.state
    if (date === chartEndDate) return

    const adjustedStartDate = date < chartStartDate ? date : chartStartDate

    this.setState({
      chartStartDate: adjustedStartDate,
      chartEndDate: date,
      typeView: ContentViewType[0],
      loadingVinculate: true,
    })
  }
  onChangeTypeView = (viewType) => {
    const { typeView } = this.state
    if (typeView === viewType) return

    this.setState({ typeView: viewType })
  }

  renderLoading = () => {
    return (
      <div className="funds-wrapper">
        <div className={defineTheme('funds-content')}>
          <div className="lds-dual-ring">
            <div />
          </div>
        </div>
      </div>
    )
  }

  getFundHistoryOnCompany = () => {
    const { companyId, chartStartDate, chartEndDate, currentTicker, shareholder } = this.state
    const { params } = this.props

    this.setState({ isLoadingPositionHistory: true, loadingSummaryShareholder: true })

    getGroupPositionList({
      companyId,
      tickerId: currentTicker.value,
      shareholderGroupId: params.id,
      referenceDateStart: formatDateToString(chartStartDate),
      referenceDateEnd: formatDateToString(chartEndDate),
    })
      .then((res) => {
        const cat = res.data.positions.map((item) => moment.utc(item.referenceDate).format(i18n.t('dateFormat')))
        const positions = res.data.positions.map((item) => {
          return {
            y: parseInt(item.stockAmountEdited, 10),
            additional: [
              {
                title: i18n.t('globals.percentage'),
                value: item.percentage,
                formater: (value) => formatNumber(0, value, 2, '', '%'),
              },
              {
                title: i18n.t('totalAmount'),
                value: item.totalStocks,
                formater: (value) => formatNumber(0, value, 0),
              },
            ],
          }
        })

        const investiments = res.data.positions.map((item) => {
          return {
            y: item.stockAmountEdited * item.closingPrice,
          }
        })

        const prices = res.data.closingPrice.map((item) => {
          return {
            y: Number(item.closingPrice),
          }
        })

        const tasks = res.data.tasks.map((item) =>
          item.title !== null
            ? {
                y: parseInt(item.stockAmountEdited, 10),
                title: item.title,
              }
            : null
        )

        const chartData = [
          {
            type: 'area',
            name: i18n.t('positionLegend'),
            yAxis: {
              index: 0,
              visible: true,
              gridLine: true,
            },
            data: positions,
            color: 'rgb(63, 159, 243)',
            colorType: 'linear-gradient',
            lineWidth: 1,
            formater: (value) => formatNumber(0, value, 0),
          },
          {
            type: 'area',
            name: i18n.t('value'),
            yAxis: {
              index: 1,
              visible: false,
            },
            data: investiments,
            color: 'rgb(89, 220, 238)',
            colorType: 'linear-gradient',
            lineWidth: 1,
            formater: (value) => formatNumber(0, value, 2, 'R$ '),
          },
          {
            type: 'area',
            name: i18n.t('price'),
            yAxis: {
              index: 2,
              visible: true,
              opposite: true,
              gridLine: true,
            },
            data: prices,
            color: 'rgb(242, 199, 89)',
            colorType: 'linear-gradient',
            lineWidth: 1,
            formater: (value) => formatNumber(0, value, 2, 'R$ '),
          },
          {
            type: 'line',
            name: i18n.t('activities'),
            yAxis: {
              index: 0,
              visible: false,
            },
            data: tasks,
            color: 'rgb(102, 186, 145)',
            colorType: 'solid',
            lineWidth: 0,
            marker: {
              enabled: true,
              fillColor: 'rgb(102, 186, 145)',
              radius: 6,
            },
          },
        ]

        const stockAmounts =
          res.data.stockAmounts != null || res.data.stockAmounts.length > 0
            ? res.data.stockAmounts.map((item) => item.totalStocks)[0]
            : null

        const tableData = res.data.positions.reverse()

        const options = {
          movementDays: `${res.data.bestAndWorstCaseSummary.movementDays || 0} ${i18n.t('days')}`,
          bestCase: `R$ ${utils.formatLocale(res.data.bestAndWorstCaseSummary.bestCaseResume || 0)}`,
          worstCase: `R$ ${utils.formatLocale(res.data.bestAndWorstCaseSummary.worstCaseResume || 0)}`,
          totalPurchases: `R$ ${utils.formatLocale(res.data.bestAndWorstCaseSummary.totalOfPurchases || 0)}`,
          totalSales: `R$ ${utils.formatLocale(res.data.bestAndWorstCaseSummary.totalOfSales || 0)}`,
          bestCaseProfitLoss:
            utils.formatLocale(res.data.bestAndWorstCaseSummary.bestCaseResume || 0) >= 0
              ? i18n.t('tearsheetProfit')
              : i18n.t('tearsheetLoss'),
          bestCaseClass:
            utils.formatLocale(res.data.bestAndWorstCaseSummary.bestCaseResume || 0) >= 0 ? 'profit' : 'loss',
          worstCaseProfitLoss:
            (res.data.bestAndWorstCaseSummary.worstCaseResume || 0) >= 0
              ? i18n.t('tearsheetProfit')
              : i18n.t('tearsheetLoss'),
          worstCaseClass: (res.data.bestAndWorstCaseSummary.worstCaseResume || 0) >= 0 ? 'profit' : 'loss',
        }

        this.setState({
          chartCategories: cat,
          chartData,
          tableData,
          mustRender: false,
          isLoadingPositionHistory: false,
          loadingSummaryShareholder: false,
          summaryShareholder: options,
          stockAmounts,
        })
      })
      .catch(() => {
        const chartData = [
          {
            name: shareholder,
            data: [],
          },
        ]
        this.setState({
          chartCategories: [],
          chartData,
          tableData: [],
          mustRender: false,
          isLoadingPositionHistory: false,
        })
      })
  }

  openTaskModal = () => {
    const store = this.context

    if (store.ActivitiesModalVisibility) {
      store.ActivitiesModalVisibility = {
        ...store.ActivitiesModalVisibility,
        isOpen: true,
      }
    }
  }

  openNewContactModal = () => {
    const store = this.context
    store.ContactModal.shareholderGroup = ref({
      id: this.props.params.id,
      name: this.state.name,
    })
    store.ContactModal.allowSearch = false
    store.ContactModal.isOpen = true
  }

  onOpenModalFunds = () => {
    this.setState({ modalFunds: true })
  }

  onCloseModalFunds = () => {
    this.setState({ fundSearch: '' })
    this.setState({ modalFunds: false })
  }

  vinculateFund = (item) => {
    vinculateShareholderWithGroup(this.state.companyId, this.props.params.id, item).then(() => {
      this.setState({ shareholderFunds: [] }, () => {
        this.getInstitutionGroupedTearSheet(this.state.offset, this.state.limit)
      })
    })
  }

  deleteTask = () => {
    deleteTask(this.state.companyId, this.state.taskRemoveItem).then(() => {
      this.getTasksFromGroup()
      this.closeRemoveTask()
    })
  }

  unlinkShareholderFromGroup = () => {
    unlinkShareholderFromGroup(
      this.state.companyId,
      this.props.params.id,
      this.state.fundRemoveItem.shareholderId
    ).then((_res) => {
      this.setState({ shareholderFunds: [] }, () => {
        this.getInstitutionGroupedTearSheet(this.state.offset, this.state.limit)
        this.closeRemoveFund()
      })
    })
  }

  openRemoveTask = (item) => {
    this.setState({
      confirmRemoveTaskModal: true,
      taskRemoveItem: item,
    })
  }

  closeRemoveTask = () => {
    this.setState({
      confirmRemoveTaskModal: false,
      taskRemoveItem: null,
    })
  }

  openRemoveFund = (item) => {
    this.setState({
      confirmRemoveFundModal: true,
      fundRemoveItem: item,
    })
  }

  closeRemoveFund = () => {
    this.setState({
      confirmRemoveFundModal: false,
      fundRemoveItem: null,
    })
  }

  formatted = (val, pref, suf) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  formatRepresentativeness = (value) => {
    const { idiom } = this.state
    return parseFloat(value).toLocaleString(parseInt(idiom, 10) === 1 ? 'pt-BR' : 'en-US', {
      minimumIntegerDigits: 2,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  inputChangedHandler = (e) => {
    this.setState({ name: e.target.value })
  }

  nameChangedHandler = () => {
    const { name, originalName } = this.state
    if (name !== originalName) {
      this.setState({ isNameModalOpen: true })
    }
  }

  closeNameModal = () => {
    const { originalName } = this.state
    this.setState({ isNameModalOpen: false, name: originalName })
  }

  submitNameModal = () => {
    const { name } = this.state
    updateShareholderGroupDisplayName(this.state.companyId, this.props.params.id, name)
      .then(() => {
        this.props.createToast({
          title: i18n.t('globals.toasts.changeShareholderName.success.title'),
          description: i18n.t('globals.toasts.changeShareholderName.success.message.group'),
          duration: 9000,
          type: 'success',
        })
        this.setState({ isNameModalOpen: false, originalName: name }, () =>
          this.getInstitutionGroupedTearSheet(this.state.offset, this.state.limit)
        )
      })
      .catch(() => {
        this.props.createToast({
          title: i18n.t('globals.toasts.changeShareholderName.error.title'),
          description: i18n.t('globals.toasts.changeShareholderName.error.message.group'),
          duration: 9000,
          type: 'error',
        })
      })
  }

  handleTab = (tabIndex) => {
    this.setState({ currentTab: tabIndex }, () => {
      if (tabIndex === 1) {
        document.querySelector('.scroll').addEventListener('scroll', this.handleScroll)
      }
    })
  }

  checkProduct = (requiredProduct) => {
    return hasProductPermission(requiredProduct)
  }

  removeIntegrationsBind = (targetEntity, targetEntityId) => {
    const { companyId } = this.state

    removeIntegrationsBind(companyId, targetEntity, targetEntityId).then((res) => {
      this.setState(
        {
          institutionId: null,
          hasPortfolioAnalysis: false,
          mustRender: true,
          historyDataPositions: null,
          sectorData: null,
          topPeersData: null,
          topCountriesData: null,
        },
        this.getIntegrationsBindForShareholder
      )
    })
  }

  loadCompanyPeers = (investorId) => {
    const { companyId } = this.state
    const userId = localInformation.getUserId()
    const currentDealogic = localInformation.getDealogicCurrentInformation()
    let companyPeers = []

    getUserPortfolios(userId, companyId).then((res) => {
      let data = []
      if (!res.success) return
      data = res.data
      companyPeers =
        data.length === 0
          ? []
          : data[0].tickers.map((item) => {
              return {
                Name: item.tickerCode,
                ListingId: item.mzTickerId,
              }
            })
      companyPeers.unshift({
        Name: currentDealogic.stock_symbol,
        ListingId: currentDealogic.dealogic_listing_id,
      })
      const peers = companyPeers.map((item) => item.ListingId).filter((i) => i !== null)
      getInstitutionTopPeers(peers, investorId).then((institutionTopPeers) => {
        const { response } = institutionTopPeers
        const formatedTopPeersCategories = response?.map((item) => item.peer)
        const value = response?.map((item) => Number(item.marketValueUsd))
        const formatedTopPeersData = [
          {
            name: i18n.t('pageGroupedTearSheet.marketValue'),
            data: value,
            dataLabels: {
              enabled: false,
            },
          },
        ]

        this.setState({
          topPeersCategories: formatedTopPeersCategories,
          topPeersData: formatedTopPeersData,
          peersListingIds: peers,
          mustRender: false,
        })
      })
    })
  }

  loadInstitutionCountries = (id) => {
    getInstitutionCountries(id).then((res) => {
      const { data } = res
      const formatedTopCountriesCategories = data.map((item) => item.countryName)
      const value = data.map((item) => Math.round(item.percentage * 100) / 100)
      const formatedTopCountriesData = [
        {
          name: i18n.t('pageGroupedTearSheet.percentageOfPort'),
          data: value,
          tooltip: {
            valueSuffix: ' %',
          },
          dataLabels: {
            enabled: false,
            format: '{point.y:.1f} %',
          },
        },
      ]
      this.setState({
        topCountriesCategories: formatedTopCountriesCategories,
        topCountriesData: formatedTopCountriesData,
        mustRender: false,
      })
    })
  }

  loadInstitutionHistory = (investorId) => {
    const { dealogic_listing_id: dealogicId } = localInformation.getDealogicCurrentInformation()
    getInstitutionHistory(dealogicId, investorId).then((res) => {
      const { data } = res

      if (data.length < 1) {
        this.setState({
          historyDataPositions: [],
          mustRender: false,
        })
        return
      }

      const positions = data.map((item) => Number(item.Position))
      const changes = data.map((item) => (item.Change ? Number(item.Change) : 0))
      const values = data.map((item) => (item.MarketValueUsd ? Math.round(item.MarketValueUsd) : 0))

      const formatedHistoryCategories = data.map((item) =>
        moment.utc(new Date(item.ReportDate)).format(i18n.t('dateFormat'))
      )

      const formatedHistoryDataPositions = [{ name: i18n.t('position'), data: positions }]
      const formatedHistoryDataChanges = [{ name: i18n.t('pageGroupedTearSheet.sharesChange'), data: changes }]
      const formatedHistoryDataValues = [{ name: i18n.t('value'), data: values }]

      this.setState({
        historyCategories: formatedHistoryCategories,
        historyDataPositions: formatedHistoryDataPositions,
        historyDataChanges: formatedHistoryDataChanges,
        historyDataValues: formatedHistoryDataValues,
        mustRender: false,
      })
    })
  }

  loadInstitutionSectors = (id) => {
    getInstitutionSectors(id).then((res) => {
      const { data } = res
      const formatedSectorCategories = data.map((item) => item.sector)
      const total = data.map((item) => (Math.round(item.marketValueUsd * 100) / 100) * 1000)
      const percentage = data.map((item) => Math.round(item.percentage * 100) / 100)
      const formatedSectorData = [
        {
          name: i18n.t('pageGroupedTearSheet.valueMM'),
          data: total,
        },
      ]
      const formatedSectorDataPercentage = [
        {
          name: i18n.t('percentageOfPort'),
          data: percentage,
          tooltip: {
            valueSuffix: ' %',
          },
          dataLabels: {
            enabled: false,
            format: '{point.y:.1f} %',
          },
        },
      ]
      this.setState({
        sectorCategories: formatedSectorCategories,
        sectorData: formatedSectorData,
        sectorDataPercentage: formatedSectorDataPercentage,
        mustRender: false,
      })
    })
  }

  onExportFunds = async () => {
    const { currentTicker, chartStartDate, chartEndDate, companyId } = this.state
    const { params } = this.props

    try {
      this.props.createToast({
        type: 'info',
        title: i18n.t('globals.export.sent.title'),
        description: i18n.t('globals.export.sent.message'),
      })

      const { shareholderReportId, status } = await vinculatedShareholdersExport({
        companyId,
        tickerId: currentTicker.value,
        referenceDateStart: formatDateToString(chartStartDate),
        referenceDateEnd: formatDateToString(chartEndDate),
        shareholderGroupId: params.id,
        language: i18n.language === 'pt-BR' ? 1 : 0,
      })

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: (
          <>
            {status == REPORT_STATUS.SUCCESS && <DownloadFileButton shareholderReportId={shareholderReportId} />}
            <GoToHistoryButton />
          </>
        ),
      })
    } catch (err) {
      this.props.createToast({
        type: 'error',
        title: err.title,
        description: err.message,
      })
    }
  }

  onExportPosition = async (startDate, endDate) => {
    const { currentTicker, companyId } = this.state
    const { params } = this.props
    try {
      this.props.createToast({
        type: 'info',
        title: i18n.t('globals.export.sent.title'),
        description: i18n.t('globals.export.sent.message'),
      })

      const params = {
        companyId,
        tickerId: currentTicker.value,
        shareholderGroupId: params.id,
        referenceDateStart: formatDateToString(startDate),
        referenceDateEnd: formatDateToString(endDate),
      }

      await downloadClient.downloadGroupedHistoryPosition(params)

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      console.error('error', err)
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.export.error.title'),
        description: i18n.t('globals.export.error.message'),
      })
    } finally {
      this.setState({ isLoading: false })
    }
  }

  onExportHistoricPosition = async () => {
    this.props.createToast({
      type: 'info',
      title: i18n.t('globals.export.sent.title'),
      description: i18n.t('globals.export.sent.message'),
    })
    try {
      const { currentTicker, companyId, idiom: language } = this.state
      const params = {
        companyId,
        tickerId: currentTicker.value,
        shareholderGroupId: this.props.params.id,
        language,
      }

      await getExportHistoricGroupedPositions(params)

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      console.log(err)
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.export.error.title'),
        description: i18n.t('globals.export.error.message'),
      })
    } finally {
      this.setState({ isLoading: false })
    }
  }

  onChangeHistoryChart = (e) => {
    const targetValue = Number(e)
    this.setState({ historyType: targetValue }, () => this.renderContactHistory())
  }

  onChangeSectorChart = (e) => {
    const targetValue = Number(e)
    this.setState({ sectorChartType: targetValue }, () => this.renderSector())
  }

  renderContactHistory = () => {
    const { historyCategories, historyDataChanges, historyDataPositions, historyDataValues, historyType } = this.state
    const { idiom } = this.props

    switch (historyType) {
      case 1:
        if (!historyDataPositions || historyDataPositions.length < 1) return null
        break
      case 2:
        if (!historyDataChanges || historyDataChanges.length < 1) return null
        break
      case 3:
        if (!historyDataValues || historyDataValues.length < 1) return null
        break
      default:
        break
    }

    if (
      (historyDataPositions && historyDataPositions[0].data.length > 0) ||
      (historyDataChanges && historyDataChanges[0].data.length > 0) ||
      (historyDataValues && historyDataValues[0].data.length > 0)
    ) {
      return (
        <div className="ContactNew__ContentItem">
          <div className="ContactNew__ContentItem buttons">
            <button
              className={`btn ${historyType === 1 ? 'btn__primary' : 'btn__secondary'}`}
              type="button"
              onClick={() => this.onChangeHistoryChart(1)}
            >
              {i18n.t('position')}
            </button>
            <button
              className={`btn ${historyType === 2 ? 'btn__primary' : 'btn__secondary'}`}
              type="button"
              onClick={() => this.onChangeHistoryChart(2)}
            >
              {i18n.t('variation')}
            </button>
            <button
              className={`btn ${historyType === 3 ? 'btn__primary' : 'btn__secondary'}`}
              type="button"
              onClick={() => this.onChangeHistoryChart(3)}
            >
              {i18n.t('value')}
            </button>
          </div>
          <div className="sector-content">
            {historyType === 1 ? (
              <BarChart
                categories={historyCategories}
                series={historyDataPositions}
                title={i18n.t('position')}
                textTitle={i18n.t('shares')}
                enableLegend={false}
                enableDataLabels={false}
                valueSuffix={i18n.t('shares')}
                idiom={idiom}
              />
            ) : null}
            {historyType === 2 ? (
              <BarChart
                categories={historyCategories}
                series={historyDataChanges}
                title={i18n.t('position')}
                textTitle={i18n.t('pageGroupedTearSheet.sharesChange')}
                enableLegend={false}
                enableDataLabels={false}
                idiom={idiom}
              />
            ) : null}
            {historyType === 3 ? (
              <BarChart
                categories={historyCategories}
                series={historyDataValues}
                title={i18n.t('position')}
                textTitle={i18n.t('value')}
                enableLegend={false}
                enableDataLabels={false}
                valueSuffix={i18n.t('value')}
                barColor="#4da178"
                idiom={idiom}
              />
            ) : null}
          </div>
        </div>
      )
    }
    return (
      <div className="ContactNew__ContentItem">
        <p className="content-text">{i18n.t('pageGroupedTearSheet.noPositoinHistoryFound')}</p>
      </div>
    )
  }

  renderSector = () => {
    const { expandSectors, sectorCategories, sectorChartType, sectorData, sectorDataPercentage } = this.state
    const { idiom } = this.props
    if (sectorChartType === 1) {
      if (!sectorData || sectorData.length < 1) return null
      if (!sectorDataPercentage || sectorDataPercentage.length < 1) return null
    }

    if (sectorData && sectorData[0].data.length > 0) {
      return (
        <div className="ContactNew__ContentItem">
          <div className="ContactNew__ContentItem buttons">
            <button
              className={`btn ${sectorChartType === 1 ? 'btn__primary' : 'btn__secondary'}`}
              type="button"
              onClick={() => this.onChangeSectorChart(1)}
            >
              {i18n.t('value')}
            </button>
            <button
              className={`btn ${sectorChartType === 2 ? 'btn__primary' : 'btn__secondary'}`}
              type="button"
              onClick={() => this.onChangeSectorChart(2)}
            >
              {i18n.t('percentageOfPort')}
            </button>
          </div>
          <div className={expandSectors ? 'sector-content show-all-list' : 'sector-content'}>
            {sectorChartType === 1 ? (
              <BarChart
                categories={sectorCategories}
                series={sectorData}
                title={i18n.t('pageGroupedTearSheet.sectorAnalysis')}
                textTitle={i18n.t('value')}
                enableLegend={false}
                enableDataLabels={false}
                valueSuffix={i18n.t('pageGroupedTearSheet.valueMM')}
                idiom={idiom}
              />
            ) : null}
            {sectorChartType === 2 ? (
              <BarChart
                categories={sectorCategories}
                series={sectorDataPercentage}
                title={i18n.t('pageGroupedTearSheet.sectorAnalysis')}
                textTitle={i18n.t('percentageOfPort')}
                enableLegend={false}
                barColor="#4da178"
                valueSufix="%"
                idiom={idiom}
              />
            ) : null}
          </div>
        </div>
      )
    }
    return (
      <div className="ContactNew__ContentItem">
        <p className="content-text">{i18n.t('pageGroupedTearSheet.noSectorFound')}</p>
      </div>
    )
  }

  renderTopPeers = () => {
    const { topPeersCategories, topPeersData } = this.state
    const { idiom } = this.props
    if (!topPeersData || topPeersData.length < 1) return null

    if (topPeersData && topPeersData[0].data?.length > 0) {
      return (
        <div className="ContactNew__ContentItem">
          <div className="top-five-countries-content">
            <BarChart
              categories={topPeersCategories}
              series={topPeersData}
              title={i18n.t('pageGroupedTearSheet.topPeers')}
              textTitle={i18n.t('shares')}
              enableLegend={false}
              valueSufix=""
              idiom={idiom}
            />
          </div>
        </div>
      )
    }
    return (
      <div className="ContactNew__ContentItem">
        <p className="content-text">{i18n.t('pageGroupedTearSheet.noTopPeersFound')}</p>
      </div>
    )
  }

  renderTopCountries = () => {
    const { topCountriesCategories, topCountriesData } = this.state
    const { idiom } = this.props
    if (!topCountriesData || topCountriesData.length < 1) return null

    if (topCountriesData && topCountriesData[0].data.length > 0) {
      return (
        <div className="ContactNew__ContentItem">
          <div className="top-five-countries-content">
            <BarChart
              categories={topCountriesCategories}
              series={topCountriesData}
              title={i18n.t('pageGroupedTearSheet.top5Countries')}
              textTitle={i18n.t('percentageOfPort')}
              enableLegend={false}
              valueSufix="%"
              idiom={idiom}
            />
          </div>
        </div>
      )
    }
    return (
      <div className="ContactNew__ContentItem">
        <p className="content-text">{i18n.t('pageGroupedTearSheet.noTopCountriesFound')}</p>
      </div>
    )
  }

  openDeleteModal = () => {
    this.setState({ isDeleteModalOpen: true })
  }

  closeDeleteModal = () => {
    this.setState({ isDeleteModalOpen: false })
  }

  deleteGroup = () => {
    const { params } = this.props
    const { companyId } = this.state

    deleteShareholderGroup(companyId, params.id).then(() => {
      this.setState({ isDeleting: true, isDeleteModalOpen: false })
    })
  }

  hasIRMPermission = () => {
    const activeProducts = localInformation.getCore2SelectedCustomerUserApplications()
    this.setState({
      hasIRMProductActive: activeProducts.includes('mz_irm'),
    })
  }

  getDealogicInformation = () => {
    const currentDealogicInformation = localInformation.getDealogicCurrentInformation()
    this.setState({
      currentDealogicInformation: currentDealogicInformation,
    })
  }

  exportGroupIRMPdf = async () => {
    this.props.createToast({
      title: i18n.t('shareholders.toasts.info.title'),
      description: i18n.t('shareholders.toasts.info.description'),
      type: 'info',
    })

    try {
      const {
        integrationsBind,
        currentTicker,
        chartStartDate,
        chartEndDate,
        currentDealogicInformation,
        hasIRMProductActive,
      } = this.state

      const params = {
        firmId: integrationsBind.firmId,
        startDate: chartStartDate,
        endDate: chartEndDate,
        tickerId: currentTicker.value,
        language: i18n.language,
        mzTickerId: currentDealogicInformation.dealogic_listing_id,
        myCompanyAxisType: 'position',
        interactionType: 'all',
        generalInteractionType: 'all',
        interactions: false,
        portfolioId: '',
      }

      await exportShareholderGroup(params)

      if (!hasIRMProductActive) {
        this.props.createToast({
          title: i18n.t('shareholders.toasts.success.title'),
          description: i18n.t('shareholders.toasts.success.description'),
          type: 'success',
          duration: 9000,
        })

        return
      }

      this.props.createToast({
        title: i18n.t('shareholders.toasts.success.title'),
        description: i18n.t('shareholders.toasts.success.description'),
        type: 'success',
        duration: 9000,
        buttons: (
          <Link to="/shareholders/history/exports" target="_blank">
            {i18n.t('globals.goToHistory')}
          </Link>
        ),
      })
    } catch (error) {
      this.props.createToast({
        title: i18n.t('shareholders.toasts.error.title'),
        description: i18n.t('shareholders.toasts.error.description'),
        type: 'error',
      })
    }
  }

  searchInputChangedHandler = (event, inputId, formKey) => {
    const currentState = this.state[formKey]
    const updatedForm = inputChangedHandler(event.target.value, inputId, currentState)

    this.setState(
      {
        [formKey]: updatedForm,
      },
      () => {
        event.persist()
        this.searchDebouncer()
      }
    )
  }

  cpfCnpj = (value) => {
    let newValue = value.replace(/\D/g, '')

    if (value.length <= 11) {
      // CPF
      newValue = newValue.replace(/(\d{3})(\d)/, '$1.$2')
      newValue = newValue.replace(/(\d{3})(\d)/, '$1.$2')
      newValue = newValue.replace(/(\d{3})(\d{1,2})$/, '$1-$2')
    } else {
      // CNPJ
      newValue = newValue.replace(/^(\d{2})(\d)/, '$1.$2')
      newValue = newValue.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
      newValue = newValue.replace(/\.(\d{3})(\d)/, '.$1/$2')
      newValue = newValue.replace(/(\d{4})(\d)/, '$1-$2')
    }
    return newValue
  }

  getShareholderType = (shareholderType) => {
    switch (parseInt(shareholderType, 10)) {
      case 1:
        return i18n.t('fund')
      case 2:
        return i18n.t('pageGroupedTearSheet.shareholderTypeIndividual')
      default:
        return i18n.t('unknown')
    }
  }

  getPeriodSummary = async () => {
    let options = [
      {
        minimum: 0,
        maximum: 0,
        maximumDate: 0,
        minimumDate: 0,
        variation: 0,
        variationPct: 0,
        averagePrice: 0,
        lastSale: 0,
      },
    ]
    const { companyId, currentTicker, chartStartDate, chartEndDate } = this.state
    const { params } = this.props
    this.setState({ loadingSummaryPeriod: true })

    try {
      const response = await getPeriodOverview({
        companyId,
        tickerId: currentTicker.value,
        viewType: 'group',
        entityId: params.id,
        referenceDateStart: formatDateToString(chartStartDate),
        referenceDateEnd: formatDateToString(chartEndDate),
      })

      if (response) {
        options = response.data.period.map((item) => ({
          minimum: item.min,
          maximum: item.max,
          maximumDate: item.maxDate,
          minimumDate: item.minDate,
          variation: item.variation,
          variationPct: item.variationPercentage,
          averagePrice: item.averagePrice,
          lastSale: item.lastSale,
        }))
      }

      this.setState({ summaryPeriod: options[0], loadingSummaryPeriod: false })
    } catch (error) {
      this.setState({ loadingSummaryPeriod: false })
    }
  }

  openIntegrationsModal = (entitySearchContext, shareholderGroupId) => {
    const { integrationsBind } = this.state

    this.setState({
      companyDetailsModal: {
        mode: 'search',
        isOpen: true,
        company: {
          id: shareholderGroupId,
          bind: {
            firmId: integrationsBind?.firmId || null,
            mzInstitutionId: integrationsBind?.mzInstitutionId || null,
            shareholderGroupId: integrationsBind?.shareholderGroupId || null,
          },
        },
        closeInBack: false,
        onClose: () => {
          this.getIntegrationsBindForShareholder()
        },
        entitySearchContext,
        applicationContext: APPLICATION_CONTEXT.SHAREHOLDERS,
      },
    })
  }

  shouldReloadPage = (update) => {
    const shouldReload = this.shouldUpdateContact(update)
    if (shouldReload) {
      this.getContactsFromGroup()
    }
  }

  shouldUpdateContact(update) {
    const store = this.context.ContactModal
    const isNewContactUploaded = update?.find((val) => val[1].indexOf('reload') > -1) !== undefined

    const shouldReloadContact = store.reload

    return isNewContactUploaded && shouldReloadContact
  }

  closeCompanyDetailsModal = () => {
    this.setState({
      companyDetailsModal: {
        mode: 'search',
        isOpen: false,
        company: null,
        closeInBack: false,
        entitySearchContext: null,
        applicationContext: null,
        onClose: null,
      },
    })
  }

  render() {
    const {
      name,
      originalName,
      tickers,
      currentTicker,
      chartStartDate,
      chartEndDate,
      fundContacts,
      fundTasks,
      fundSearchInputForm,
      currentTab,
      hasPortfolioAnalysis,
      historyDataPositions,
      sectorData,
      topPeersData,
      topCountriesData,
      institutionName,
      institutionId,
      mustRender,
      isDeleting,
      typeView,
      isDeleteModalOpen,
      isLoadingPositionHistory,
      datesAvailable,
      isNameModalOpen,
      confirmRemoveTaskModal,
      confirmRemoveFundModal,
      summaryPeriod,
      loadingSummaryPeriod,
      loadingVinculate,
      integrationsBind,
      hasLegacyParam,
      hasIRMProductActive,
    } = this.state

    const shareholderGroupId = this.props.params.id

    if (isDeleting) {
      return <Navigate to="/shareholders/shareholders/" replace />
    }

    if (!hasLegacyParam && hasIRMProductActive) {
      return <Navigate to={`${PATH}/group/${shareholderGroupId}`} replace />
    }

    return (
      <div className="funds-wrapper tearsheet-container">
        {(isLoadingPositionHistory && <Loading />) || (
          <>
            <div id="divToPrint" className={defineTheme('funds-content private-tearsheet')}>
              <div className="basic-header-content dark private-tearsheet-header group-header">
                <div style={{ display: 'flex', alignItems: 'center', padding: '0 20px' }}>
                  <input
                    autoComplete="false"
                    className="title"
                    onChange={(e) => this.inputChangedHandler(e)}
                    onBlur={() => this.nameChangedHandler()}
                    value={name}
                  />

                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'end',
                      gap: '8px',
                    }}
                  >
                    <Tooltip
                      text={i18n.t('pageGroupedTearSheet.delete')}
                      $width="auto"
                      $alignment={ALIGNMENTS.BOTTOM_LEFT}
                    >
                      <Icons.Trash size={20} className="trash-icon" onClick={this.openDeleteModal} />
                    </Tooltip>

                    {integrationsBind?.firmId && (
                      <Tooltip
                        text={i18n.t('pageGroupedTearSheet.export')}
                        $width="auto"
                        $alignment={ALIGNMENTS.BOTTOM_LEFT}
                      >
                        <Icons.ExportFile size={20} className="export-icon" onClick={this.exportGroupIRMPdf} />
                      </Tooltip>
                    )}
                  </div>
                </div>

                <ul className="more-info" style={{ fontSize: '14px' }}>
                  <li>
                    <span className="mini-title">{i18n.t('details')}</span>
                    <span className="value">{i18n.t('ownership.group')}</span>
                  </li>

                  {currentTab === 0 || currentTab === 1 ? (
                    <>
                      <li>
                        <span className="mini-title">{i18n.t('stockType')}</span>
                        <Dropdown
                          options={tickers}
                          onChange={this.onChangeStockType}
                          selected={currentTicker || tickers[0]}
                        />
                      </li>
                      <Datepicker
                        language={i18n.language}
                        label={i18n.t('pageGroupedTearSheet.startDate')}
                        selected={chartStartDate}
                        onChange={(date) => this.onChangeStartDate(date)}
                        $availableDates={datesAvailable}
                      />

                      <Datepicker
                        language={i18n.language}
                        label={i18n.t('pageGroupedTearSheet.endDate')}
                        selected={chartEndDate}
                        onChange={(date) => this.onChangeEndDate(date)}
                        $availableDates={datesAvailable}
                      />
                    </>
                  ) : null}
                  {hasIRMProductActive && (
                    <div style={{ marginLeft: 'auto' }} id={'grouped-overview-go-to-new-version'}>
                      <LinkButton link={`${PATH}/group/${shareholderGroupId}`}>
                        {i18n.t('pageGroupedTearSheet.tours.goToNewVersion.button')}
                      </LinkButton>
                    </div>
                  )}
                </ul>
              </div>
              <div className="tearsheet-container__HeaderTabs">
                <ul className="list-tabs">
                  <li className={currentTab === 0 ? 'tearsheet-container__tab active' : 'tearsheet-container__tab'}>
                    <div
                      role="button"
                      onClick={() => this.handleTab(0)}
                      onKeyDown={() => this.handleTab(0)}
                      tabIndex={0}
                      aria-label="Handle Tab"
                    >
                      {i18n.t('positionHistory')}
                    </div>
                  </li>
                  {this.checkProduct(APPLICATIONS_ENUM.INTELLIGENCE) ? (
                    <li className={currentTab === 4 ? 'tearsheet-container__tab active' : 'tearsheet-container__tab'}>
                      <div
                        role="button"
                        onClick={() => this.handleTab(4)}
                        onKeyDown={() => this.handleTab(4)}
                        tabIndex={0}
                        aria-label="Handle Tab"
                      >
                        {i18n.t('pageGroupedTearSheet.publicInformation')}
                      </div>
                    </li>
                  ) : null}
                  <li className={currentTab === 1 ? 'tearsheet-container__tab active' : 'tearsheet-container__tab'}>
                    <div
                      role="button"
                      onClick={() => this.handleTab(1)}
                      onKeyDown={() => this.handleTab(1)}
                      tabIndex={0}
                      aria-label="Handle Tab"
                    >
                      {i18n.t('pageGroupedTearSheet.vinculatedShareholders')}
                    </div>
                  </li>
                  {this.checkProduct(APPLICATIONS_ENUM.IRM) ? (
                    <li className={currentTab === 2 ? 'tearsheet-container__tab active' : 'tearsheet-container__tab'}>
                      <div
                        role="button"
                        onClick={() => this.handleTab(2)}
                        onKeyDown={() => this.handleTab(2)}
                        tabIndex={0}
                        aria-label="Handle Tab"
                      >
                        {i18n.t('pageGroupedTearSheet.tasks')}
                      </div>
                    </li>
                  ) : null}
                  {this.checkProduct(APPLICATIONS_ENUM.IRM) ? (
                    <li className={currentTab === 3 ? 'tearsheet-container__tab active' : 'tearsheet-container__tab'}>
                      <div
                        role="button"
                        onClick={() => this.handleTab(3)}
                        onKeyDown={() => this.handleTab(3)}
                        tabIndex={0}
                        aria-label="Handle Tab"
                      >
                        {i18n.t('firmContacts')}
                      </div>
                    </li>
                  ) : null}
                </ul>
              </div>
              {currentTab === 0 ? (
                <div className="tearsheet-container__content private">
                  <div className="tearsheet-container__ContentItem private">
                    <div id="shareholderOverviewSummary" className="basic-filter-content shareholderOverview dark">
                      <div className="filters">
                        <div className="content">
                          {this.state.chartData &&
                          this.state.chartData.length > 0 &&
                          this.state.chartData[0].data.length > 0
                            ? (loadingSummaryPeriod && <Loading />) || (
                                <HistorySummaryListContent title={i18n.t('tearsheetPeriod')}>
                                  <HistorySummaryListItem
                                    legend={i18n.t('tearsheetMinimum')}
                                    icon={IMAGES.MINIMUM_ICON}
                                    value={
                                      summaryPeriod.minimum === null ? '--' : this.formatted(summaryPeriod.minimum)
                                    }
                                    date={
                                      summaryPeriod.minimumDate === null
                                        ? '--'
                                        : moment.utc(summaryPeriod.minimumDate).format(i18n.t('dateFormat'))
                                    }
                                  />
                                  <HistorySummaryListItem
                                    legend={i18n.t('tearsheetMaximum')}
                                    icon={IMAGES.MAXIMUM_ICON}
                                    value={
                                      summaryPeriod.maximum === null ? '--' : this.formatted(summaryPeriod.maximum)
                                    }
                                    date={
                                      summaryPeriod.maximumDate === null
                                        ? '--'
                                        : moment.utc(summaryPeriod.maximumDate).format(i18n.t('dateFormat'))
                                    }
                                  />
                                  <HistorySummaryListItem
                                    className={
                                      Number(summaryPeriod.variation) > 0
                                        ? 'green'
                                        : Number(summaryPeriod.variation) < 0
                                          ? 'red'
                                          : ''
                                    }
                                    legend={i18n.t('tearsheetVariation')}
                                    icon={
                                      Number(summaryPeriod.variation) > 0
                                        ? IMAGES.VARIATIONUP_ICON
                                        : Number(summaryPeriod.variation) < 0
                                          ? IMAGES.VARIATIONDOWN_ICON
                                          : IMAGES.VARIATIONNONE_ICON
                                    }
                                    value={
                                      summaryPeriod.variation === null ? '--' : this.formatted(summaryPeriod.variation)
                                    }
                                    variation={
                                      summaryPeriod.variationPct === null
                                        ? '--'
                                        : `(${this.formatted(summaryPeriod.variationPct)}%)`
                                    }
                                  />
                                  <HistorySummaryListItem
                                    legend={i18n.t('tearsheetLastSale')}
                                    icon={IMAGES.SALE_ICON}
                                    value={
                                      summaryPeriod.lastSale === null
                                        ? '--'
                                        : moment.utc(summaryPeriod.lastSale).format(i18n.t('dateFormat'))
                                    }
                                  />
                                </HistorySummaryListContent>
                              )
                            : null}

                          {this.state.chartData && this.state.chartData.length > 0 ? (
                            this.state.chartData[0].data.length > 0 ? (
                              <HistorySummaryListContent title={i18n.t('pageGroupedTearSheet.viewType')}>
                                <Dropdown
                                  onChange={this.onChangeTypeView}
                                  options={ContentViewType}
                                  selected={typeView}
                                />
                              </HistorySummaryListContent>
                            ) : null
                          ) : null}
                          {this.state.chartData &&
                          this.state.chartData.length > 0 &&
                          this.state.chartData[0].data.length > 0 ? (
                            <HistorySummaryListContent title={i18n.t('pageGroupedTearSheet.export')}>
                              <Icons.Download
                                style={{ cursor: 'pointer', display: 'block', margin: '0 auto' }}
                                onClick={() => {
                                  this.setState({ showExportModal: true })
                                }}
                                size={20}
                              />
                            </HistorySummaryListContent>
                          ) : null}
                        </div>
                      </div>
                    </div>
                    <div className="content-graph">
                      {isLoadingPositionHistory ? (
                        <Loading />
                      ) : (
                        <div>
                          {this.state.typeView.value === '1' ? (
                            this.state.chartData && this.state.chartData.length > 0 ? (
                              this.state.chartData[0].data.length > 0 ? (
                                <Charts.Line
                                  title={i18n.t('positionHistory')}
                                  legend={true}
                                  series={this.state.chartData}
                                  categories={this.state.chartCategories}
                                  xAxisInterval={Math.round(this.state.chartCategories.length / 20)}
                                />
                              ) : (
                                <p className="content-text">{i18n.t('pageGroupedTearSheet.noPositoinHistoryFound')}</p>
                              )
                            ) : (
                              <p className="content-text">{i18n.t('pageGroupedTearSheet.chartPlaceholder')}</p>
                            )
                          ) : this.state.chartData.length > 0 ? (
                            this.state.chartData[0].data.length > 0 ? (
                              <div className="tearsheet-container__listContent tearsheet-container__listContent--tearsheetOverview">
                                <div className="tearsheet-container__listContent-header">
                                  <ul className="list">
                                    <li className="list__item list__item-header">
                                      <span className="title">{i18n.t('shareholderOverview.date')}</span>
                                      <span className="title">{i18n.t('shareholderOverview.initialPosition')}</span>
                                      <span className="title">{i18n.t('shareholderOverview.variation')}</span>
                                      <span className="title">{i18n.t('shareholderOverview.finalPosition')}</span>
                                      <span className="title">{i18n.t('shareholderOverview.chartPosition')} (%)</span>
                                      <span className="title">{i18n.t('shareholderOverview.patrimony')}</span>
                                      <span className="title">{`${i18n.t('shareholderOverview.averagePrice')}*`}</span>
                                      <span className="title">{`${i18n.t('shareholderOverview.closingPrice')}`}</span>
                                      <span className="title">{i18n.t('shareholderOverview.lowPrice')}</span>
                                      <span className="title">{i18n.t('shareholderOverview.highPrice')}</span>
                                      <span className="title">{i18n.t('shareholderOverview.minMovement')}</span>
                                      <span className="title">{i18n.t('shareholderOverview.maxMovement')}</span>
                                    </li>
                                  </ul>
                                </div>
                                <ul className="list list-group scroll full-width">
                                  {this.state.tableData.length > 0 ? (
                                    this.state.tableData.map((item) => (
                                      <li key={item.patrimony} className="list__item">
                                        <span>{moment.utc(item.referenceDate).format(i18n.t('dateFormat'))}</span>
                                        <span>{this.formatted(item.stockAmountInitial)}</span>
                                        <span>{this.formatted(item.deltaDay)}</span>
                                        <span>{this.formatted(item.stockAmountEdited)}</span>
                                        <span>
                                          {parseFloat(item.percentage) === 0
                                            ? `> ${this.formatted('00.01')}%`
                                            : `${this.formatRepresentativeness(item.percentage)}%`}
                                        </span>
                                        <span>{this.formatted(item.patrimony)}</span>
                                        <span>
                                          {item.averagePrice
                                            ? `R$ ${utils.formatLocale(item.averagePrice)}`
                                            : 'R$ 0,00'}
                                        </span>
                                        <span>
                                          {item.closingPrice
                                            ? `R$ ${utils.formatLocale(item.closingPrice)}`
                                            : 'R$ 0,00'}
                                        </span>
                                        <span>
                                          {item.lowPrice ? `R$ ${utils.formatLocale(item.lowPrice)}` : 'R$ 0,00 '}
                                        </span>
                                        <span>
                                          {item.highPrice ? `R$ ${utils.formatLocale(item.highPrice)}` : 'R$ 0,00'}
                                        </span>
                                        <span>{`R$ ${utils.formatLocale(item.minimumMovement)}`}</span>
                                        <span>{`R$ ${utils.formatLocale(item.maximumMovement)}`}</span>
                                      </li>
                                    ))
                                  ) : (
                                    <li className="list__item">
                                      <span className="notFound">
                                        {i18n.t('pageGroupedTearSheet.noInformationFound')}
                                      </span>
                                    </li>
                                  )}
                                </ul>
                                <p className="disclaimer">{`* ${i18n.t('shareholderOverview.closingPriceDisclaimer')}`}</p>
                              </div>
                            ) : (
                              <p className="content-text">{i18n.t('pageGroupedTearSheet.noPositoinHistoryFound')}</p>
                            )
                          ) : (
                            <p className="content-text">{i18n.t('pageGroupedTearSheet.chartPlaceholder')}</p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : null}
              {currentTab === 1 ? (
                <div>
                  <div className="tearsheet-container__content list irm-tearSheet">
                    {loadingVinculate ? (
                      <div className="lds-dual-ring">
                        <div />
                      </div>
                    ) : (
                      <div className="tearsheet-container__ContentItem">
                        <div className="tearsheet-container__FlexTitle">
                          <h4 className="content-title">{i18n.t('pageGroupedTearSheet.vinculatedShareholders')}</h4>
                          <div className="action__group">
                            <div className="tearsheet-container__search">
                              <input
                                className="search_fund"
                                autoComplete="off"
                                id={fundSearchInputForm.searchTerm.id}
                                name={fundSearchInputForm.searchTerm.id}
                                placeholder={i18n.t('pageGroupedTearSheet.filterShareholder')}
                                onChange={(e) =>
                                  this.searchInputChangedHandler(
                                    e,
                                    fundSearchInputForm.searchTerm.id,
                                    fundSearchInputForm.id
                                  )
                                }
                                type={fundSearchInputForm.searchTerm.inputType}
                                value={fundSearchInputForm.searchTerm.value}
                              />
                            </div>
                            <Buttons.Primary
                              $height="35px"
                              $margin="0 15px 0 0"
                              onClick={() => this.onOpenModalFunds()}
                            >
                              {i18n.t('pageGroupedTearSheet.vinculateShareholder')}
                            </Buttons.Primary>

                            <Buttons.Export $height="35px" onClick={() => this.onExportFunds()}>
                              {i18n.t('btnExport')}
                            </Buttons.Export>
                          </div>
                        </div>
                        <div className="tearsheet-container__listContent scroll">
                          <ul className="vinculated-shareholders list list-group full-width">
                            <li className="list__item list__item-header">
                              <span className="name title">{i18n.t('name')}</span>
                              <span className="volume title">{i18n.t('finalVolume')}</span>
                              <span className="variation title">{i18n.t('variation')}</span>
                              <span className="doc title">{i18n.t('pageGroupedTearSheet.type')}</span>
                              <span className="doc title">{i18n.t('document')}</span>
                              <span className="country title">{i18n.t('country')}</span>
                              <span className="actions title">{i18n.t('actions')}</span>
                            </li>
                            {this.state.shareholderFunds.map((item) => (
                              <li className="list__item" key={item.shareholderId}>
                                <span className="name">
                                  <Link
                                    to={`${PATH}/ownership/${item.shareholderId}/simple/overview`}
                                    data-tip={item.displayName}
                                  >
                                    {item.displayName}
                                  </Link>
                                </span>
                                <span className="volume">{this.formatted(item.stockAmountEnd)}</span>
                                <span
                                  className={
                                    Number(item.variation) > 0
                                      ? 'variation green'
                                      : Number(item.variation) < 0
                                        ? 'variation red'
                                        : 'variation'
                                  }
                                >
                                  {this.formatted(item.variation)}
                                </span>
                                <span className="doc">{this.getShareholderType(item.shareholderType)}</span>
                                <span className="doc">{this.cpfCnpj(item.document)}</span>
                                <span className="country">{item.country !== null ? item.country : '--'}</span>
                                <span className="actions">
                                  <span
                                    className="icon icon-delete"
                                    data-tip={i18n.t('pageGroupedTearSheet.delete')}
                                    onClick={() => this.openRemoveFund(item)}
                                    onKeyDown={() => this.openRemoveFund(item)}
                                    tabIndex={0}
                                    role="button"
                                  >
                                    {i18n.t('pageGroupedTearSheet.delete')}
                                  </span>
                                </span>
                                <ReactTooltip place="top" delayShow={50} />
                              </li>
                            ))}
                            {this.state.listLoading ? (
                              <li className="ShareholderNew__ListItem list-loading">
                                <Loading />
                              </li>
                            ) : null}
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : null}
              {currentTab === 2 ? (
                <div>
                  <div className="tearsheet-container__content list irm-tearSheet">
                    <div className="tearsheet-container__ContentItem">
                      <div className="tearsheet-container__FlexTitle">
                        <h4 className="content-title">{i18n.t('pageGroupedTearSheet.tasks')}</h4>
                        <div>
                          <Buttons.Primary onClick={() => this.openTaskModal()}>
                            {i18n.t('pageGroupedTearSheet.addTask')}
                          </Buttons.Primary>
                        </div>
                      </div>
                      <div className="tearsheet-container__listContent task">
                        <div className="scroll">
                          <ul className="list task-list list-group full-width" style={{ paddingLeft: '20px' }}>
                            <li className="list__item list__item-header">
                              <span className="date title">{i18n.t('pageGroupedTearSheet.date')}</span>
                              <span className="name title">{i18n.t('pageGroupedTearSheet.title')}</span>
                              <span className="activity title">{i18n.t('pageGroupedTearSheet.type')}</span>
                              <span className="activity title">{i18n.t('pageGroupedTearSheet.subType')}</span>
                              <span className="contact title">{i18n.t('pageGroupedTearSheet.participants')}</span>
                              <span className="executors title">{i18n.t('pageGroupedTearSheet.executors')}</span>
                              <span className="groups title">{i18n.t('pageGroupedTearSheet.groups')}</span>
                              <span className="actions title">{i18n.t('actions')}</span>
                            </li>
                            {fundTasks.map((item, index) => (
                              <li className="list__item" key={item.task_id}>
                                <span className="date">{moment.utc(item.task_due).format(i18n.t('dateFormat'))}</span>
                                <span className="name">
                                  <ExternalLink product={EXTERNAL_PRODUCT.IRM} pagePath={`/activity/${item.task_id}`}>
                                    {item.task_title}
                                  </ExternalLink>
                                </span>
                                <span className="activity">
                                  {item.mz_task_type_name ? item.mz_task_type_name : '--'}
                                </span>
                                <span className="activity">
                                  {item.mz_task_subtype_name ? item.mz_task_subtype_name : '--'}
                                </span>
                                <span className="contact">{item.count_contacts}</span>
                                <span className="executors">
                                  {item.executorsArray
                                    ? item.executorsArray.map((executor) =>
                                        index < 2 ? (
                                          <span
                                            key={executor.name}
                                            className="executors__ball"
                                            data-tip={executor.name}
                                          >
                                            {executor.initials}
                                          </span>
                                        ) : null
                                      )
                                    : '--'}
                                  {item.executorsArray ? (
                                    item.executorsArray.length > 2 ? (
                                      <span
                                        data-tip={item.executorsArray
                                          .filter((executor, i) => {
                                            if (i > 1) return executor.name
                                            return null
                                          })
                                          .map((executor) => executor.name)
                                          .join('<br/>')}
                                        data-html
                                      >
                                        {`+${item.executorsArray.length - 2}`}
                                      </span>
                                    ) : null
                                  ) : null}
                                </span>
                                <span className="groups">{item.count_shareholders}</span>
                                <span className="actions">
                                  <span
                                    className="icon icon-delete"
                                    data-tip={i18n.t('pageGroupedTearSheet.delete')}
                                    onClick={() => this.openRemoveTask(item.task_id)}
                                    onKeyDown={() => this.openRemoveTask(item.task_id)}
                                    tabIndex={0}
                                    role="button"
                                  >
                                    {i18n.t('pageGroupedTearSheet.delete')}
                                  </span>
                                </span>
                                <ReactTooltip place="top" delayShow={50} />
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
              {currentTab === 3 ? (
                <div className="tearsheet-container__content">
                  <div className="tearsheet-container__ContentItem">
                    <div className="tearsheet-container__FlexTitle">
                      <h4 className="content-title">{i18n.t('contacts')}</h4>
                      <Buttons.Primary onClick={() => this.openNewContactModal()}>
                        {i18n.t('pageGroupedTearSheet.newContact')}
                      </Buttons.Primary>
                    </div>
                    <div className="tearsheet-container__contacts">
                      {fundContacts.map((item) => (
                        <div
                          className="tearsheet-container__contact"
                          key={item.id || item.dealogic_investor_contact_id}
                        >
                          {item.id ? (
                            <Link className="contact__picture" to={`/shareholders/contact/${item.id}`}>
                              <img src={item.profile_picture_url == null ? userImg : item.profile_picture_url} alt="" />
                            </Link>
                          ) : (
                            <ExternalLink
                              product={EXTERNAL_PRODUCT.IRM}
                              pagePath={`/contact/public/${item.dealogic_investor_contact_id}/${item.id}`}
                              className="contact__picture"
                            >
                              <img src={item.profile_picture_url == null ? userImg : item.profile_picture_url} alt="" />
                            </ExternalLink>
                          )}
                          <div className="tearsheet-container__ContactInfo">
                            {item.id ? (
                              <Link className="contact__info" to={`/shareholders/contact/${item.id}`}>
                                {item.name}
                              </Link>
                            ) : (
                              <ExternalLink
                                product={EXTERNAL_PRODUCT.IRM}
                                pagePath={`/contact/public/${item.dealogic_investor_contact_id}/${item.id}`}
                                className="contact__info"
                              >
                                {item.name}
                              </ExternalLink>
                            )}

                            {item.id ? (
                              <Link className="contact__info" to={`/shareholders/contact/${item.id}`}>
                                {item.job_title}
                              </Link>
                            ) : (
                              <ExternalLink
                                product={EXTERNAL_PRODUCT.IRM}
                                pagePath={`/contact/public/${item.dealogic_investor_contact_id}/${item.id}`}
                                className="contact__info"
                              >
                                {item.job_title}
                              </ExternalLink>
                            )}

                            {item.id ? (
                              <Link className="contact__info" to={`/shareholders/contact/${item.id}`}>
                                {item.email_1}
                              </Link>
                            ) : (
                              <ExternalLink
                                product={EXTERNAL_PRODUCT.IRM}
                                pagePath={`/contact/public/${item.dealogic_investor_contact_id}/${item.id}`}
                                className="contact__info"
                              >
                                {item.email_1}
                              </ExternalLink>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : null}
              {currentTab === 4 ? (
                <div className="tearsheet-container__content private">
                  {hasPortfolioAnalysis ? (
                    historyDataPositions != null ||
                    (sectorData != null && topPeersData != null && topCountriesData != null) ? (
                      <div className="tearsheet-container__full">
                        <div className="ContactNew__PortfolioAnalysis">
                          <h3 className="content-title">
                            <div style={{ display: 'inline-flex' }}>
                              <span style={{ color: '#fff', marginRight: '5px' }}>
                                {i18n.t('pageGroupedTearSheet.showingPublicInformation')}:{' '}
                              </span>
                              <Tooltip
                                text={i18n.t('pageGroupedTearSheet.seeIntelligenceDetails')}
                                $width="auto"
                                $alignment={ALIGNMENTS.BOTTOM_CENTER}
                              >
                                <ExternalLink
                                  product={EXTERNAL_PRODUCT.INTELLIGENCE}
                                  pagePath={`/institution/tearsheet/${institutionId}`}
                                  style={{ color: '#3f91d8' }}
                                >
                                  {institutionName}
                                </ExternalLink>
                              </Tooltip>
                            </div>
                          </h3>
                          <div>
                            <Buttons.Primary
                              $minWidth="120px"
                              $height="20px"
                              $fontSize="11px"
                              onClick={() => this.removeIntegrationsBind('institution', institutionId)}
                            >
                              {i18n.t('pageGroupedTearSheet.unbind')}
                            </Buttons.Primary>
                          </div>
                        </div>
                        {mustRender ? null : (
                          <div className="ContactNew__graphs">
                            {this.renderContactHistory()}
                            {this.renderSector()}
                            {this.renderTopPeers()}
                            {this.renderTopCountries()}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="lds-dual-ring">
                        <div />
                      </div>
                    )
                  ) : (
                    <div className="ContactNew__ContentItem center">
                      <h2 className="content-text">{i18n.t('pageGroupedTearSheet.fundAssociationEmptyMessage')}</h2>
                      <Buttons.Primary
                        onClick={() =>
                          this.openIntegrationsModal(ENTITY_SEARCH_CONTEXT.INSTITUTION_SELECTION, this.props.params.id)
                        }
                        $margin="15px 0 0 0"
                      >
                        {i18n.t('pageGroupedTearSheet.bind')}
                      </Buttons.Primary>
                    </div>
                  )}
                </div>
              ) : null}
            </div>

            <VinculateShareholderModal
              cpfCnpj={this.cpfCnpj}
              getShareholderType={this.getShareholderType}
              shareholderGroupId={this.props.params.id}
              shareholderFundsReload={this.shareholderFundsReload}
              onClose={this.onCloseModalFunds}
              title={i18n.t('pageGroupedTearSheet.vinculateShareholder')}
              visibility={this.state.modalFunds}
              placeholder={i18n.t('pageGroupedTearSheet.typeShareholderNameOrDocument')}
            />

            <DeleteModal
              show={confirmRemoveTaskModal}
              message={i18n.t('pageGroupedTearSheet.removeTaskConfirmation')}
              onDelete={this.deleteTask}
              onClose={this.closeRemoveTask}
              cancelButtonLabel={i18n.t('components.deleteModal.cancelButton')}
              deleteButtonLabel={i18n.t('components.deleteModal.deleteButton')}
            />

            <DeleteModal
              show={confirmRemoveFundModal}
              message={i18n.t('pageGroupedTearSheet.removeTaskConfirmation')}
              onDelete={this.unlinkShareholderFromGroup}
              onClose={this.closeRemoveFund}
              cancelButtonLabel={i18n.t('components.deleteModal.cancelButton')}
              deleteButtonLabel={i18n.t('components.deleteModal.deleteButton')}
            />

            <ConfirmationModal
              show={isNameModalOpen}
              title={i18n.t('exportHistory.sendAlert.confirmTitle')}
              message={`${i18n.t('changeName')} ${originalName} ${i18n.t('to')} ${name}. ${i18n.t('confirmChanges')}`}
              onConfirm={this.submitNameModal}
              onClose={this.closeNameModal}
              cancelButtonLabel={i18n.t('components.confirmModal.cancelButton')}
              confirmButtonLabel={i18n.t('components.confirmModal.confirmButton')}
            />

            <DeleteModal
              show={isDeleteModalOpen}
              message={i18n.t('shareholders.deleteGroupingMessage')}
              onDelete={this.deleteGroup}
              onClose={this.closeDeleteModal}
              cancelButtonLabel={i18n.t('components.deleteModal.cancelButton')}
              deleteButtonLabel={i18n.t('components.deleteModal.deleteButton')}
            />

            <TearsheetModal
              show={this.state.showExportModal}
              onClose={() => this.setState({ showExportModal: false })}
              onExportSelectedPeriod={() => {
                this.onExportPosition(chartStartDate, chartEndDate)
              }}
              onExportHistoricPosition={this.onExportHistoricPosition}
            />

            <BindConfirmationModal
              group={this.props.params.id}
              show={this.state.bindConfirmationModal}
              onClose={() => {
                this.setState({ bindConfirmationModal: false })
              }}
              onConfirm={() => {
                this.setState({ bindConfirmationModal: false })
                this.openIntegrationsModal(ENTITY_SEARCH_CONTEXT.INSTITUTION_SELECTION, this.props.params.id)
              }}
            />
            <CompanyDetailsModal
              mode={this.state.companyDetailsModal.mode}
              show={this.state.companyDetailsModal.isOpen}
              entitySearchContext={this.state.companyDetailsModal.entitySearchContext}
              applicationContext={this.state.companyDetailsModal.applicationContext}
              company={this.state.companyDetailsModal.company}
              closeInBack={this.state.companyDetailsModal.closeInBack}
              onClose={() => {
                this.closeCompanyDetailsModal()
                this.getIntegrationsBindForShareholder()
              }}
            />
          </>
        )}
      </div>
    )
  }
}

BaseGroupedOverview.contextType = StoreContext

export const GroupedOverview = hocWrapper(BaseGroupedOverview)
