export type HistorySummary = {
  className?: string
  icon: string
  legend: string
  profitLoss?: string
  value: number | string
  date?: string
  variation?: number
}

export function HistorySummaryListItem(props: HistorySummary) {
  const { className = '', icon, legend, profitLoss, value, date, variation } = props
  return (
    <li className={`shareholderOverview__summaryHistory__item ${className}`}>
      <img src={icon} alt="icon" />
      <span className="shareholderOverview__summaryHistory__item__legend">
        {legend}{' '}
        {profitLoss && <strong className="shareholderOverview__summaryHistory__item__value"> {profitLoss}</strong>}:
      </span>
      <strong className="shareholderOverview__summaryHistory__item__value">{value}</strong>
      {variation && <span className="shareholderOverview__summaryHistory__item__variation"> {variation} </span>}
      {date && <span className="shareholderOverview__summaryHistory__item__date"> {date} </span>}
    </li>
  )
}
