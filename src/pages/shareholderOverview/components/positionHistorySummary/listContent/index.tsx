import { ReactNode } from 'react'
import '../styles/style.scss'
import { Icons, Tooltip, ALIGNMENTS } from '@mz-codes/design-system'

type params = {
  children: ReactNode
  title: string
  tooltip?: string
}

export function HistorySummaryListContent({ children, title, tooltip }: params) {
  return (
    <ul className="shareholderOverview__summaryHistory">
      <li className="shareholderOverview__summaryHistory__title">
        {title}
        <span className="shareholderOverview__summaryHistory__title__tooltipArea">
          {tooltip && (
            <Tooltip $alignment={ALIGNMENTS.BOTTOM_CENTER} $width="280px" text={tooltip}>
              <Icons.OutlineInfoCircle size={14} />
            </Tooltip>
          )}
        </span>
      </li>
      {children}
    </ul>
  )
}
