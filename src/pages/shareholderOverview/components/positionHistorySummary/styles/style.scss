@use '../../../../../assets/styles/utils/variables';

$gray: #636365;
$green: #6ece1a;
$red: #c14646;

#shareholderOverviewSummary {
  height: auto;
  padding: 0 20px;

  .filter-by {
    min-width: inherit;
    float: none;
    flex: inherit;
  }
}

.shareholderOverview {
  &__summaryHistory {
    li {
      list-style: none;
    }

    &__title {
      font-weight: bold;
      font-size: 14px;
      line-height: 12px;
      color: variables.$main-light-blue;
      display: block;
      margin-bottom: 10px;
      display: flex;
      align-items: center;

      &__tooltipArea {
        $this: &;
        margin-left: 5px;
        position: relative;
        cursor: pointer;

        &__icon {
          width: 13px;
        }

        &__content {
          position: absolute;
          background-color: #171717;
          border: 1px solid variables.$main-light-blue;
          top: 0;
          left: 285%;
          visibility: hidden;
          width: 240px;
          z-index: 9;
          display: flex;
          align-items: center;
          padding: 10px;
          line-height: 1.1em;

          p {
            margin-bottom: 5px;
            color: white;

            span {
              color: variables.$main-light-blue;
            }
          }

          &::before {
            content: '';
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 2.5px 5px 2.5px 0;
            border-color: transparent variables.$main-light-blue transparent transparent;
            right: 100%;
            position: absolute;
            top: 0;
          }
        }

        &:hover {
          #{ $this } {
            &__content {
              visibility: visible;
            }
          }
        }
      }
    }

    &__item {
      $this: &;
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      img {
        margin-right: 7px;
      }

      &__legend,
      &__value,
      &__date,
      &__variation {
        font-weight: normal;
        font-size: 14px;
        line-height: 12px;
        color: white;
      }

      &__value {
        font-weight: bold;
        margin-left: 5px;
      }

      &__variation,
      &__date {
        margin-left: 12px;
        color: $gray;
      }

      &.green,
      &.profit {
        #{ $this } {
          &__value,
          &__variation {
            color: $green;
          }
        }
      }

      &.red,
      &.loss {
        #{ $this } {
          &__value,
          &__variation {
            color: $red;
          }
        }
      }
    }
  }
}
