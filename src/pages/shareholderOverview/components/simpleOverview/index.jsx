/* eslint-disable react/no-unused-class-component-methods */
import React, { Component } from 'react'
import moment from 'moment'
import { Link } from 'react-router-dom'
import { Dropdown, Icons, Loading, ConfirmationModal, DeleteModal, Charts, Datepicker } from '@mz-codes/design-system'

import {
  dateFromUTC,
  defineTheme,
  formatDateToString,
  localInformation,
  subtractDays,
  hasProductPermission,
  formatNumber,
} from 'utils'

import { getInstitutionTearSheet, getTickers, ungroup, updateShareholderDisplayName } from 'client'
import * as downloadClient from 'client/downloadClient'

import { i18n } from 'translate'

import { IMAGES } from 'assets'
import { GroupModal, TearsheetModal, LinkButton, NewOptionDropdown, GoToHistoryButton } from 'components'

import 'assets/styles/layout/_fundHistory.scss'
import { PATH, ContentViewType } from 'consts'
import {
  deleteUnvinculateClassification,
  getAvailableDates,
  getExportHistoricSimplePositions,
  getGroups,
  hocWrapper,
  patchVinculateClassification,
  postCreateClassification,
  postCreateGroupAndGroupShareholder,
  postGroupShareholder,
} from 'hooks'
import '../styles/style.scss'

import { theme, Tooltip, ALIGNMENTS } from '@mz-codes/design-system'

import { getHistoryOverview, getPeriodOverview, getSimplePositionList } from '../../services/overview'

import { HistorySummaryListContent, HistorySummaryListItem } from '../positionHistorySummary'
import { getClassificationsList } from 'hooks'
import { BaseError } from 'errors'
import { SHAREHOLDER_TYPES } from 'types/shareholders'

class BaseSimpleOverview extends Component {
  static cpfCnpj = (value) => {
    let newValue = value.replace(/\D/g, '')

    if (newValue.length <= 11) {
      // CPF
      newValue = newValue.replace(/(\d{3})(\d)/, '$1.$2')
      newValue = newValue.replace(/(\d{3})(\d)/, '$1.$2')
      newValue = newValue.replace(/(\d{3})(\d{1,2})$/, '$1-$2')
    } else {
      // CNPJ
      newValue = newValue.replace(/^(\d{2})(\d)/, '$1.$2')
      newValue = newValue.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
      newValue = newValue.replace(/\.(\d{3})(\d)/, '.$1/$2')
      newValue = newValue.replace(/(\d{4})(\d)/, '$1-$2')
    }
    return newValue
  }

  static shareholderSummaryTooltip = () => (
    <div>
      <p
        style={{
          marginBottom: '15px',
        }}
      >
        <span style={{ color: theme.legacy.colors.primary.primary }}>{i18n.t('shareholderOverview.bestCase')}</span>
        {i18n.t('shareholderOverview.bestCaseDisclaimer')}
      </p>
      <p>
        <span style={{ color: theme.legacy.colors.semantic.negative }}>{i18n.t('shareholderOverview.worstCase')}</span>
        {i18n.t('shareholderOverview.worstCaseDisclaimer')}
      </p>
    </div>
  )

  static checkProduct(product) {
    return hasProductPermission(product)
  }

  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      companyId: localInformation.getCompanyId(),
      chartCategories: [],
      chartData: [],
      chartEndDate: null,
      groupList: [],
      chartStartDate: null,
      confirmRemoveFundModal: false,
      currentFund: null,
      currentTab: 0,
      currentTicker: null,
      datesAvailable: [],
      document: '',
      isFundsError: false,
      isLoading: false,
      isLoadingFunds: true,
      isNameModalOpen: false,
      mustRender: true,
      name: '',
      originalName: '',
      shareholder: null,
      shareholderDocType: '',
      shareholderGroupId: '',
      showAggModal: false,
      showExportModal: false,
      tableData: [],
      tickers: [],
      typeView: ContentViewType[0],
      summaryPeriod: null,
      loadingSummaryPeriod: true,
      summaryHistoric: null,
      loadingSummaryHistoric: true,
      shareholderHasAnotherGroup: false,
      classificationsList: [],
      classificationInputValue: '',
      classificationSelectedValue: null,
      classificationCloseDropdown: false,
      classificationLoading: false,
      unvinculateClassificationModal: false,
      confirmCreateAndVinculateClassificationModal: false,
    }
  }

  async componentDidMount() {
    try {
      this.setState({ isLoading: true })
      await this.getTickers()
      await this.getInstitutionTearSheet()
      await this.getAvailableDates()
      await this.handleListClassifications()
      await this.handleGetGroups()
      await this.getUpdateInfo()

      this.setState({ isLoading: false, finishApiFetch: true })
    } catch (error) {
      this.setState({ isLoading: false })
    }
  }

  componentDidUpdate(_prevProps, prevState) {
    if (this.state.currentTicker !== prevState.currentTicker) {
      this.getAvailableDates()
      this.getInstitutionTearSheet()
    }

    if (!prevState.finishApiFetch && this.state.finishApiFetch) {
      if (
        this.state.datesAvailable !== prevState.datesAvailable ||
        this.state.chartStartDate !== prevState.chartStartDate ||
        this.state.chartEndDate !== prevState.chartEndDate
      ) {
        this.getUpdateInfo()
      }
    }
  }

  async getUpdateInfo() {
    this.getFundHistoryOnCompany()
    this.getHistorySummary()
    this.getPeriodSummary()
  }

  onChangeTypeView = (viewType) => {
    const { typeView } = this.state
    if (typeView === viewType) return

    this.setState({ typeView: viewType })
  }

  getInstitutionTearSheet = async () => {
    const { companyId } = this.state
    const response = await getInstitutionTearSheet(companyId, this.props.params.id)

    this.setState({
      shareholder: response.data,
      currentFund: response.data.shareholder,
      name: response.data.shareholder.displayName,
      originalName: response.data.shareholder.displayName,
      document: response.data.shareholder.document,
      shareholderDocType: response.data.shareholder.shareholder_type,
      shareholderGroupId: response.data.shareholderGroup.shareholder_group_id,
    })
  }

  getTickers = async () => {
    const { companyId } = this.state
    const response = await getTickers(companyId)
    if (response.success) {
      const mappedTickers = response.data.map((ticker) => {
        return {
          label: ticker.label,
          value: ticker.tickerId,
        }
      })
      this.setState({
        tickers: mappedTickers,
        currentTicker: mappedTickers[0],
      })
    }
  }

  handleGetGroups = async () => {
    try {
      const { companyId } = this.state
      const shareholderGroups = await getGroups(companyId)

      const formatShareholderGroups = shareholderGroups.data.data.map((shareholderGroup) => ({
        label: shareholderGroup.name,
        value: shareholderGroup.shareholderGroupId,
      }))

      this.setState({
        groupList: formatShareholderGroups,
      })
    } catch {
      return this.props.createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        type: 'error',
      })
    }
  }

  getChartStartDate = (dates, endDate) => {
    const limitDate = subtractDays(endDate, 30)

    const chartStartDate = dates
      .filter((date) => {
        return date <= limitDate
      })
      .at(-1)

    return chartStartDate || dates[dates.length - 1]
  }

  getAvailableDates = async () => {
    const { currentTicker, companyId } = this.state

    const res = await getAvailableDates({ companyId, tickerId: currentTicker.value })
    const datesAvailable = res.sort((a, b) => (a > b ? 1 : -1)).map(dateFromUTC)
    const chartEndDate = datesAvailable.at(-1)
    const chartStartDate = this.getChartStartDate(datesAvailable, chartEndDate)
    this.setState({ datesAvailable, chartStartDate, chartEndDate })
  }

  onChangeStockType = (item) => {
    const prevTicker = this.state.tickers
    const newTicker = prevTicker.filter((ticker) => {
      if (ticker.value === item.value) {
        return ticker
      }
      return null
    })

    this.setState({ currentTicker: newTicker[0], typeView: ContentViewType[0] }, async () => {
      this.getUpdateInfo()
    })
  }

  onChangeStartDate = (date) => {
    const { chartStartDate } = this.state
    if (date === chartStartDate) return
    this.setState({ chartStartDate: date, typeView: ContentViewType[0] }, async () => {
      this.getUpdateInfo()
    })
  }

  onChangeEndDate = (date) => {
    const { chartEndDate } = this.state
    if (date === chartEndDate) return
    this.setState({ chartEndDate: date, typeView: ContentViewType[0] }, async () => {
      this.getUpdateInfo()
    })
  }

  // eslint-disable-next-line class-methods-use-this
  renderLoading = () => {
    return (
      <div className="funds-wrapper">
        <div className={defineTheme('funds-content')}>
          <div className="lds-dual-ring">
            <div />
          </div>
        </div>
      </div>
    )
  }

  getFundHistoryOnCompany = async () => {
    try {
      const { chartStartDate, chartEndDate, currentTicker, companyId } = this.state

      this.setState({
        isFundsError: false,
        isLoadingFunds: true,
      })

      const res = await getSimplePositionList({
        companyId,
        tickerId: currentTicker.value,
        shareholderId: this.props.params.id,
        startDate: formatDateToString(chartStartDate),
        endDate: formatDateToString(chartEndDate),
      })

      const cat = res.positions.map((item) =>
        formatDateToString(dateFromUTC(item.referenceDate), i18n.t('globals.DatePickerDateFormat'))
      )

      const positions = res.positions.map((item) => ({
        y: parseInt(item.finalStocks, 10),
        additional: [
          {
            title: i18n.t('globals.percentage'),
            value: item.stocksPercentage,
            formater: (value) => formatNumber(0, value, 2, '', '%'),
          },
          {
            title: i18n.t('totalAmount'),
            value: item.totalStocks,
            formater: (value) => formatNumber(0, value, 0),
          },
        ],
      }))

      const investiments = res.positions.map((item) => {
        return {
          y: Number(item.finalStocks) * Number(item.closingPrice),
        }
      })

      const prices = res.positions.map((item) => ({ y: Number(item.closingPrice) }))

      const chartData = [
        {
          type: 'area',
          name: i18n.t('positionLegend'),
          yAxis: {
            index: 0,
            visible: true,
          },
          data: positions,
          color: 'rgb(51, 160, 255)',
          colorType: 'linear-gradient',
          lineWidth: 1,
          formater: (value) => formatNumber(0, value, 0),
        },
        {
          type: 'area',
          name: i18n.t('value'),
          yAxis: {
            index: 1,
            visible: false,
          },
          data: investiments,
          color: 'rgb(89, 220, 238)',
          colorType: 'linear-gradient',
          lineWidth: 1,
          formater: (value) => formatNumber(0, value, 2, 'R$ '),
        },
        {
          type: 'area',
          name: i18n.t('price'),
          yAxis: {
            index: 2,
            visible: true,
            opposite: true,
          },
          data: prices,
          color: 'rgb(242, 199, 89)',
          colorType: 'linear-gradient',
          lineWidth: 1,
          formater: (value) => formatNumber(0, value, 2, 'R$ '),
        },
      ]

      const tableData = res.positions.reverse()

      this.setState({
        chartCategories: cat,
        chartData,
        tableData,
        mustRender: false,
        typeView: ContentViewType[0],
      })
    } catch (error) {
      console.log('error', error)
      this.props.createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        type: 'error',
      })
      this.setState({
        mustRender: false,
        isFundsError: true,
        typeView: ContentViewType[0],
      })
    } finally {
      this.setState({
        isLoadingFunds: false,
      })
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.state.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  formatRepresentativeness = (value) => {
    const { idiom } = this.state
    return parseFloat(value).toLocaleString(parseInt(idiom, 10) === 1 ? 'pt-BR' : 'en-US', {
      minimumIntegerDigits: 2,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  inputChangedHandler = (e) => {
    this.setState({ name: e.target.value })
  }

  nameChangedHandler = () => {
    const { name, originalName } = this.state
    if (name !== originalName) {
      this.setState({ isNameModalOpen: true })
    }
  }

  closeNameModal = () => {
    const { originalName } = this.state
    this.setState({ isNameModalOpen: false, name: originalName })
  }

  submitNameModal = () => {
    const { name } = this.state
    updateShareholderDisplayName(this.state.companyId, this.props.params.id, name)
      .then(() => {
        this.props.createToast({
          title: i18n.t('globals.toasts.changeShareholderName.success.title'),
          description: i18n.t('globals.toasts.changeShareholderName.success.message.individual'),
          duration: 9000,
          type: 'success',
        })
        this.setState({ isNameModalOpen: false, originalName: name }, () => this.getInstitutionTearSheet())
      })
      .catch(() => {
        this.props.createToast({
          title: i18n.t('globals.toasts.changeShareholderName.error.title'),
          description: i18n.t('globals.toasts.changeShareholderName.error.message.individual'),
          duration: 9000,
          type: 'error',
        })
      })
  }

  handleTab = (tabIndex) => {
    this.setState({ currentTab: tabIndex })
  }

  onOpenAgglutinationModal = () => {
    this.setState({ showAggModal: true })
  }

  closeAggModal = () => {
    this.setState({ showAggModal: false, shareholderHasAnotherGroup: false })
  }

  handleGroupedShareholderActionsToast = (shareholderGroupId) => {
    this.props.createToast({
      title: i18n.t('globals.groupedShareholderActionsToastfy.success.title'),
      description: i18n.t('globals.groupedShareholderActionsToastfy.success.groupDetails'),
      duration: 15000,
      type: 'success',
      buttons: (
        <LinkButton link={`${PATH}/ownership/${shareholderGroupId}/grouped/overview`}>
          {i18n.t('groupDetail')}
        </LinkButton>
      ),
    })
  }

  handleGroupModalTitleShareholderType = () => {
    const { currentFund } = this.state
    if (currentFund?.shareholder_type === SHAREHOLDER_TYPES.FUND) return i18n.t('ownership.fund').toLowerCase()
    if (currentFund?.shareholder_type === SHAREHOLDER_TYPES.INDIVIDUAL)
      return i18n.t('ownership.individual').toLowerCase()
    if (currentFund?.shareholder_type === SHAREHOLDER_TYPES.UNKNOW) return ''

    return
  }

  onCreateGroup = async (groupName) => {
    const { companyId, currentFund } = this.state
    try {
      const res = await postCreateGroupAndGroupShareholder({
        companyId,
        groupName,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      await this.getInstitutionTearSheet()
      this.setState({ showAggModal: false })
      this.handleGroupedShareholderActionsToast(res.data.shareholderGroupId)
    } catch (error) {
      this.props.createToast({
        title: i18n.t('globals.errors.createGroup.title'),
        description: i18n.t('globals.errors.createGroup.message'),
        duration: 9000,
        type: 'error',
      })

      this.setState({ showAggModal: false })
    }
  }

  onVinculateGroup = async (shareholderGroup) => {
    const { companyId, currentFund } = this.state
    const shareholderGroupId = shareholderGroup.value
    try {
      await postGroupShareholder({
        companyId,
        shareholderGroupId,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      await this.getInstitutionTearSheet()
      this.setState({ showAggModal: false })
      this.handleGroupedShareholderActionsToast(shareholderGroupId)
    } catch (error) {
      this.props.createToast({
        title: i18n.t('globals.errors.vinculateGroup.title'),
        description: i18n.t('globals.errors.vinculateGroup.message'),
        duration: 9000,
        type: 'error',
      })

      await this.getInstitutionTearSheet()
      this.setState({ showAggModal: false, isLoading: false })
    }
  }

  onUngroup = () => {
    ungroup(this.state.companyId, this.state.shareholderGroupId, this.state.currentFund.shareholderId)
      .then(() => {
        this.props.createToast({
          title: i18n.t('globals.ungroupedToastfy.success.title'),
          description: i18n.t('globals.ungroupedToastfy.success.message'),
          duration: 9000,
          type: 'success',
        })

        this.setState({ showAggModal: false })
        this.getInstitutionTearSheet()
        this.closeRemoveFund()
      })
      .catch(() => {
        this.props.createToast({
          title: i18n.t('globals.ungroupedToastfy.errors.title'),
          description: i18n.t('globals.ungroupedToastfy.errors.message'),
          duration: 9000,
          type: 'error',
        })

        this.getInstitutionTearSheet()
        this.setState({ showAggModal: false, isLoading: false })
      })
  }

  openRemoveFund = () => {
    this.setState({
      confirmRemoveFundModal: true,
    })
  }

  closeRemoveFund = () => {
    this.setState({
      confirmRemoveFundModal: false,
    })
  }

  onExportPosition = async (startDate, endDate) => {
    const { currentTicker, companyId } = this.state

    try {
      this.props.createToast({
        type: 'info',
        title: i18n.t('globals.export.sent.title'),
        description: i18n.t('globals.export.sent.message'),
      })

      const params = {
        companyId,
        tickerId: currentTicker.value,
        shareholderId: this.props.params.id,
        referenceDateStart: formatDateToString(startDate),
        referenceDateEnd: formatDateToString(endDate),
      }

      await downloadClient.downloadSimpleHistoryPosition(params)

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      console.error('error', err)
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.export.error.title'),
        description: i18n.t('globals.export.error.message'),
      })
    } finally {
      this.setState({ isLoading: false })
    }
  }

  onExportHistoricPosition = async () => {
    this.props.createToast({
      type: 'info',
      title: i18n.t('globals.export.sent.title'),
      description: i18n.t('globals.export.sent.message'),
    })
    try {
      const { currentTicker } = this.state
      const params = {
        tickerId: currentTicker.value,
        shareholderId: this.props.params.id,
        companyId: this.state.companyId,
      }

      await getExportHistoricSimplePositions(params)

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      console.log(err)
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.export.error.title'),
        description: i18n.t('globals.export.error.message'),
      })
    } finally {
      this.setState({ isLoading: false })
    }
  }

  getHistorySummary = async () => {
    const { companyId, currentTicker, chartStartDate, chartEndDate } = this.state

    const optionsPlaceholder = {
      minimum: '-',
      maximum: '-',
      maximumDate: '-',
      minimumDate: '-',
      firstPurchase: '-',
      averagePrice: '-',
      lastSale: '-',
    }
    this.setState({ loadingSummaryHistoric: true })

    try {
      const response = await getHistoryOverview({
        companyId,
        tickerId: currentTicker.value,
        viewType: 'shareholder',
        entityId: this.props.params.id,
        referenceDateStart: formatDateToString(chartStartDate),
        referenceDateEnd: formatDateToString(chartEndDate),
      })

      const options = response.data?.historic.map((item) => ({
        minimum: item.min,
        maximum: item.max,
        maximumDate: item.maxDate,
        minimumDate: item.minDate,
        firstPurchase: item.firstPurchase,
        averagePrice: item.averagePrice,
        lastSale: item.lastSale,
      }))

      this.setState({
        summaryHistoric: options ? options[0] : optionsPlaceholder,
      })
    } catch (e) {
      console.log(e)
    } finally {
      this.setState({
        loadingSummaryHistoric: false,
      })
    }
  }

  getPeriodSummary = async () => {
    const { companyId, currentTicker, chartStartDate, chartEndDate } = this.state

    this.setState({ loadingSummaryPeriod: true })

    let options = [
      {
        minimum: 0,
        maximum: 0,
        maximumDate: 0,
        minimumDate: 0,
        variation: 0,
        variationPct: 0,
        averagePrice: 0,
        lastSale: 0,
      },
    ]

    try {
      const response = await getPeriodOverview({
        companyId,
        tickerId: currentTicker.value,
        viewType: 'shareholder',
        entityId: this.props.params.id,
        referenceDateStart: formatDateToString(chartStartDate),
        referenceDateEnd: formatDateToString(chartEndDate),
      })

      if (response) {
        options = response.data.period.map((item) => ({
          minimum: item.min,
          maximum: item.max,
          maximumDate: item.maxDate,
          minimumDate: item.minDate,
          variation: item.variation,
          variationPct: item.variationPercentage,
          averagePrice: item.averagePrice,
          lastSale: item.lastSale,
        }))
      }

      this.setState({ summaryPeriod: options[0] })
    } catch (error) {
      console.log(error)
    } finally {
      this.setState({
        loadingSummaryPeriod: false,
      })
    }
  }

  handleListClassifications = async () => {
    const { companyId, shareholder } = this.state
    const currentClassificationId = shareholder.shareholder.classificationId
    try {
      const response = await getClassificationsList({ companyId })

      const formatClassifications = response.map((item) => ({
        label: item.description,
        value: item.classificationId,
      }))

      const getCurrentClassification = formatClassifications.filter((classification) => {
        if (currentClassificationId === classification.value) {
          return classification
        }
        return null
      })

      this.setState({
        classificationsList: formatClassifications,
        classificationSelectedValue: getCurrentClassification,
      })
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
    }
  }

  handleNewClassification = async () => {
    const { companyId, classificationInputValue } = this.state
    try {
      this.setState({
        classificationLoading: true,
      })

      const response = await postCreateClassification({ companyId, description: classificationInputValue })

      await this.handleVinculateClassification(response.classificationId)
      await this.getInstitutionTearSheet()
      await this.handleListClassifications()

      return response
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
    } finally {
      this.setState({
        classificationLoading: false,
      })
    }
  }

  handleVinculateClassification = async (classificationId) => {
    const { companyId, shareholder } = this.state
    const shareholderId = shareholder.shareholder.shareholderId

    if (!classificationId) return
    try {
      this.setState({
        classificationLoading: true,
      })

      const response = await patchVinculateClassification({ companyId, shareholderId, classificationId })

      this.props.createToast({
        title: i18n.t('shareholderOverview.classificationToasts.vinculate.success.title'),
        description: i18n.t('shareholderOverview.classificationToasts.vinculate.success.description'),
        duration: 9000,
        type: 'success',
      })

      this.getInstitutionTearSheet()

      return response
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
    } finally {
      this.setState({
        classificationLoading: false,
      })
    }
  }

  handleUnvinculateClassification = async () => {
    const { companyId, shareholder } = this.state
    const shareholderId = shareholder.shareholder.shareholderId
    try {
      const response = await deleteUnvinculateClassification({ companyId, shareholderId })

      this.getInstitutionTearSheet()
      this.handleUnvinculateModal()

      this.setState({
        classificationSelectedValue: null,
      })

      this.props.createToast({
        title: i18n.t('shareholderOverview.classificationToasts.unvinculate.success.title'),
        description: i18n.t('shareholderOverview.classificationToasts.unvinculate.success.description'),
        duration: 9000,
        type: 'success',
      })

      return response
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
    }
  }

  handleUnvinculateModal = () => {
    this.setState((prevState) => ({
      unvinculateClassificationModal: !prevState.unvinculateClassificationModal,
    }))
  }

  handleCreateAndVinculateClassificationModal = () => {
    this.setState((prevState) => ({
      confirmCreateAndVinculateClassificationModal: !prevState.confirmCreateAndVinculateClassificationModal,
    }))
  }

  handleClassificationInput = (value) => {
    this.setState({
      classificationInputValue: value,
    })
  }

  handleClassificationDropdown = (value) => {
    const { classificationSelectedValue } = this.state
    if (classificationSelectedValue === value) return

    this.setState(
      {
        classificationSelectedValue: value,
      },
      () => {
        this.handleVinculateClassification(value?.value)
        this.getInstitutionTearSheet()
        this.handleCloseClassificationDropdown()
      }
    )
  }

  handleCloseClassificationDropdown = () => {
    this.setState({
      classificationCloseDropdown: true,
    })
  }

  render() {
    if (this.state.isLoading || !this.state.shareholder || !this.state.currentTicker) {
      return this.renderLoading()
    }

    const {
      shareholder,
      name,
      originalName,
      shareholderGroupId,
      shareholderDocType,
      groupList,
      tickers,
      currentTicker,
      chartData,
      chartStartDate,
      chartEndDate,
      document,
      currentTab,
      isFundsError,
      isLoadingFunds,
      isNameModalOpen,
      datesAvailable,
      confirmRemoveFundModal,
      summaryHistoric,
      loadingSummaryHistoric,
      summaryPeriod,
      loadingSummaryPeriod,
      typeView,
      classificationsList,
      classificationCloseDropdown,
      classificationSelectedValue,
      classificationInputValue,
      classificationLoading,
      unvinculateClassificationModal,
      confirmCreateAndVinculateClassificationModal,
    } = this.state

    const customClassificationDropdownStyle = {
      valueContainer: (provided, state) => ({
        ...provided,
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
      }),
    }

    const chartDataValidation = chartData && chartData.length > 0 && chartData[0].data.length > 0

    return (
      <div className="funds-wrapper tearsheet-container">
        <div id="divToPrint" className={defineTheme('funds-content private-tearsheet')}>
          <div className="basic-header-content dark private-tearsheet-header">
            <input
              autoComplete="false"
              className={
                shareholder.shareholderGroup && shareholder.shareholderGroup.name ? 'title double-title' : 'title'
              }
              onChange={(e) => this.inputChangedHandler(e)}
              onBlur={() => this.nameChangedHandler()}
              value={name}
              maxLength="200"
            />

            <ul className="more-info" style={{ fontSize: '12px' }}>
              <li>
                <span className="mini-title">{i18n.t('shareholderType')}</span>
                {shareholderDocType === 1 ? (
                  <span className="value">{i18n.t('shareholderTypeFund')}</span>
                ) : shareholderDocType === 2 ? (
                  <span className="value">{i18n.t('pageGroupedTearSheet.shareholderTypeIndividual')}</span>
                ) : (
                  <span className="value">{i18n.t('unknown')}</span>
                )}
              </li>
              <li>
                <span className="mini-title">{i18n.t('document')}</span>
                <span className="value">{this.constructor.cpfCnpj(document)}</span>

                <span className="document__WarningBtn">
                  !
                  <span className="document__WarningContent">
                    <span>{i18n.t('statusError.invalidDocuments')}</span>
                  </span>
                </span>
              </li>
              <li>
                <span className="mini-title">{i18n.t('managedBy')}</span>
                {shareholder.shareholderGroup.name !== null ? (
                  <div style={{ display: 'flex' }}>
                    <Tooltip
                      $text={shareholder.shareholderGroup.name}
                      $width="fit-content"
                      $alignment={ALIGNMENTS.BOTTOM_CENTER}
                    >
                      <Link to={`${PATH}/ownership/${shareholderGroupId}/grouped/overview`} className="group_name">
                        {shareholder.shareholderGroup.name}
                      </Link>
                    </Tooltip>

                    <Tooltip text={i18n.t('ungroup')} $width="auto">
                      <Icons.OutlineLayersClear
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          this.openRemoveFund()
                        }}
                        size={20}
                      />
                    </Tooltip>
                  </div>
                ) : (
                  <Tooltip text={i18n.t('toGroup')} $width="auto">
                    <Icons.OutlineLayers
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        this.onOpenAgglutinationModal()
                      }}
                      size={20}
                    />
                  </Tooltip>
                )}
              </li>
              <li>
                <span className="mini-title">{i18n.t('monitoring.classification')}</span>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: '7px',
                    width: '147px',
                    height: '22px',
                  }}
                >
                  <NewOptionDropdown
                    styles={customClassificationDropdownStyle}
                    value={classificationSelectedValue}
                    className={'tearsheet-classifications-dropdown'}
                    options={classificationsList}
                    closeMenuOnSelect={classificationCloseDropdown}
                    newOptionLabel={i18n.t('shareholderOverview.newClassification')}
                    handleNewOption={this.handleNewClassification}
                    inputValue={classificationInputValue}
                    handleInputChange={this.handleClassificationInput}
                    handleChange={this.handleClassificationDropdown}
                    loading={classificationLoading}
                    disabled={classificationLoading}
                  />
                  {shareholder.shareholder.classificationId && (
                    <Tooltip text={i18n.t('shareholderOverview.unvinculate')} $width="auto">
                      <Icons.OutlineLayersClear
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          this.handleUnvinculateModal()
                        }}
                        size={20}
                      />
                    </Tooltip>
                  )}
                </div>
              </li>
              {currentTab === 0 && (
                <>
                  <li>
                    <span className="mini-title">{i18n.t('stockType')}</span>
                    <Dropdown
                      options={tickers}
                      onChange={this.onChangeStockType}
                      selected={currentTicker || tickers[0]}
                    />
                  </li>
                  <Datepicker
                    language={i18n.language}
                    label={i18n.t('pageGroupedTearSheet.startDate')}
                    selected={chartStartDate}
                    onChange={(date) => this.onChangeStartDate(date)}
                    $availableDates={
                      chartEndDate ? datesAvailable.filter((date) => date <= chartEndDate) : datesAvailable
                    }
                  />

                  <Datepicker
                    language={i18n.language}
                    label={i18n.t('pageGroupedTearSheet.endDate')}
                    selected={chartEndDate}
                    onChange={(date) => this.onChangeEndDate(date)}
                    $availableDates={
                      chartStartDate ? datesAvailable.filter((date) => date >= chartStartDate) : datesAvailable
                    }
                  />
                </>
              )}
            </ul>
          </div>
          <div className="tearsheet-container__HeaderTabs">
            <ul className="list-tabs">
              <li
                className={currentTab === 0 ? 'tearsheet-container__tab active' : 'tearsheet-container__tab'}
                onClick={() => this.handleTab(0)}
                onKeyDown={() => this.handleTab(0)}
                role="menuitem"
              >
                {i18n.t('positionHistory')}
              </li>
            </ul>
          </div>
          {currentTab === 0 ? (
            <div className="tearsheet-container__content private">
              <div className="tearsheet-container__ContentItem private">
                {!loadingSummaryHistoric && !isLoadingFunds && !loadingSummaryPeriod ? (
                  <>
                    <div id="shareholderOverviewSummary" className="basic-filter-content dark shareholderOverview">
                      <div className="filters">
                        <div className="content">
                          {chartDataValidation && (
                            <>
                              <HistorySummaryListContent title={i18n.t('tearsheetHistory')}>
                                <HistorySummaryListItem
                                  legend={i18n.t('tearsheetMinimum')}
                                  icon={IMAGES.MINIMUM_ICON}
                                  value={
                                    summaryHistoric.minimum === null ? '--' : this.formatted(summaryHistoric.minimum)
                                  }
                                  date={
                                    summaryHistoric.minimumDate === null
                                      ? '--'
                                      : moment.utc(summaryHistoric.minimumDate).format(i18n.t('dateFormat'))
                                  }
                                />
                                <HistorySummaryListItem
                                  legend={i18n.t('tearsheetMaximum')}
                                  icon={IMAGES.MAXIMUM_ICON}
                                  value={
                                    summaryHistoric.maximum === null ? '--' : this.formatted(summaryHistoric.maximum)
                                  }
                                  date={
                                    summaryHistoric.maximumDate === null
                                      ? '--'
                                      : moment.utc(summaryHistoric.maximumDate).format(i18n.t('dateFormat'))
                                  }
                                />
                                <HistorySummaryListItem
                                  legend={i18n.t('tearsheetFirstPurchase')}
                                  icon={IMAGES.PURCHASE_ICON}
                                  value={
                                    summaryHistoric.firstPurchase === undefined
                                      ? '--'
                                      : moment.utc(summaryHistoric.firstPurchase).format(i18n.t('dateFormat'))
                                  }
                                />
                                <HistorySummaryListItem
                                  legend={i18n.t('tearsheetLastSale')}
                                  icon={IMAGES.SALE_ICON}
                                  value={
                                    summaryHistoric.lastSale === null
                                      ? '--'
                                      : moment.utc(summaryHistoric.lastSale).format(i18n.t('dateFormat'))
                                  }
                                />
                              </HistorySummaryListContent>
                              <HistorySummaryListContent title={i18n.t('tearsheetPeriod')}>
                                <HistorySummaryListItem
                                  legend={i18n.t('tearsheetMinimum')}
                                  icon={IMAGES.MINIMUM_ICON}
                                  value={summaryPeriod.minimum === null ? '--' : this.formatted(summaryPeriod.minimum)}
                                  date={
                                    summaryPeriod.minimumDate === null
                                      ? '--'
                                      : moment.utc(summaryPeriod.minimumDate).format(i18n.t('dateFormat'))
                                  }
                                />
                                <HistorySummaryListItem
                                  legend={i18n.t('tearsheetMaximum')}
                                  icon={IMAGES.MAXIMUM_ICON}
                                  value={summaryPeriod.maximum === null ? '--' : this.formatted(summaryPeriod.maximum)}
                                  date={
                                    summaryPeriod.maximumDate === null
                                      ? '--'
                                      : moment.utc(summaryPeriod.maximumDate).format(i18n.t('dateFormat'))
                                  }
                                />
                                <HistorySummaryListItem
                                  className={
                                    Number(summaryPeriod.variation) > 0
                                      ? 'green'
                                      : Number(summaryPeriod.variation) < 0
                                        ? 'red'
                                        : ''
                                  }
                                  legend={i18n.t('tearsheetVariation')}
                                  icon={
                                    Number(summaryPeriod.variation) > 0
                                      ? IMAGES.VARIATIONUP_ICON
                                      : Number(summaryPeriod.variation) < 0
                                        ? IMAGES.VARIATIONDOWN_ICON
                                        : IMAGES.VARIATIONNONE_ICON
                                  }
                                  value={
                                    summaryPeriod.variation === null ? '--' : this.formatted(summaryPeriod.variation)
                                  }
                                  variation={
                                    summaryPeriod.variationPct === null
                                      ? '--'
                                      : `(${this.formatted(summaryPeriod.variationPct)}%)`
                                  }
                                />
                                <HistorySummaryListItem
                                  legend={i18n.t('tearsheetLastSale')}
                                  icon={IMAGES.SALE_ICON}
                                  value={
                                    summaryPeriod.lastSale === null
                                      ? '--'
                                      : moment.utc(summaryPeriod.lastSale).format(i18n.t('dateFormat'))
                                  }
                                />
                              </HistorySummaryListContent>
                              <HistorySummaryListContent title={i18n.t('pageGroupedTearSheet.viewType')}>
                                <Dropdown
                                  onChange={this.onChangeTypeView}
                                  options={ContentViewType}
                                  selected={typeView}
                                />
                              </HistorySummaryListContent>
                              <HistorySummaryListContent title={i18n.t('pageGroupedTearSheet.export')}>
                                <Icons.Download
                                  style={{ cursor: 'pointer', display: 'block', margin: '0 auto' }}
                                  onClick={() => {
                                    this.setState({ showExportModal: true })
                                  }}
                                  size={20}
                                />
                              </HistorySummaryListContent>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="content-graph">
                      {this.state.mustRender ? (
                        chartStartDate === undefined || chartEndDate === undefined ? (
                          <p className="content-text" style={{ marginTop: '200px' }}>
                            {i18n.t('pageGroupedTearSheet.chartPlaceholderNoResults')}
                          </p>
                        ) : (
                          <p className="content-text" style={{ marginTop: '200px' }}>
                            {i18n.t('pageGroupedTearSheet.chartPlaceholder')}
                          </p>
                        )
                      ) : isFundsError ? (
                        <p className="content-text" style={{ marginTop: '200px' }}>
                          {i18n.t('globals.dataError')}
                        </p>
                      ) : (
                        <div>
                          {this.state.typeView.value === '1' ? (
                            this.state.chartData && this.state.chartData.length > 0 ? (
                              this.state.chartData[0].data.length > 0 ? (
                                <Charts.Line
                                  title={i18n.t('pageGroupedTearSheet.position')}
                                  legend={true}
                                  language={i18n.language}
                                  series={this.state.chartData}
                                  xAxis={{
                                    categories: this.state.chartCategories,
                                    tickInterval: Math.round(this.state.chartCategories.length / 20),
                                  }}
                                />
                              ) : (
                                <p className="content-text">{i18n.t('pageGroupedTearSheet.noPositoinHistoryFound')}</p>
                              )
                            ) : (
                              <p className="content-text">{i18n.t('pageGroupedTearSheet.chartPlaceholder')}</p>
                            )
                          ) : (
                            <div className="tearsheet-container__listContent tearsheet-container__listContent--tearsheetOverview">
                              <div className="tearsheet-container__listContent-header">
                                <ul className="list">
                                  <li className="list__item list__item-header tearsheet-container__listContent-listItem">
                                    <span className="title">{i18n.t('shareholderOverview.date')}</span>
                                    <span className="title">{i18n.t('shareholderOverview.initialPosition')}</span>
                                    <span className="title">{i18n.t('shareholderOverview.variation')}</span>
                                    <span className="title">{i18n.t('shareholderOverview.finalPosition')}</span>
                                    <span className="title">{i18n.t('shareholderOverview.chartPosition')} (%)</span>
                                    <span className="title">{i18n.t('shareholderOverview.patrimony')}</span>
                                    <span className="title">{`${i18n.t('shareholderOverview.averagePrice')}*`}</span>
                                    <span className="title">{i18n.t('shareholderOverview.closingPrice')}</span>
                                    <span className="title">{i18n.t('shareholderOverview.lowPrice')}</span>
                                    <span className="title">{i18n.t('shareholderOverview.highPrice')}</span>
                                    <span className="title">{i18n.t('shareholderOverview.minMovement')}</span>
                                    <span className="title">{i18n.t('shareholderOverview.maxMovement')}</span>
                                  </li>
                                </ul>
                              </div>
                              <ul className="list list-group scroll full-width">
                                {this.state.tableData.map((item) => {
                                  return (
                                    <li key={item.initialStocks} className="list__item">
                                      <span>{moment.utc(item.referenceDate).format(i18n.t('dateFormat'))}</span>
                                      <span>{this.formatted(item.initialStocks)}</span>
                                      <span>{this.formatted(item.deltaStocks)}</span>
                                      <span>{this.formatted(item.finalStocks)}</span>
                                      <span>
                                        {parseFloat(item.stocksPercentage) <= 0.01 &&
                                        parseFloat(item.stocksPercentage) > 0
                                          ? `< ${this.formatted('00.01')}%`
                                          : `${this.formatRepresentativeness(item.stocksPercentage)}%`}
                                      </span>
                                      <span>{this.formatted(item.patrimony)}</span>
                                      <span>
                                        {item.averagePrice
                                          ? `R$ ${this.formatRepresentativeness(item.averagePrice)}`
                                          : 'R$ 0,00'}
                                      </span>
                                      <span>
                                        {item.closingPrice
                                          ? `R$ ${this.formatRepresentativeness(item.closingPrice)}`
                                          : 'R$ 0,00'}
                                      </span>
                                      <span>
                                        {item.lowPrice
                                          ? `R$ ${this.formatRepresentativeness(item.lowPrice)}`
                                          : 'R$ 0,00'}
                                      </span>
                                      <span>
                                        {item.highPrice
                                          ? `R$ ${this.formatRepresentativeness(item.highPrice)}`
                                          : 'R$ 0,00'}
                                      </span>
                                      <span>{`R$ ${this.formatRepresentativeness(item.minimumMovement)}`}</span>
                                      <span>{`R$ ${this.formatRepresentativeness(item.maximumMovement)}`}</span>
                                    </li>
                                  )
                                })}
                              </ul>
                              <p className="disclaimer">{`* ${i18n.t('shareholderOverview.closingPriceDisclaimer')}`}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <Loading />
                )}
              </div>
            </div>
          ) : null}
        </div>

        <ConfirmationModal
          show={confirmCreateAndVinculateClassificationModal}
          title={i18n.t('shareholderOverview.unvinculateModalTitle')}
          message={i18n.t('shareholderOverview.unvinculateModalMessage')}
          onConfirm={this.handleNewClassification}
          onClose={this.handleCreateAndVinculateClassificationModal}
          cancelButtonLabel={i18n.t('components.confirmModal.cancelButton')}
          confirmButtonLabel={i18n.t('components.confirmModal.confirmButton')}
        />

        <DeleteModal
          show={confirmRemoveFundModal}
          message={i18n.t('pageGroupedTearSheet.removeTaskConfirmation')}
          onDelete={this.onUngroup}
          onClose={this.closeRemoveFund}
          cancelButtonLabel={i18n.t('components.deleteModal.cancelButton')}
          deleteButtonLabel={i18n.t('components.deleteModal.deleteButton')}
        />
        <DeleteModal
          show={unvinculateClassificationModal}
          message={i18n.t('shareholderOverview.unvinculateModalMessage')}
          onDelete={this.handleUnvinculateClassification}
          onClose={this.handleUnvinculateModal}
          cancelButtonLabel={i18n.t('components.deleteModal.cancelButton')}
          deleteButtonLabel={i18n.t('components.deleteModal.deleteButton')}
        />

        <ConfirmationModal
          show={isNameModalOpen}
          title={i18n.t('exportHistory.sendAlert.confirmTitle')}
          message={`${i18n.t('changeName')} ${originalName} ${i18n.t('to')} ${name}. ${i18n.t('confirmChanges')}`}
          onConfirm={this.submitNameModal}
          onClose={this.closeNameModal}
          cancelButtonLabel={i18n.t('components.confirmModal.cancelButton')}
          confirmButtonLabel={i18n.t('components.confirmModal.confirmButton')}
        />

        <TearsheetModal
          show={this.state.showExportModal}
          onClose={() => this.setState({ showExportModal: false })}
          onExportSelectedPeriod={() => this.onExportPosition(chartStartDate, chartEndDate)}
          onExportHistoricPosition={this.onExportHistoricPosition}
        />

        <GroupModal
          title={`${i18n.t('ownership.agroup')} ${this.handleGroupModalTitleShareholderType()}`}
          visibility={this.state.showAggModal}
          options={groupList}
          onClose={this.closeAggModal}
          onCreateGroup={this.onCreateGroup}
          onConfirm={this.onVinculateGroup}
        />
      </div>
    )
  }
}

const SimpleOverview = hocWrapper(BaseSimpleOverview)

export { SimpleOverview }
