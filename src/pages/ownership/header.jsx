import React, { Component } from 'react'
import { i18n } from 'translate'
import { <PERSON><PERSON>, But<PERSON>, Dropdown, NewDatepicker } from '@mz-codes/design-system'
import { groupedOptions, baseViewOwnership } from 'utils'

const visionType = { simple: '0', grouped: '1', albert: '2', factSet: '3' }

export default class OwnershipHeader extends Component {
  constructor(props) {
    super(props)
    this.state = {
      stockType: null,
      baseMode: null,
    }
  }

  componentDidMount() {
    const baseMode = baseViewOwnership[1]
    this.setState({ baseMode })
  }

  onChangeBaseMode = (value) => {
    const { baseMode } = this.state
    if (baseMode.value === value.value) {
      return
    }

    this.setState({ baseMode: value })
    this.props.onChangeBaseMode(value.value)
  }

  onChangeStockType = (value) => {
    const { tickers } = this.props

    const newTicker = tickers.filter((ticker) => {
      if (ticker.label === value.label) {
        return ticker
      }
      return null
    })
    this.setState({ stockType: value })

    this.props.onChangeStockType(newTicker[0])
  }

  onChangeTopQuantity = (topQuantity) => {
    this.props.onChangeTopQuantity(topQuantity)
  }

  onChangeShareholderType = (value) => {
    this.props.onChangeShareholderType(value)
  }

  onChangeEndDate = (dt) => {
    this.props.onChangeEndDate(dt)
  }

  onExportBase = (e) => {
    e?.preventDefault()
    this.props.onExportBase(e)
  }

  onChangeGroupedType = (value) => {
    this.props.onChangeGroupedType(value)
  }

  render() {
    const {
      tickers,
      currentDate = today,
      datesAvailable,
      currentShareholderType,
      currentGroupedType,
      vision,
      currentClassification,
      allClassifications,
      topQuantity,
      selectedTopQuantity,
      currentPortfolio,
      portfolios,
      hasIntelligenceProductActive,
      isLoading = false,
    } = this.props
    const { stockType, baseMode } = this.state

    const today = new Date()

    const shareholderTypeOptions = i18n.t('ownership._dropdownOptions.shareholderTypes', {
      returnObjects: true,
    })

    const visionOptions = hasIntelligenceProductActive
      ? baseViewOwnership
      : baseViewOwnership.filter((view) => view.value !== '3')

    const disableHeaderOptions = !tickers || !datesAvailable?.length || !baseMode || !currentDate

    return (
      <Header>
        <Header.Content style={{ alignItems: 'flex-end' }}>
          <Header.Item>
            <Header.Label>{i18n.t('ownership.stockType')}</Header.Label>
            <Dropdown
              disabled={isLoading}
              options={tickers}
              minWidth={150}
              onChange={this.onChangeStockType}
              selected={stockType || tickers[0]}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{i18n.t('ownership.view')}</Header.Label>
            <Dropdown
              minWidth={150}
              disabled={isLoading || disableHeaderOptions}
              options={visionOptions}
              onChange={this.onChangeBaseMode}
              selected={baseMode || visionOptions[1]}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{i18n.t('ownership.quantity')}</Header.Label>
            <Dropdown
              minWidth={150}
              disabled={isLoading || disableHeaderOptions}
              options={topQuantity}
              onChange={this.onChangeTopQuantity}
              selected={selectedTopQuantity}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{i18n.t('ownership.date')}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              blocked={isLoading || disableHeaderOptions}
              selected={currentDate}
              onChange={(date) => this.onChangeEndDate(date)}
              availableDates={datesAvailable}
              hint={i18n.t('globals.referenceDate')}
            />
          </Header.Item>
          {this.props.grouped ? null : this.state.baseMode.value !== '0' ? null : (
            <Header.Item>
              <Header.Label>{i18n.t('ownership.shareholderType')}</Header.Label>
              <Dropdown
                disabled={isLoading || disableHeaderOptions}
                options={shareholderTypeOptions}
                onChange={this.onChangeShareholderType}
                selected={currentShareholderType || shareholderTypeOptions[0]}
              />
            </Header.Item>
          )}
          {(this.state.baseMode && this.state.baseMode.value === '2') || vision === visionType.factSet ? null : (
            <Header.Item>
              <Header.Label>{i18n.t('ownership.grouping')}</Header.Label>
              <Dropdown
                disabled={isLoading || disableHeaderOptions}
                options={groupedOptions}
                onChange={this.onChangeGroupedType}
                selected={currentGroupedType || groupedOptions[0]}
              />
            </Header.Item>
          )}

          {vision === visionType.factSet && currentPortfolio && (
            <Header.Item>
              <Header.Label>{i18n.t('portfolio')}</Header.Label>
              <Dropdown
                disabled={isLoading || disableHeaderOptions}
                options={portfolios}
                onChange={this.props.onChangePortfolio}
                selected={currentPortfolio}
              />
            </Header.Item>
          )}

          {vision === visionType.simple && (
            <Header.Item>
              <Header.Label>{i18n.t('ownership.classification')}</Header.Label>
              <Dropdown
                minWidth={150}
                height={150}
                labelMaxLen={20}
                disabled={isLoading || disableHeaderOptions}
                options={allClassifications}
                onChange={this.props.onChangeClassification}
                selected={currentClassification || allClassifications[0]}
              />
            </Header.Item>
          )}

          <Header.Item $alignRight>
            <Header.Search
              disabled={isLoading || disableHeaderOptions}
              type="text"
              placeholder={i18n.t('ownership.search')}
              onChange={this.props.handleSearchValueChange}
              value={this.props.searchTerm}
            />
          </Header.Item>
        </Header.Content>

        <Header.ButtonGroup style={{ marginLeft: 0 }}>
          <Buttons.Primary disabled={isLoading} onClick={() => this.props.onOpenUploaderModal()}>
            {i18n.t('ownership.add')}
          </Buttons.Primary>
          <Buttons.Export disabled={isLoading || disableHeaderOptions} onClick={this.onExportBase}>
            {i18n.t('ownership.export')}
          </Buttons.Export>
        </Header.ButtonGroup>
      </Header>
    )
  }
}
