import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { PATH } from 'consts'
import ReactTooltip from 'react-tooltip'
import { Loading } from '@mz-codes/design-system'

import { i18n } from 'translate'
import { defineTheme, formatVolumePrice, generateUniqueId } from 'utils'

class SimpleBaseList extends Component {
  constructor(props) {
    super(props)
    this.state = {
      orderedBy: 'StocksLastDateValue',
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.props.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  renderNoResultsFound = () => {
    return <li className="no-results">{i18n.t('ownership.noResultsFoundOnYourBase')}</li>
  }

  openClosePriceModal = () => {
    this.props.openClosePriceModal()
  }

  onSortClickHandler = () => {
    this.props.onSortClickHandler()
  }

  renderClassName = () => {
    let className = `header-title ${this.state.orderedBy.toLowerCase()}`
    if (this.props.currentOrder === -1) {
      className += '-desc'
    } else {
      className += '-asc'
    }
    return className
  }

  render() {
    const { isLoading, currentBase } = this.props

    const noResults = !isLoading && !currentBase.length

    return (
      <div className="base-content">
        <ul className={defineTheme('base-list shareholder-base-list simple')}>
          <li className={this.renderClassName()}>
            <span className="name sticky">{i18n.t('ownership.name')}</span>
            <span className="shareholder-type">{i18n.t('ownership.shareholderType')}</span>
            <span className="group">{i18n.t('ownership.shareholderGroup')}</span>
            <span>{i18n.t('ownership.classification')}</span>
            <span className="country">{i18n.t('ownership.country')}</span>
            <span className={this.props.showVolume ? 'volume-last-value' : 'volume-last-value price'}>
              <span
                onClick={() => this.onSortClickHandler()}
                onKeyDown={() => this.onSortClickHandler()}
                tabIndex={0}
                role="button"
              >
                {this.props.isDaily
                  ? this.props.showVolume
                    ? i18n.t('ownership.volume')
                    : i18n.t('ownership.priceVolume')
                  : this.props.showVolume
                    ? i18n.t('ownership.finalVolume')
                    : i18n.t('ownership.finalPriceVolume')}{' '}
                <i className="order-by" />
              </span>{' '}
            </span>

            <span className="value">{i18n.t('ownership.value')}</span>
          </li>

          {isLoading && <Loading />}
          {noResults && this.renderNoResultsFound()}

          {currentBase.map((item) => {
            const key = generateUniqueId()
            const percentage = (item.stockAmountEdited * 100) / (this.props.currentBatchInfo.totalStocks || 1)
            const hasGroup = item.alreadyGrouped && item.shareholderGroupName
            const showClassificationTitle =
              item.classification && item.classification.length > 15 ? item.classification : undefined
            return (
              <li key={key}>
                <span className="open-drill-dawn sticky">
                  {!item.alreadyGrouped && (
                    <i
                      data-tip={i18n.t('ownership.agroup')}
                      onClick={(e) => this.props.onOpenAgglutinationModal(e, item)}
                      onKeyDown={(e) => this.props.onOpenAgglutinationModal(e, item)}
                      tabIndex={-1}
                      role="button"
                      aria-label="openDrillDawn"
                    />
                  )}
                </span>
                <span className="name sticky">
                  <Link
                    to={`${PATH}/ownership/${item.shareholderId}/simple/overview`}
                    data-tip={item.displayName || item.name}
                  >
                    {item.displayName || item.name}
                  </Link>
                </span>
                <span className="shareholder-type">
                  {item.shareholderType === 1 && i18n.t('ownership.fund')}
                  {item.shareholderType !== 1 && i18n.t('ownership.individual')}
                </span>
                <span className="group">
                  {hasGroup && (
                    <Link to={`${PATH}/ownership/${item.shareholderGroupId}/grouped/overview`}>
                      {item.shareholderGroupName}
                    </Link>
                  )}
                  {!hasGroup && '--'}
                </span>
                <span title={showClassificationTitle}>{item.classification || '--'}</span>
                <span className="country">{item.countryCode || '--'}</span>
                <span className="volume-last-value is-edited">
                  {this.formatted(item.stockAmountEdited)}{' '}
                  {this.props.currentBatchInfo && `(${percentage.toFixed(2)}%)`}
                  {item.updatedBy && <i data-tip={i18n.t('ownership.editedBy')}>E</i>}
                </span>

                {this.props.closingPrice && (
                  <span
                    className="value"
                    onClick={this.openClosePriceModal}
                    onKeyDown={this.openClosePriceModal}
                    tabIndex={-3}
                    role="button"
                  >
                    {formatVolumePrice(this.props.closingPrice.closingPrice, item.stockAmountEdited, this.props.idiom)}
                  </span>
                )}

                {!this.props.closingPrice && (
                  <span
                    onClick={this.openClosePriceModal}
                    onKeyDown={this.openClosePriceModal}
                    tabIndex={-4}
                    role="button"
                  >
                    <span className="value">{i18n.t('ownership.configure')}</span>
                  </span>
                )}
              </li>
            )
          })}

          {!!currentBase.length && <ReactTooltip place="top" delayShow={500} />}
        </ul>
      </div>
    )
  }
}

export default SimpleBaseList
