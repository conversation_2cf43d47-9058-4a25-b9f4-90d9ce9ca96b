import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { PATH } from 'consts'
import { Collapse } from 'react-collapse'
import ReactTooltip from 'react-tooltip'

import { i18n } from 'translate'
import { getCurrentShareholderAlbertChildPosition } from 'client'
import { defineTheme, formatVolumePrice, generateUniqueId } from 'utils'

class AlbertBaseList extends Component {
  constructor(props) {
    super(props)
    this.state = {
      childItems: [],
      expandedItems: [],
      orderedBy: 'StocksLastDateValue',
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.referenceDate !== this.props.referenceDate) {
      this.setState({ childItems: [], expandedItems: [] })
    }
  }

  static renderNoResultsFound = () => {
    return <li className="no-results">{i18n.t('ownership.noResultsFoundOnYourBase')}</li>
  }

  onShowDetail = (id) => {
    const { childItems, expandedItems } = this.state
    const index = expandedItems.indexOf(id)

    if (index === -1) {
      expandedItems.push(id)
    } else {
      expandedItems.splice(index, 1)
    }

    if (childItems[id]) {
      this.setState({ expandedItems })
    } else {
      getCurrentShareholderAlbertChildPosition(
        this.props.companyId,
        this.props.tickerId,
        this.props.referenceDate,
        id
      ).then((res) => {
        childItems[id] = res.data.positions
        this.setState({ expandedItems, childItems })
      })
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.props.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  openClosePriceModal = () => {
    this.props.openClosePriceModal()
  }

  onSortClickHandler = () => {
    this.props.onSortClickHandler()
  }

  renderClassName = () => {
    let className = `header-title ${this.state.orderedBy.toLowerCase()}`
    if (this.props.currentOrder === -1) {
      className += '-desc'
    } else {
      className += '-asc'
    }
    return className
  }

  render() {
    const albert = 88
    const simple = 1

    return (
      <div className="base-content">
        <ul className={defineTheme('base-list shareholder-base-list grouped')}>
          <li className={this.renderClassName()}>
            <span className="name sticky">{i18n.t('ownership.name')}</span>
            <span className="shareholder-type">{i18n.t('ownership.shareholderType')}</span>
            <span className="country">{i18n.t('ownership.country')}</span>
            <span className={this.props.showVolume ? 'volume-last-value' : 'volume-last-value price'}>
              <span
                onClick={() => this.onSortClickHandler()}
                onKeyDown={() => this.openClosePriceModal()}
                role="button"
                tabIndex={0}
              >
                {this.props.isDaily
                  ? this.props.showVolume
                    ? i18n.t('ownership.volume')
                    : i18n.t('ownership.priceVolume')
                  : this.props.showVolume
                    ? i18n.t('ownership.finalVolume')
                    : i18n.t('ownership.finalPriceVolume')}{' '}
                <i className="order-by" />
              </span>{' '}
            </span>
            <span className="value">{i18n.t('ownership.value')}</span>
          </li>
          {!this.props.isLoading ? (
            <>
              {this.props.currentBase.length === 0
                ? AlbertBaseList.renderNoResultsFound()
                : this.props.currentBase.map((item) => {
                    const key = generateUniqueId()
                    return (
                      <li key={key}>
                        {item.isExpandable ? (
                          <span className="open-drill-dawn sticky">
                            <span
                              className={this.state.expandedItems.includes(item.shareholderGroupId) ? 'close' : 'open'}
                              onClick={() => this.onShowDetail(item.shareholderGroupId)}
                              onKeyDown={() => this.onShowDetail(item.shareholderGroupId)}
                              role="button"
                              tabIndex={-1}
                            >
                              <span />
                            </span>
                          </span>
                        ) : (
                          <span className="open-drill-dawn sticky" />
                        )}
                        <span className="name sticky">
                          {item.shareholderType === albert ? (
                            <Link
                              to={`${PATH}/ownership/${item.shareholderGroupId}/albert/overview`}
                              data-tip={item.name}
                              data-for="albert"
                            >
                              {item.name}
                            </Link>
                          ) : (
                            <Link
                              to={`${PATH}/ownership/${item.shareholderId}/simple/overview`}
                              data-tip={item.name}
                              data-for="albert"
                            >
                              {item.name}
                            </Link>
                          )}
                        </span>
                        <span className="shareholder-type">
                          {item.shareholderType === albert
                            ? 'Beta'
                            : item.shareholderType === simple
                              ? i18n.t('ownership.fund')
                              : i18n.t('ownership.individual')}
                        </span>
                        <span className="country">{item.shareholderType === albert ? '--' : item.countryCode}</span>
                        <span className="volume-last-value is-edited">
                          {`${this.formatted(item.stockAmountEdited)} (${(
                            (item.stockAmountEdited * 100) /
                            this.props.currentBatchInfo.totalStocks
                          ).toFixed(2)}%)`}
                        </span>
                        {this.props.closingPrice ? (
                          <span
                            onClick={() => this.openClosePriceModal()}
                            onKeyDown={() => this.openClosePriceModal()}
                            role="button"
                            tabIndex={-2}
                          >
                            <span className="value">
                              {formatVolumePrice(
                                this.props.closingPrice.closingPrice,
                                item.stockAmountEdited,
                                this.props.idiom
                              )}
                            </span>
                          </span>
                        ) : (
                          <span
                            onClick={() => this.openClosePriceModal()}
                            onKeyDown={() => this.openClosePriceModal()}
                            role="button"
                            tabIndex={-3}
                          >
                            <span className="value">{i18n.t('ownership.configure')}</span>
                          </span>
                        )}

                        <div
                          className={`history-detail ${
                            this.state.expandedItems.includes(item.shareholderGroupId) ? 'open' : 'close'
                          }`}
                        >
                          <Collapse
                            isOpened={this.state.expandedItems.includes(item.shareholderGroupId)}
                            className="history-detail"
                          >
                            <ul>
                              {this.state.childItems[item.shareholderGroupId] ? (
                                <>
                                  {this.state.childItems[item.shareholderGroupId].map((subItem) => {
                                    const keyId = generateUniqueId()
                                    return (
                                      <li className="sub-item" key={keyId}>
                                        <span className="name sticky">
                                          <Link
                                            to={`${PATH}/ownership/${subItem.shareholderId}/simple/overview`}
                                            data-tip={subItem.displayName}
                                            data-for="albert-child"
                                          >
                                            {subItem.displayName}
                                          </Link>
                                        </span>
                                        <span className="shareholder-type">
                                          {subItem.shareholderType === simple
                                            ? i18n.t('ownership.fund')
                                            : i18n.t('ownership.individual')}
                                        </span>
                                        <span className="country">{subItem.countryCode}</span>
                                        <span className="volume-last-value">
                                          {`${this.formatted(subItem.stockAmountEdited)} (${(
                                            (subItem.stockAmountEdited * 100) /
                                            this.props.currentBatchInfo.totalStocks
                                          ).toFixed(2)}%)`}
                                        </span>
                                        {this.props.closingPrice ? (
                                          <span
                                            onClick={() => this.openClosePriceModal()}
                                            onKeyDown={() => this.openClosePriceModal()}
                                            role="button"
                                            tabIndex={-4}
                                          >
                                            <span className="value">
                                              R${' '}
                                              {this.formatted(
                                                this.props.closingPrice.closingPrice * subItem.stockAmountEdited
                                              )}
                                            </span>
                                          </span>
                                        ) : (
                                          <span
                                            onClick={() => this.openClosePriceModal()}
                                            onKeyDown={() => this.openClosePriceModal()}
                                            role="button"
                                            tabIndex={-5}
                                          >
                                            <span className="value">{i18n.t('ownership.configure')}</span>
                                          </span>
                                        )}
                                      </li>
                                    )
                                  })}
                                  <ReactTooltip place="top" delayShow={500} id="albert-child" />
                                </>
                              ) : null}
                            </ul>
                          </Collapse>
                        </div>
                      </li>
                    )
                  })}
              <ReactTooltip place="top" delayShow={500} id="albert" />
            </>
          ) : (
            <li className="list-loading">
              <div className="lds-dual-ring">
                <div />
              </div>
            </li>
          )}
        </ul>
      </div>
    )
  }
}

export default AlbertBaseList
