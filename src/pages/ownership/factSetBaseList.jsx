import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { PATH } from 'consts'
import { Collapse } from 'react-collapse'
import ReactTooltip from 'react-tooltip'

import { i18n } from 'translate'

import { getCurrentShareholderFactSetChildrenPosition } from 'client'
import { defineTheme, generateUniqueId } from 'utils'
import { IMAGES } from 'assets'
import { shareholdersGroupedFormater } from './formaters'

class FactSetBaseList extends Component {
  constructor(props) {
    super(props)
    this.state = {
      childItems: [],
      expandedItems: [],
      orderedBy: 'StocksLastDateValue',
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.referenceDate !== this.props.referenceDate) {
      this.setState({ childItems: [], expandedItems: [] })
    }
  }

  static renderNoResultsFound = () => {
    return <li className="no-results">{i18n.t('ownership.noResultsFoundOnYourBase')}</li>
  }

  onShowDetail = (id) => {
    const { childItems, expandedItems } = this.state
    const index = expandedItems.indexOf(id)

    if (index === -1) {
      expandedItems.push(id)
    } else {
      expandedItems.splice(index, 1)
    }

    if (childItems[id]) {
      this.setState({ expandedItems })
    } else {
      getCurrentShareholderFactSetChildrenPosition(
        this.props.companyId,
        this.props.tickerId,
        this.props.userId,
        this.props.portfolioId,
        id,
        this.props.referenceDate
      ).then((res) => {
        childItems[id] = res.data.positions
        this.setState({ expandedItems, childItems })
      })
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.props.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  renderClassName = () => {
    let className = `header-title factSet-header-title ${this.state.orderedBy.toLowerCase()}`
    if (this.props.currentOrder === -1) {
      className += '-desc'
    } else {
      className += '-asc'
    }
    return className
  }

  render() {
    const albert = 88
    const simple = 1

    const formatedBase = shareholdersGroupedFormater.format(this.props.currentBase, this.props.idiom)

    return (
      <div className="base-content">
        {!this.props.portfolioId && (
          <div className="portfolioError">
            <h2>{i18n.t('globals.errors.noPortfolioData.title')}</h2>
            <p>{i18n.t('globals.errors.noPortfolioData.message')}</p>
          </div>
        )}
        {this.props.portfolioId && (
          <ul className={defineTheme('base-list shareholder-base-list factSet')}>
            <li className={this.renderClassName()}>
              <span className="name sticky">{i18n.t('ownership.name')}</span>
              <span className="variation">(%)</span>
              <span className="positionValue">{i18n.t('ownership.position')}</span>
              <span className="mmValue">{i18n.t('ownership.valueMM')}</span>
              <span className="mm">
                {i18n.t('ownership.$MM')} <img src={IMAGES.INFORMATION_ICON} alt="icon" />
                <span className="tooltip">{i18n.t('ownership.$MMTooltip')}</span>
              </span>
              <span className="shareholder-type">{i18n.t('ownership.type')}</span>
              <span className="style">{i18n.t('ownership.style')}</span>
              <span className="style">T/O</span>
              <span className="city">{i18n.t('ownership.city')}</span>
              <span className="pairs">{i18n.t('ownership.peers')}</span>
            </li>
            {!this.props.isLoading ? (
              <>
                {formatedBase.length === 0
                  ? this.renderNoResultsFound()
                  : formatedBase.map((item) => {
                      const key = generateUniqueId()
                      return (
                        <li className="factSet-item-list" key={key}>
                          <span className="name">
                            {item.isExpandable && (
                              <span className="open-drill-dawn">
                                <span
                                  className={
                                    this.state.expandedItems.includes(item.shareholderGroupId) ? 'close' : 'open'
                                  }
                                  onClick={() => this.onShowDetail(item.shareholderGroupId)}
                                  onKeyDown={() => this.onShowDetail(item.shareholderGroupId)}
                                  role="button"
                                  tabIndex={0}
                                  aria-label="openDrillDawn"
                                />
                              </span>
                            )}
                            {item.shareholderType === albert ? (
                              <Link
                                to={`${PATH}/ownership/${item.shareholderGroupId}/albert/overview`}
                                data-tip={item.name.toUpperCase()}
                                data-for="albert"
                              >
                                {item.name}
                              </Link>
                            ) : (
                              <Link
                                to={`${PATH}/ownership/${item.shareholderId}/simple/overview`}
                                data-tip={item.name.toUpperCase()}
                                data-for="albert"
                              >
                                {item.name}
                              </Link>
                            )}
                          </span>
                          <span className="variation">{item.pct}</span>
                          <span className="positionValue">{item.stockAmountEdited}</span>
                          <span className="mmValue">{item.marketValue}</span>
                          <span className="mm">{item.totalAum}</span>
                          {item.institutionType !== '--' ? (
                            <span className="shareholder-type has-tooltip">
                              {item.institutionType}
                              <span className="tooltip">{item.institutionType}</span>
                            </span>
                          ) : (
                            <span className="shareholder-type">{item.institutionType}</span>
                          )}
                          <span className="shareholder-style">{item.institutionStyle}</span>
                          <span className="shareholder-turnOver">{item.turnover}</span>
                          <span className="city">{item.city}</span>
                          <span className="volume-last-value is-edited">{item.marketValuePeersBRL}</span>
                          <div
                            className={`history-detail ${
                              this.state.expandedItems.includes(item.shareholderGroupId) ? 'open' : 'close'
                            }`}
                          >
                            <Collapse
                              isOpened={this.state.expandedItems.includes(item.shareholderGroupId)}
                              className="history-detail"
                            >
                              <ul>
                                {this.state.childItems[item.shareholderGroupId] ? (
                                  <>
                                    {this.state.childItems[item.shareholderGroupId].map((subItem) => {
                                      const subKey = generateUniqueId()
                                      return (
                                        <li className="sub-item" key={subKey}>
                                          <span className="name sticky">
                                            <Link
                                              to={`${PATH}/ownership/${subItem.shareholderId}/simple/overview`}
                                              data-tip={subItem.displayName}
                                              data-for="albert-child"
                                            >
                                              {subItem.displayName}
                                            </Link>
                                          </span>
                                          <span className="percentage">
                                            {subItem.pct !== null || subItem.pct !== undefined ? subItem.pct : '--'}
                                          </span>
                                          <span className="position">{this.formatted(subItem.stockAmountEdited)}</span>
                                          <span className="mmValue">{this.formatted(subItem.marketValue)}</span>
                                          <span className="mm">
                                            {subItem.totalAum === undefined ? '--' : this.formatted(subItem.totalAum)}
                                          </span>
                                          <span className="type">
                                            {subItem.shareholderType === albert
                                              ? 'Beta'
                                              : subItem.shareholderType === simple
                                                ? i18n.t('ownership.fund')
                                                : i18n.t('ownership.individual')}
                                          </span>
                                          <span className="style">
                                            {subItem.fundStyle !== null ? subItem.fundStyle : '--'}
                                          </span>
                                          <span className="turnover">
                                            {subItem.turnover !== null ? subItem.turnover : '--'}
                                          </span>
                                          <span className="city">{subItem.city !== null ? subItem.city : '--'}</span>
                                          <span className="pairs">
                                            {subItem.positionInPeers !== null
                                              ? this.formatted(subItem.positionInPeers)
                                              : '--'}
                                          </span>
                                        </li>
                                      )
                                    })}
                                    <ReactTooltip place="top" delayShow={500} id="albert-child" />
                                  </>
                                ) : null}
                              </ul>
                            </Collapse>
                          </div>
                        </li>
                      )
                    })}
                <ReactTooltip place="top" delayShow={500} id="albert" />
              </>
            ) : (
              <li className="list-loading">
                <div className="lds-dual-ring">
                  <div />
                </div>
              </li>
            )}
          </ul>
        )}
      </div>
    )
  }
}

export default FactSetBaseList
