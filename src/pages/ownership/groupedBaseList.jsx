import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { PATH } from 'consts'
import { Collapse } from 'react-collapse'
import ReactTooltip from 'react-tooltip'

import { i18n } from 'translate'
import { getCurrentShareholderChildPosition } from 'client'
import { defineTheme, formatVolumePrice, generateUniqueId } from 'utils'

class GroupedBaseList extends Component {
  constructor(props) {
    super(props)
    this.state = {
      childItems: [],
      expandedItems: [],
      pointerEvents: false,
      idList: '',
      orderedBy: 'StocksLastDateValue',
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.referenceDate !== this.props.referenceDate) {
      this.setState({ childItems: [], expandedItems: [] })
    }
  }

  onClickPointerEvents = (item, index, groupName) => {
    this.setState(
      {
        idList: index,
      },
      () => {
        if (index === this.state.idList) {
          const formatItemName = groupName
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/\s/g, '')
            .trim()
          const limit = formatItemName === ('pessoasfisicas' || 'pessoafisica') ? 10 : 100
          const { pointerEvents } = this.state
          this.setState({ pointerEvents: !pointerEvents })
          setTimeout(() => {
            this.onShowDetail(item, limit)
          }, 1500)
        }
      }
    )
  }

  onShowDetail = (id, limit) => {
    const { childItems, expandedItems } = this.state
    const index = expandedItems.indexOf(id)

    if (index === -1) {
      expandedItems.push(id)
    } else {
      expandedItems.splice(index, 1)
    }

    if (childItems[id]) {
      this.setState({ expandedItems, pointerEvents: false })
    } else {
      getCurrentShareholderChildPosition(
        this.props.companyId,
        this.props.tickerId,
        this.props.referenceDate,
        id,
        limit
      ).then((res) => {
        childItems[id] = res.data.positions
        this.setState({ expandedItems, childItems, pointerEvents: false })
      })
    }
  }

  formatted = (val, pref, suf) => {
    const language = this.props.idiom === 1 ? 'pt-BR' : 'en-US'
    return `${pref || ''}${Number(val).toLocaleString(language)}${suf || ''}`
  }

  openClosePriceModal = () => {
    this.props.openClosePriceModal()
  }

  onSortClickHandler = () => {
    this.props.onSortClickHandler()
  }

  renderClassName = () => {
    let className = `header-title ${this.state.orderedBy.toLowerCase()}`
    if (this.props.currentOrder === -1) {
      className += '-desc'
    } else {
      className += '-asc'
    }
    return className
  }

  render() {
    const group = 99
    const simple = 1
    return (
      <div className="base-content">
        <ul className={defineTheme('base-list shareholder-base-list grouped')}>
          <li className={this.renderClassName()}>
            <span className="name sticky">{i18n.t('ownership.name')}</span>
            <span className="shareholder-type">{i18n.t('ownership.shareholderType')}</span>
            <span className="country">{i18n.t('ownership.country')}</span>
            <span className={this.props.showVolume ? 'volume-last-value' : 'volume-last-value price'}>
              <span
                onClick={() => this.onSortClickHandler()}
                onKeyDown={() => this.onSortClickHandler()}
                tabIndex={0}
                role="button"
              >
                {this.props.isDaily
                  ? this.props.showVolume
                    ? i18n.t('ownership.volume')
                    : i18n.t('ownership.priceVolume')
                  : this.props.showVolume
                    ? i18n.t('ownership.finalVolume')
                    : i18n.t('ownership.finalPriceVolume')}{' '}
                <i className="order-by" />
              </span>{' '}
            </span>

            <span className="value">{i18n.t('ownership.value')}</span>
          </li>
          {!this.props.isLoading ? (
            this.props.currentBase.length === 0 ? (
              <li className="no-results">{i18n.t('ownership.noResultsFoundOnYourBase')}</li>
            ) : (
              <>
                {this.props.currentBase.map((item, index) => {
                  const key = generateUniqueId()
                  return (
                    <li key={key}>
                      {item.isExpandable ? (
                        <span
                          onClick={() => this.onClickPointerEvents(item.shareholderGroupId, index, item.name)}
                          className={`open-drill-dawn sticky ${this.state.pointerEvents ? 'pointerEvents' : ''}`}
                          onKeyDown={() => this.onClickPointerEvents(item.shareholderGroupId, index, item.name)}
                          tabIndex={-1}
                          role="button"
                        >
                          {this.state.pointerEvents ? (
                            this.state.idList === index ? (
                              <div className="lds-dual-ring">
                                <div />
                              </div>
                            ) : (
                              <span
                                className={
                                  this.state.expandedItems.includes(item.shareholderGroupId) ? 'close' : 'open'
                                }
                              />
                            )
                          ) : (
                            <span
                              className={this.state.expandedItems.includes(item.shareholderGroupId) ? 'close' : 'open'}
                            />
                          )}
                        </span>
                      ) : (
                        <span className="open-drill-dawn sticky">
                          {!item.alreadyGrouped && item.shareholderType !== group ? (
                            <i
                              data-tip={i18n.t('ownership.agroup')}
                              data-for="shareholder-base"
                              onClick={(e) => this.props.onOpenAgglutinationModal(e, item)}
                              onKeyDown={(e) => this.props.onOpenAgglutinationModal(e, item)}
                              tabIndex={-2}
                              role="button"
                              aria-label="openAgglutinationModal"
                            />
                          ) : null}
                        </span>
                      )}

                      <span
                        className="name sticky"
                        style={{
                          fontWeight: item.shareholderType === group ? 'bold' : 'normal',
                        }}
                        data-tip={item.name}
                        data-for="shareholder-base"
                      >
                        {item.shareholderType === group ? (
                          <Link to={`${PATH}/ownership/${item.shareholderGroupId}/grouped/overview`}>{item.name}</Link>
                        ) : (
                          <Link to={`${PATH}/ownership/${item.shareholderId}/simple/overview`}>{item.name}</Link>
                        )}
                      </span>

                      <span
                        className="shareholder-type"
                        style={{
                          fontWeight: item.shareholderType === group ? 'bold' : 'normal',
                        }}
                      >
                        {item.shareholderType === group
                          ? i18n.t('ownership.group')
                          : item.shareholderType === simple
                            ? i18n.t('ownership.fund')
                            : i18n.t('ownership.individual')}
                      </span>

                      <span className="country">{item.countryOriginal ? item.countryCode : '--'}</span>

                      <span
                        className="volume-last-value is-edited"
                        style={{
                          fontWeight: item.shareholderType === group ? 'bold' : 'normal',
                        }}
                      >
                        {`${this.formatted(item.stockAmountEdited)} (${(
                          (item.stockAmountEdited * 100) /
                          this.props.currentBatchInfo?.totalStocks
                        ).toFixed(2)}%)`}
                      </span>

                      {this.props.closingPrice ? (
                        <span
                          onClick={() => this.openClosePriceModal()}
                          onKeyDown={() => this.openClosePriceModal()}
                          tabIndex={-3}
                          role="button"
                        >
                          <span
                            className="value"
                            style={{
                              fontWeight: item.shareholderType === group ? 'bold' : 'normal',
                            }}
                          >
                            {formatVolumePrice(
                              item.stockAmountEdited,
                              this.props.closingPrice.closingPrice,
                              this.props.idiom
                            )}
                          </span>
                        </span>
                      ) : (
                        <span
                          onClick={() => this.openClosePriceModal()}
                          onKeyDown={() => this.openClosePriceModal()}
                          tabIndex={-4}
                          role="button"
                        >
                          <span className="value">{i18n.t('ownership.configure')}</span>
                        </span>
                      )}

                      <div
                        className={`history-detail ${
                          this.state.expandedItems.includes(item.shareholderGroupId) ? 'open' : 'close'
                        }`}
                      >
                        <Collapse isOpened={this.state.expandedItems.includes(item.shareholderGroupId)}>
                          <ul>
                            {this.state.childItems[item.shareholderGroupId] ? (
                              <>
                                {this.state.childItems[item.shareholderGroupId].map((subItem) => {
                                  const subKey = generateUniqueId()
                                  return (
                                    <li className="sub-item" key={subKey}>
                                      <span className="name sticky">
                                        <Link
                                          to={`${PATH}/ownership/${subItem.shareholderId}/simple/overview`}
                                          data-tip={subItem.displayName}
                                          data-for="shareholder-base-child"
                                        >
                                          {subItem.displayName}
                                        </Link>
                                      </span>
                                      <span className="shareholder-type">
                                        {subItem.shareholderType === simple
                                          ? i18n.t('ownership.fund')
                                          : i18n.t('ownership.individual')}
                                      </span>
                                      <span className="country">{subItem.countryCode}</span>
                                      <span className="volume-last-value">
                                        {`${this.formatted(subItem.stockAmountEdited)} (${(
                                          (subItem.stockAmountEdited * 100) /
                                          this.props.currentBatchInfo.totalStocks
                                        ).toFixed(2)}%)`}
                                      </span>

                                      {this.props.closingPrice ? (
                                        <span
                                          onClick={() => this.openClosePriceModal()}
                                          onKeyDown={() => this.openClosePriceModal()}
                                          tabIndex={-5}
                                          role="button"
                                        >
                                          <span className="value">
                                            {formatVolumePrice(
                                              subItem.stockAmountEdited,
                                              this.props.closingPrice.closingPrice,
                                              this.props.idiom
                                            )}
                                          </span>
                                        </span>
                                      ) : (
                                        <span
                                          onClick={() => this.openClosePriceModal()}
                                          onKeyDown={() => this.openClosePriceModal()}
                                          tabIndex={-6}
                                          role="button"
                                        >
                                          <span className="value">{i18n.t('ownership.configure')}</span>
                                        </span>
                                      )}
                                    </li>
                                  )
                                })}
                                <ReactTooltip place="top" delayShow={500} id="shareholder-base-child" />
                              </>
                            ) : null}
                          </ul>
                        </Collapse>
                      </div>
                    </li>
                  )
                })}
                <ReactTooltip place="top" delayShow={500} id="shareholder-base" />
              </>
            )
          ) : (
            <li className="list-loading">
              <div className="lds-dual-ring">
                <div />
              </div>
            </li>
          )}
        </ul>
      </div>
    )
  }
}

export default GroupedBaseList
