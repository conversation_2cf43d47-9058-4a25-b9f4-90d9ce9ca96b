import { formatNumber } from 'utils'

import { shareholderInfo, shareholderInfoView } from './interfaces'
import { IShareholderFormater } from './interfaces/IShareholderFormater'
import { formatText } from './services'

export const shareholdersGroupedFormater: IShareholderFormater = {
  format: (data: shareholderInfo[], idiom: number) =>
    data.map<shareholderInfoView>((item) => ({
      ...item,
      name: item.name.toUpperCase(),
      marketValuePeersUSD: formatNumber(idiom, item.marketValuePeersUSD, 2),
      shareholderType: formatText(item.shareholderType.toString()),
      pct: formatNumber(idiom, item.pct, 2),
      stockAmountEdited: formatNumber(idiom, item.stockAmountEdited),
      marketValue: formatNumber(idiom, item.marketValue),
      totalAum: formatNumber(idiom, item.totalAum),
      institutionType: formatText(item.institutionType),
      institutionStyle: formatText(item.institutionStyle),
      turnover: formatText(item.turnover),
      city: formatText(item.city),
    })),
}
