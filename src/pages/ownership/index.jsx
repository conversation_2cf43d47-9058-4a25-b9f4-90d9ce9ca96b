import React, { Component } from 'react'
import { PATH, COMMON } from 'consts'
import { BaseModal, Buttons, Inputs, Loading } from '@mz-codes/design-system'
import 'assets/styles/pages/_ownership.scss'
import {
  getTickers,
  getCurrentShareholderBase,
  getCurrentShareholderAlbertBase,
  getCurrentShareholderFactSetBase,
  exportCurrentShareholderBase,
  exportCurrentShareholderAlbertBase,
  createGroupAndGroupShareholder,
  saveClosingPrice,
  getCompanyCurrentPortfolio,
} from 'client'

import { postUploadShareholderBase, getAvailableDates, getClassificationsList, hocWrapper, getGroups } from 'hooks'

import { i18n } from 'translate'

import { UploadModal, GroupModal, LinkButton, GoToHistoryButton } from 'components'
import { dateFromUTC, debounce, localInformation, defineTheme, hasScopePermission, formatDateToString } from 'utils'
import { exportOwnershipFactSetVision } from 'globals/services'
import { StoreContext } from 'contexts/store'
import { BaseError } from 'errors'
import { SHAREHOLDER_TYPES } from 'types/shareholders'
import OwnershipHeader from './header'
import SimpleBaseList from './simpleBaseList'
import GroupedBaseList from './groupedBaseList'
import AlbertBaseList from './albertBaseList'
import FactSetBaseList from './factSetBaseList'
import { postGroupShareholder } from 'hooks'

const visionTypes = {
  simple: '0',
  grouped: '1',
  albert: '2',
  factSet: '3',
}

const idiomIQ = () => (i18n.language === 'en-US' ? 0 : 1)

class BaseOwnership extends Component {
  searchDebouncer = debounce(() => {
    this.handleFetchingData()
  }, 1000)

  constructor(props) {
    super(props)
    this.state = {
      isLoading: true,
      showMapModal: false,
      topQuantity: {
        label: 10,
        value: 10,
      },
      showVolume: true,
      vision: visionTypes.grouped,
      showAggModal: false,
      tickers: [],
      currentDate: new Date(),
      currentTicker: {},
      currentBase: [],
      currentBatchInfo: null,
      currentPortfolio: {},
      portfolios: [],
      modalClosePrice: false,
      searchTerm: '',
      currentShareholderType: i18n.t('ownership._dropdownOptions.shareholderTypes.0', {
        returnObjects: true,
      }),
      formatShareholderTypeForReqs: null,
      currentOrder: 1,
      currentFund: null,
      currentClosingPrice: 0,
      closingPrice: null,
      currentFundId: '',
      currentGroupedType: i18n.t('ownership._dropdownOptions.grouped.0', {
        returnObjects: true,
      }),
      currentClassification: null,
      allClassifications: [{ value: null, label: i18n.t('globals.all') }],
      companyId: localInformation.getCompanyId(),
      userId: localInformation.getUserId(),
      idiom: idiomIQ(),
      hasIntelligenceProductActive: false,
      selectedFile: undefined,
      uploadProgress: 0,
      groupList: [],
    }
  }

  componentDidMount() {
    const hasEditPermission = hasScopePermission(['mzshareholders:management:closing:price:post'])
    this.setPageAmount()
    this.hasIntelligencePermission()
    this.setState({ hasEditPermission })
    this.getCurrentPorfolio(this.state.companyId, this.state.userId)
    this.getClassifications(this.state.companyId)
    this.handleGetGroups()
  }

  getWillMount = () => {
    this.componentDidMount()
  }

  handleExport = () => {
    if (this.state.vision === visionTypes.albert) {
      this.exportCurrentAlbertBase()
    } else if (this.state.vision === visionTypes.factSet) {
      this.exportCurrentFactSetBase()
    } else {
      this.exportCurrentBase()
    }
  }

  handleFetchingData = async () => {
    const { vision, currentTicker, currentDate, currentPortfolio } = this.state
    try {
      this.setState({ isLoading: true })

      if (vision === visionTypes.albert) {
        await this.getCurrentAlbertBase(currentTicker.value, formatDateToString(currentDate))
      } else if (vision === visionTypes.factSet) {
        if (!currentPortfolio) {
          this.props.createToast({
            type: 'error',
            title: i18n.t('globals.errors.noPortfolioData.title'),
            description: i18n.t('globals.errors.noPortfolioData.message'),
          })
          this.setState({ isLoading: false })
        } else {
          await this.getCurrentFactSetBase(currentTicker.value, formatDateToString(currentDate), currentPortfolio.value)
        }
      } else {
        await this.getCurrentBase(currentTicker.value, currentDate)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      this.setState({ isLoading: false })
    }
  }

  setPageAmount() {
    const { topQuantity } = this.state
    let globalItemsAmount = localInformation.getPageItemsAmount()

    if (!globalItemsAmount) {
      globalItemsAmount = toString(topQuantity.value)
      localInformation.setPageItemsAmount(globalItemsAmount)
    }
    this.setState({ topQuantity }, () => this.getTickers())
  }

  getTickers = () => {
    const { companyId } = this.state
    getTickers(companyId).then((res) => {
      if (res.success) {
        const options = res.data.map((ticker) => ({
          label: ticker.label,
          value: ticker.tickerId,
        }))

        const currentTicker = options[0] ?? null

        this.setState(
          {
            tickers: options,
            currentTicker,
          },
          () => currentTicker && this.getAvailableDates(currentTicker.value)
        )
      }
    })
  }

  getAvailableDates = async (tickerId) => {
    const { companyId } = this.state
    try {
      getAvailableDates({ companyId, tickerId }).then((res) => {
        const parsedDates = res.sort((a, b) => (a > b ? 1 : -1)).map((date) => dateFromUTC(date))

        const lastAvaiableDate = parsedDates[parsedDates.length - 1]

        if (!lastAvaiableDate) {
          this.props.createToast({
            type: 'alert',
            title: i18n.t('globals.infos.noBaseListFound.title'),
            description: i18n.t('globals.infos.noBaseListFound.message'),
          })

          return this.setState({
            closingPrice: null,
            currentBase: [],
            currentBatchInfo: null,
            datesAvailable: [],
            isLoading: false,
            showMapModal: true,
          })
        }

        return this.setState(
          {
            datesAvailable: parsedDates,
            currentDate: lastAvaiableDate,
          },
          this.handleFetchingData
        )
      })
    } catch (error) {
      console.error('Error fetching data:', error)
    }
  }

  handleGetGroups = async () => {
    try {
      const { companyId } = this.state
      const shareholderGroups = await getGroups(companyId)

      const formatShareholderGroups = shareholderGroups.data.data.map((shareholderGroup) => ({
        label: shareholderGroup.name,
        value: shareholderGroup.shareholderGroupId,
      }))

      this.setState({
        groupList: formatShareholderGroups,
      })
    } catch {
      return this.props.createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        type: 'error',
      })
    }
  }

  getCurrentBase = async (tickerId, referenceDate) => {
    try {
      const res = await getCurrentShareholderBase(
        this.state.companyId,
        tickerId,
        formatDateToString(referenceDate),
        this.state.topQuantity.value,
        this.state.searchTerm.trim(),
        this.state.formatShareholderTypeForReqs,
        this.state.currentOrder,
        this.state.vision !== '0', // 1 or 2 === is grouped
        this.state.vision === '2', // 2 === isAlbert true,
        this.state.currentGroupedType.value,
        this.state.currentClassification?.value ?? null
      )
      this.setState({
        currentBase: res.positions,
        currentBatchInfo: res.batch,
        closingPrice: res.closingPrice,
      })
    } catch (err) {
      throw new Error(err)
    }
  }

  getCurrentAlbertBase = (tickerId, referenceDate) => {
    getCurrentShareholderAlbertBase(
      this.state.companyId,
      tickerId,
      referenceDate,
      this.state.topQuantity.value,
      this.state.searchTerm.trim(),
      this.state.formatShareholderTypeForReqs,
      this.state.currentOrder,
      this.state.vision !== '0' // 1 or 2 === is grouped
    ).then((res) => {
      if (res.success) {
        this.setState({
          currentBase: res.data.positions,
          currentBatchInfo: res.data.batch,
          closingPrice: res.data.closingPrice,
        })
      }
    })
  }

  getCurrentFactSetBase = async (tickerId, referenceDate, currentPortfolio) => {
    const { companyId, userId, topQuantity, searchTerm } = this.state

    try {
      const res = await getCurrentShareholderFactSetBase(
        companyId,
        tickerId,
        userId,
        currentPortfolio,
        referenceDate,
        topQuantity.value,
        searchTerm
      )
      this.setState({
        currentBase: res.data.positions,
        closingPrice: res.closingPrice,
      })
    } catch (e) {
      throw new Error(e)
    }
  }

  getCurrentPorfolio = (companyId, userId) => {
    getCompanyCurrentPortfolio(companyId, userId).then((res) => {
      const portfolios = res.data.map((item) => ({
        value: item.id,
        label: item.name,
      }))
      const currentPortfolio = portfolios[0]
      this.setState({
        portfolios,
        currentPortfolio,
      })
      return currentPortfolio
    })
  }

  getClassifications = async (companyId, offset = 0, limit = 10) => {
    try {
      const classifications = await getClassificationsList({
        companyId,
        offset,
        limit,
      })

      if (!classifications.length) return this.setState({ isLoading: false })

      const classificationsLabel = classifications.map((classification) => ({
        value: classification.classificationId,
        label: classification.description,
      }))

      this.setState({
        allClassifications: [{ value: null, label: i18n.t('globals.all') }, ...classificationsLabel],
      })
    } catch (error) {
      throw new Error(error)
    }
  }

  exportCurrentFactSetBase = async () => {
    const { companyId, currentTicker, currentPortfolio, currentDate, searchTerm, topQuantity, currentOrder } =
      this.state

    try {
      const response = await exportOwnershipFactSetVision({
        companyId,
        tickerId: currentTicker,
        portfolioId: currentPortfolio.value,
        referenceDate: currentDate,
        search: searchTerm.trim(),
        limit: +topQuantity.value,
        order: currentOrder,
        language: idiomIQ(),
      })

      if (!response.success)
        return this.props.createToast({
          type: 'error',
          title: i18n.t('globals.export.error.title'),
          description: i18n.t('globals.export.error.message'),
        })

      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })
    } catch (err) {
      this.setState({ isLoading: false })
    }
  }

  exportCurrentBase = () => {
    exportCurrentShareholderBase(
      this.state.companyId,
      this.state.currentTicker.value,
      formatDateToString(this.state.currentDate)
    ).then((res) => {
      this.props.createToast({
        type: 'success',
        title: i18n.t('globals.export.success.title'),
        description: i18n.t('globals.export.success.message'),
        buttons: <GoToHistoryButton />,
      })

      this.setState({ isLoading: false })
    })
  }

  exportCurrentAlbertBase = () => {
    exportCurrentShareholderAlbertBase(
      this.state.companyId,
      this.state.currentTicker.value,
      formatDateToString(this.state.currentDate),
      this.state.topQuantity.value,
      this.state.searchTerm.trim(),
      this.state.formatShareholderTypeForReqs,
      this.state.currentOrder,
      this.state.vision === '1'
    ).then((res) => {
      if (res.success) {
        this.props.createToast({
          type: 'success',
          title: i18n.t('globals.export.success.title'),
          description: i18n.t('globals.export.success.message'),
          buttons: <GoToHistoryButton />,
        })
      }
    })
  }

  openClosePriceModal = () => {
    this.setState({ modalClosePrice: true })
  }

  onChangeTopQuantity = (quantity) => {
    if (quantity.value === this.state.topQuantity.value) return

    localInformation.setPageItemsAmount(quantity.value)
    this.setState({ topQuantity: quantity, isLoading: true }, this.handleFetchingData)
  }

  onChangeShareholderType = (shareholderType) => {
    if (shareholderType.value === this.state.currentShareholderType.value) return

    const formatShareholderType = shareholderType.value === '0' ? null : shareholderType.value

    this.setState(
      { currentShareholderType: shareholderType, formatShareholderTypeForReqs: formatShareholderType, isLoading: true },
      this.handleFetchingData
    )
  }

  handleSearchValueChange = (e) => {
    this.setState({ searchTerm: e.target.value })

    if (e.target.value.length >= 3) {
      e.persist()
      this.searchDebouncer(e)
    } else if (e.target.value.length === 0) {
      e.persist()
      this.searchDebouncer(e)
    }
  }

  onOpenAgglutinationModal = (e, item) => {
    this.setState({
      showAggModal: true,
      currentFund: item,
      currentFundId: item.shareholderId,
    })
  }

  closeAggModal = () => {
    this.setState({ showAggModal: false })
  }

  onChangeStockType = (ticker) => {
    this.setState({ currentTicker: ticker, isLoading: true }, () => this.getAvailableDates(ticker.value))
  }

  onChangeGroupedType = (value) => {
    this.setState({ currentGroupedType: value, isLoading: true }, this.handleFetchingData)
  }

  onSortClick = () => {
    const { currentOrder } = this.state

    const order = currentOrder * -1
    this.setState({ currentOrder: order, isLoading: true })
    this.handleFetchingData()
  }

  handleGroupedShareholderActionsToast = (shareholderGroupId, shareholderId) => {
    this.props.createToast({
      title: i18n.t('globals.groupedShareholderActionsToastfy.success.title'),
      description: i18n.t('globals.groupedShareholderActionsToastfy.success.message'),
      duration: 15000,
      type: 'success',
      buttons: (
        <>
          <LinkButton link={`${PATH}/ownership/${shareholderGroupId}/grouped/overview`}>
            {i18n.t('groupDetail')}
          </LinkButton>
          <LinkButton link={`${PATH}/ownership/${shareholderId}/simple/overview`}>
            {i18n.t('shareholderDetail')}
          </LinkButton>
        </>
      ),
    })
  }

  onCreateGroup = async (groupName) => {
    try {
      const res = await createGroupAndGroupShareholder(
        this.state.companyId,
        groupName,
        this.state.currentFund.document,
        this.state.currentFund.documentType
      )

      await this.getCurrentBase(this.state.currentTicker.value, this.state.currentDate)
      await this.handleGetGroups()

      this.setState({ showAggModal: false })

      this.handleGroupedShareholderActionsToast(res.data.shareholderGroupId, this.state.currentFundId)
    } catch (error) {
      this.props.createToast({
        title: i18n.t('globals.errors.createGroup.title'),
        description: i18n.t('globals.errors.createGroup.message'),
        duration: 9000,
        type: 'error',
      })

      this.setState({ showAggModal: false })
    }
  }

  onChangePortfolio = (currentPortfolio) => {
    if (currentPortfolio.value === this.state.currentPortfolio.value) return
    this.setState({
      currentPortfolio,
      isLoading: true,
    })
    this.getCurrentFactSetBase(
      this.state.currentTicker.value,
      formatDateToString(this.state.currentDate),
      currentPortfolio.value
    )
  }

  onVinculateGroup = async (shareholderGroup) => {
    const { companyId, currentFund } = this.state
    const shareholderGroupId = shareholderGroup.value
    try {
      await postGroupShareholder({
        companyId,
        shareholderGroupId,
        shareholderDocument: currentFund.document,
        shareholderDocumentType: currentFund.documentType,
      })

      this.setState({ showAggModal: false })
      this.handleFetchingData()

      this.handleGroupedShareholderActionsToast(shareholderGroupId, this.state.currentFundId)
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          type: 'error',
          title: err.title,
          description: err.message,
        })
        return
      }
      this.props.createToast({
        type: 'error',
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
      })

      this.setState({ showAggModal: false })
    }
  }

  onChangeBaseMode = (param) => {
    this.setState(
      {
        vision: param,
        isLoading: true,
      },
      this.handleFetchingData
    )
  }

  onChangeEndDate = (date) => {
    const { currentDate } = this.state
    if (date === currentDate) return

    this.setState({ currentDate: date, isLoading: true }, this.handleFetchingData)
  }

  onChangeClassification = (classification) => {
    const { currentClassification } = this.state
    if (classification.value === currentClassification?.value) return

    this.setState({ currentClassification: classification, isLoading: true }, this.handleFetchingData)
  }

  onOpenUploaderModal = () => {
    this.setState({ showMapModal: true, selectedFile: null, uploadProgress: 0 })
  }

  onSaveClosingPrice = () => {
    saveClosingPrice(
      this.state.companyId,
      this.state.currentTicker.value,
      formatDateToString(this.state.currentDate),
      this.state.currentClosingPrice
    ).then(() => {
      this.setState({ modalClosePrice: false })
      this.handleFetchingData()
    })
  }

  hasIntelligencePermission = () => {
    const activeProducts = localInformation.getCore2SelectedCustomerUserApplications()
    this.setState({
      hasIntelligenceProductActive: activeProducts.includes('mz_intelligence'),
    })
  }

  onChangeCurrentClosingPrice = (event) => {
    this.setState({ currentClosingPrice: event.target.value })
  }

  handleCloseUploadModal = () => {
    this.setState({ selectedFile: undefined, showMapModal: false })
  }

  handleProgressFileUpload = (progressEvent) => {
    const { loaded, total } = progressEvent
    const progress = total > 0 ? Math.round((loaded * 100) / total) : 0

    this.setState({
      uploadProgress: progress,
    })
  }

  handleSelectedFile = (file) => {
    if (file === this.state.selectedFile) return
    this.setState({
      selectedFile: file,
    })
  }

  handleGroupModalTitleShareholderType = () => {
    const { currentFund } = this.state
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.FUND) return i18n.t('ownership.fund').toLowerCase()
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.INDIVIDUAL)
      return i18n.t('ownership.individual').toLowerCase()
    if (currentFund?.shareholderType === SHAREHOLDER_TYPES.UNKNOW) return ''

    return
  }

  handleUploadFile = async () => {
    try {
      if (!this.state.selectedFile) return
      await postUploadShareholderBase({
        file: this.state.selectedFile,
        companyId: this.state.companyId,
        tickerId: this.state.currentTicker.value,
        idiom: this.state.idiom,
        onUploadProgress: this.handleProgressFileUpload,
      })
    } catch (err) {
      if (err instanceof BaseError) {
        this.props.createToast({
          type: 'error',
          title: err.title,
          description: err.message,
          duration: 5000,
        })
        return
      }
      throw err
    } finally {
      this.setState({ showMapModal: false })
    }
  }

  render() {
    const {
      tickers,
      currentDate,
      datesAvailable,
      currentBase,
      currentBatchInfo,
      currentFundId,
      currentShareholderType,
      currentOrder,
      groupList,
      searchTerm,
      isLoading,
      modalClosePrice,
      topQuantity,
      currentGroupedType,
      allClassifications,
      currentClassification,
      currentPortfolio,
      portfolios,
      hasIntelligenceProductActive,
      hasEditPermission,
      vision: visionState,
      showMapModal,
      selectedFile,
      uploadProgress,
    } = this.state

    const showExport = true
    const disableTickers = visionState === visionTypes.factSet
    const disableGrouping = visionState === visionTypes.factSet
    const disablePortfolio = visionState === visionTypes.factSet
    const disableSearchBar = visionState === visionTypes.factSet
    const acceptedFiles = '.txt, .csv, .xls, .xlsx, .zip, .7z'

    if (tickers.length === 0) {
      return (
        <div className="shareholders-wrapper">
          <div className={defineTheme('shareholders-content')}>
            <div className="lds-dual-ring">
              <div />
            </div>
          </div>
        </div>
      )
    }

    function showPortfolioFilter(visionType) {
      if (visionType === visionTypes.factSet) return currentPortfolio
      return null
    }

    return (
      <div className="shareholders-wrapper">
        <div className={defineTheme('shareholders-content')}>
          <div>
            <OwnershipHeader
              tickers={tickers}
              datesAvailable={datesAvailable}
              currentDate={currentDate}
              currentClassification={currentClassification}
              allClassifications={allClassifications}
              currentGroupedType={currentGroupedType}
              onChangeClassification={this.onChangeClassification}
              onChangeShareholderType={this.onChangeShareholderType}
              onChangeStockType={this.onChangeStockType}
              onChangeEndDate={this.onChangeEndDate}
              onChangeBaseMode={this.onChangeBaseMode}
              onOpenUploaderModal={this.onOpenUploaderModal}
              onChangeTopQuantity={this.onChangeTopQuantity}
              onChangeGroupedType={this.onChangeGroupedType}
              onChangePortfolio={this.onChangePortfolio}
              topQuantity={COMMON.topQuantity}
              selectedTopQuantity={topQuantity}
              handleSearchValueChange={this.handleSearchValueChange}
              searchTerm={searchTerm}
              currentShareholderType={currentShareholderType}
              showExport={showExport}
              onExportBase={this.handleExport}
              grouped={this.state.vision === '1'}
              vision={this.state.vision}
              currentPortfolio={showPortfolioFilter(this.state.vision)}
              portfolios={portfolios}
              hasIntelligenceProductActive={hasIntelligenceProductActive}
              isLoading={isLoading}
              disableTickers={disableTickers}
              disableGrouping={disableGrouping}
              disablePortfolio={disablePortfolio}
              disableSearchBar={disableSearchBar}
            />

            {!this.state.isLoading && (
              <div
                className={`scroll shareholder-base ownership-base ${
                  this.state.vision === visionTypes.factSet ? 'ownership-scroll' : ' '
                }`}
              >
                {this.state.vision === visionTypes.simple ? (
                  <SimpleBaseList
                    onSortClickHandler={this.onSortClick}
                    onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                    openClosePriceModal={this.openClosePriceModal}
                    currentBase={currentBase}
                    currentBatchInfo={currentBatchInfo}
                    idiom={this.state.idiom}
                    closingPrice={this.state.closingPrice}
                    showVolume={this.state.showVolume}
                    currentOrder={currentOrder}
                    isLoading={isLoading}
                  />
                ) : null}

                {this.state.vision === visionTypes.grouped ? (
                  <GroupedBaseList
                    onSortClickHandler={this.onSortClick}
                    onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                    openClosePriceModal={this.openClosePriceModal}
                    currentBase={currentBase}
                    currentBatchInfo={currentBatchInfo}
                    idiom={this.state.idiom}
                    companyId={this.state.companyId}
                    tickerId={this.state.currentTicker.value}
                    referenceDate={formatDateToString(this.state.currentDate)}
                    closingPrice={this.state.closingPrice}
                    vision={this.state.vision}
                    showVolume={this.state.showVolume}
                    currentOrder={currentOrder}
                    isLoading={isLoading}
                  />
                ) : null}

                {this.state.vision === visionTypes.albert ? (
                  <AlbertBaseList
                    onSortClickHandler={this.onSortClick}
                    onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                    openClosePriceModal={this.openClosePriceModal}
                    currentBase={currentBase}
                    currentBatchInfo={currentBatchInfo}
                    idiom={this.state.idiom}
                    companyId={this.state.companyId}
                    tickerId={this.state.currentTicker.value}
                    referenceDate={formatDateToString(this.state.currentDate)}
                    closingPrice={this.state.closingPrice}
                    vision={this.state.vision}
                    showVolume={this.state.showVolume}
                    currentOrder={currentOrder}
                    isLoading={isLoading}
                  />
                ) : null}

                {this.state.vision === visionTypes.factSet && hasIntelligenceProductActive ? (
                  <FactSetBaseList
                    onSortClickHandler={this.onSortClick}
                    onOpenAgglutinationModal={this.onOpenAgglutinationModal}
                    openClosePriceModal={this.openClosePriceModal}
                    currentBase={currentBase}
                    currentBatchInfo={currentBatchInfo}
                    idiom={this.state.idiom}
                    companyId={this.state.companyId}
                    tickerId={this.state.currentTicker.value}
                    userId={this.state.userId}
                    portfolioId={this.state.currentPortfolio?.value}
                    referenceDate={formatDateToString(this.state.currentDate)}
                    closingPrice={this.state.closingPrice}
                    vision={this.state.vision}
                    showVolume={this.state.showVolume}
                    currentOrder={currentOrder}
                    isLoading={isLoading}
                  />
                ) : null}
              </div>
            )}
            {this.state.isLoading && <Loading />}
          </div>
        </div>

        <UploadModal
          show={showMapModal}
          onConfirm={this.handleUploadFile}
          onClose={this.handleCloseUploadModal}
          selectedFile={selectedFile}
          onSelectedFile={this.handleSelectedFile}
          title={i18n.t('uploadShareholderBase')}
          progress={uploadProgress}
          acceptedFiles={acceptedFiles}
          confirmButtonLabel={i18n.t('upload')}
        />

        {hasEditPermission ? (
          <BaseModal
            show={modalClosePrice}
            onClose={() => {
              this.setState({ modalClosePrice: false })
            }}
            width="600px"
          >
            <BaseModal.Header>
              <BaseModal.Title>{i18n.t('ownership.closingPrice')}</BaseModal.Title>
            </BaseModal.Header>
            <BaseModal.Body>
              <Inputs.Label $horizontalAlignment="start" htmlFor="input-close-price">
                R${' '}
              </Inputs.Label>
              <Inputs.BaseLined
                value={this.state.currentClosingPrice}
                onChange={this.onChangeCurrentClosingPrice}
                id="input-close-price"
              />
            </BaseModal.Body>
            <BaseModal.Footer>
              <BaseModal.ButtonWrapper>
                <Buttons.Primary onClick={this.onSaveClosingPrice}>{i18n.t('ownership.save')}</Buttons.Primary>
              </BaseModal.ButtonWrapper>
            </BaseModal.Footer>
          </BaseModal>
        ) : null}

        <GroupModal
          title={`${i18n.t('ownership.agroup')} ${this.handleGroupModalTitleShareholderType()}`}
          visibility={this.state.showAggModal}
          options={groupList}
          onClose={this.closeAggModal}
          onCreateGroup={this.onCreateGroup}
          onConfirm={this.onVinculateGroup}
        />
      </div>
    )
  }
}

BaseOwnership.contextType = StoreContext

const Ownership = hocWrapper(BaseOwnership)

export default Ownership
