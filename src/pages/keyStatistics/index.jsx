// External components
import React, { useState } from 'react'

import { localInformation } from 'utils'

import 'assets/styles/pages/_keyStatistics.scss'

// Internal components/imports
import { i18n } from 'translate'
import CompanyTearsheet from './CompanyTearsheet'

function CompanyKeyStatistics(props) {
  const companyId = localInformation.getCompanyId()
  const dealogic = localInformation.getDealogicInformation()
  const currentDealogic = localInformation.getDealogicCurrentInformation()

  const tickers = dealogic?.map((ticker) => ({
    label: ticker.stock_symbol,
    value: ticker.dealogic_listing_id,
  }))

  const currentTicker = {
    label: currentDealogic?.stock_symbol ?? '',
    value: currentDealogic?.dealogic_listing_id ?? '',
  }

  return (
    <CompanyTearsheet
      companyId={companyId}
      currentTicker={currentTicker}
      idiom={i18n.language}
      history={props.history}
      location={props.location}
      tickers={tickers}
    />
  )
}

export default CompanyKeyStatistics
