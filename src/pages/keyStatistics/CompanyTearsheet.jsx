// External components
import React, { Component } from 'react'
import moment from 'moment'
import ReactTooltip from 'react-tooltip'
import { Buttons, Icons, Loading, Dropdown } from '@mz-codes/design-system'
import { useNavigate, useLocation } from 'react-router-dom'

// Internal components/imports
import { numberHelper, utils, localInformation, hasProductPermission } from 'utils'
import { useExternalRedirect, EXTERNAL_PRODUCT } from 'hooks/use-external-redirect'
import { savePageAccessLog, getEstimatesHighlights, getDistributionByStyle, getDistributionByTurnover } from 'client'

// Translations
import { i18n } from 'translate'

// Images
import { APPLICATIONS_ENUM } from 'consts'
import GenericChart from './GenericChart'
import IntelligenceTooltip from './IntelligenceTooltip'

class CompanyTearsheet extends Component {
  // NUMBER FORMATS
  constructor(props) {
    super(props)
    this.state = {
      idiom: i18n.language === 'pt-BR' ? 1 : 0,
      companyId: localInformation.getCompanyId(),
      currentTab: 'KeyStatistics', // KeyStatistics|Shareholders|TargetsAndRatings|Address
      currentTicker: null,
      distributionByStyle: null,
      distributionByTurnover: null,
      estimatesHighlights: null,
      isLoading: true,
      showDeniedIntelligenceAccess: false,
      tickers: [],
    }
  }

  async componentDidMount() {
    this.saveLog()
    await this.loadTickers()
    this.loadData()
  }
  async componentDidUpdate(prevProps) {
    if (this.props.context === 'intelligence:company:tearsheet:self') {
      if (prevProps.currentTicker.value !== this.props.currentTicker.value) {
        const s = {
          currentTicker: this.props.currentTicker,
          isLoading: true,
        }

        this.saveLog()
        this.setState(s, this.loadData)
      }
    }

    // when general iQ search points to another company/ticker
    if (this.props.context === 'intelligence:company:tearsheet:generic') {
      if (
        prevProps.location.pathname !== this.props.location.pathname ||
        prevProps.currentTickerCode !== this.props.currentTickerCode
      ) {
        this.saveLog()
        await this.resetState()
        await this.loadTickers()
        await this.loadData()
      }
    }

    ReactTooltip.rebuild()
  }

  numberFormat = (v, dp = 2) => numberHelper(v, this.state.idiom, null, null, dp)

  // eslint-disable-next-line class-methods-use-this
  valOrNA = (v, rep = 'N/A') => {
    return typeof v !== 'string' || v.trim() === '' ? rep : v
  }

  saveLog = () => {
    if (hasProductPermission(APPLICATIONS_ENUM.INTELLIGENCE)) {
      return savePageAccessLog(this.state.companyId, 'IRM: Company key statistics', this.props.location)
    }
    return savePageAccessLog(this.state.companyId, 'IRM: Company key statistics (denied)', this.props.location)
  }

  // LOAD DATA

  loadTickers = async () => {
    if (hasProductPermission(APPLICATIONS_ENUM.INTELLIGENCE)) {
      this.setState({
        currentTicker: this.props.currentTicker,
        tickers: this.props.tickers,
      })
    }
  }

  loadData = async () => {
    try {
      this.setState({
        isLoading: true,
      })
      if (hasProductPermission(APPLICATIONS_ENUM.INTELLIGENCE) && this.state.currentTicker.value) {
        await this.loadKeyStatistics()
        this.loadTabs()
        return
      }
      this.setState({
        showDeniedIntelligenceAccess: true,
      })
    } catch (e) {
      console.log(e)
    } finally {
      this.setState({
        isLoading: false,
      })
    }
  }

  loadKeyStatistics = async () => {
    const { currentTicker } = this.state
    const tickerId = currentTicker.value

    const responses = await Promise.all([
      getEstimatesHighlights(tickerId),
      getDistributionByStyle(tickerId),
      getDistributionByTurnover(tickerId),
    ])

    const estimatesHighlights = responses[0].data
    const distributionByStyle = responses[1].data
    const distributionByTurnover = responses[2].data

    this.setState({
      estimatesHighlights,
      distributionByStyle,
      distributionByTurnover,
    })
  }

  loadTabs = () => {
    const { currentTab } = this.state
    const tabs = []

    tabs.push({
      key: 'KeyStatistics',
      label: i18n.t('keyStatistics'),
    })

    if (
      this.props.context === 'intelligence:company:tearsheet:self' ||
      this.props.context === 'intelligence:company:tearsheet:generic'
    ) {
      tabs.push({
        key: 'Shareholders',
        label: i18n.t('pageKeyStatistics.shareholders'),
      })
    }

    if (this.props.context === 'intelligence:company:tearsheet:generic') {
      tabs.push({
        key: 'Address',
        label: i18n.t('address'),
      })
    }

    this.setState({
      currentTab,
      isLoading: false,
    })
  }

  // EVENTS

  onChangeCompanyTicker = (option) => {
    if (option.value === this.state.currentTicker.value) return
    this.setState(
      {
        currentTicker: option,
      },
      this.loadData
    )
  }

  // HELPERS

  getChartsClass = () => {
    return 'company-tearsheet-irm'
  }

  resetState = () => {
    return this.setState({
      distributionByStyle: null,
      distributionByTurnover: null,
      estimatesHighlights: null,
      isLoading: true,
    })
  }

  handleNavigate = () => {
    this.props.navigateToIntelligence('/dashboard')
  }

  // RENDERS

  renderDeniedAccess = () => {
    return (
      <div className="main-wrapper">
        <div className="content-wrapper">
          <div className="grey-wrapper" style={{ textAlign: 'center' }}>
            {i18n.t('pageKeyStatistics.deniedIntelligenceAccess')}{' '}
            <a href="mailto:<EMAIL>">{i18n.t('pageKeyStatistics.contactUs').toLowerCase()}</a>.
          </div>
          <div className={`intelligence-marketing ${i18n.language === 'pt-BR' ? 'pt' : ''}`} />
        </div>
      </div>
    )
  }

  renderTickerSelector = () => {
    return (
      <div className="grey-wrapper">
        <div className="companyData">
          <span className="label">Ticker:</span>
          <div style={{ display: 'inline-block' }}>
            <Dropdown
              options={this.state.tickers}
              onChange={this.onChangeCompanyTicker}
              selected={this.state.currentTicker}
            />
          </div>
          <div className="buttons">
            <Buttons.Primary
              onKeyDown={() => {
                this.handleNavigate()
              }}
              onClick={() => {
                this.handleNavigate()
              }}
            >
              {i18n.t('pageKeyStatistics.accessIntelligence')}
            </Buttons.Primary>
          </div>
        </div>
      </div>
    )
  }
  // TABS AND CONTENTS

  // eslint-disable-next-line react/no-unused-class-component-methods
  renderKeyStatisticsTabContent = () => {
    const { idiom } = this.props
    const { estimatesHighlights, distributionByStyle, distributionByTurnover } = this.state
    const { ownership } = estimatesHighlights || {}
    const { marketCap, referenceDate, stockPriceUsd, totalShares } = ownership || {}

    let { capGroup, sector } = ownership || {}

    const totalSharesFormatted = this.numberFormat(Math.round((parseFloat(totalShares) || 0) / 1000000), 0)
    const marketCapFormatted = this.numberFormat(Math.round((parseFloat(marketCap) || 0) / 1000000), 0)
    capGroup = this.valOrNA(capGroup, '--')
    sector = this.valOrNA(sector, '--')

    const styleAnalisysLabels = []
    const styleAnalisysYAxis = []
    distributionByStyle.forEach((d) => {
      styleAnalisysLabels.push(d.InvestorStyle)
      styleAnalisysYAxis.push({
        y: d.Position,
        yFormatted: utils.formatThousand(d.Position, idiom),
      })
    })

    const turnoverAnalysisLabels = []
    const turnoverAnalysisYAxis = []
    distributionByTurnover.forEach((d) => {
      turnoverAnalysisLabels.push(d.InvestorTurnover)
      turnoverAnalysisYAxis.push({
        name: d.InvestorTurnover,
        y: d.Number,
        yFormatted: utils.formatThousand(d.Number, idiom),
      })
    })

    const marketCapTip =
      marketCapFormatted === ''
        ? ''
        : `<table>
          <tr>
            <td>${i18n.t('pageKeyStatistics.marketCap')}:</td>
            <td>${utils.formatThousand(marketCap, idiom)} <span>(USD)</span></td>
          </tr>
          <tr>
            <td>${i18n.t('pageKeyStatistics.totalShares')}:</td>
            <td>${utils.formatThousand(totalShares, idiom)} <span>[${moment
              .utc(referenceDate)
              .format(i18n.t('dateFormat'))}]</span></td>
          </tr>
          <tr>
            <td>${i18n.t('pageKeyStatistics.stockPriceInDollar')}:</td>
            <td>${utils.formatPrice(stockPriceUsd, idiom)} <span>(USD) [${moment
              .utc(referenceDate)
              .format(i18n.t('dateFormat'))}]</span></td>
          </tr>
          <tr>
            <td>${i18n.t('pageKeyStatistics.currency')}:</td>
            <td>U.S. Dollar <span>(USD)</span></td>
          </tr>
          <tr>
            <td>${i18n.t('pageKeyStatistics.orderOfMagnitude')}:</td>
            <td>${i18n.t('pageKeyStatistics.million')} <span>(M)</span></td>
          </tr>
          <tr>
            <td>${i18n.t('pageKeyStatistics.description')}:</td>
            <td class='desc'>${i18n.t('pageKeyStatistics.marketCapRule')}</td>
          </tr>
        </table>`

    const totalSharesTip =
      totalSharesFormatted === ''
        ? ''
        : `<table>
          <tr>
            <td>${i18n.t('pageKeyStatistics.totalShares')}:</td>
            <td>${utils.formatThousand(totalShares, idiom)} <span>[${moment
              .utc(referenceDate)
              .format(i18n.t('dateFormat'))}]</span></td>
          </tr>
          <tr>
            <td>${i18n.t('pageKeyStatistics.orderOfMagnitude')}:</td>
            <td>${i18n.t('pageKeyStatistics.million')} <span>(M)</span></td>
          </tr>
          <tr>
            <td>${i18n.t('pageKeyStatistics.description')}:</td>
            <td class='desc'>${i18n.t('pageKeyStatistics.totalSharesRule')}</td>
          </tr>
        </table>`

    return (
      <>
        {/* first row */}
        <div className="simple-info-line-wrapper last">
          <div className="simple-info-items-wrapper">
            <div
              className="simple-info"
              data-tip={totalSharesTip}
              data-for="companyDataTooltip"
              data-place="right"
              data-offset="{'top': -5}"
            >
              <div>
                <span className="label">
                  {i18n.t('pageKeyStatistics.basicSharesM')} <Icons.Info size={13} />
                </span>
                <span className="value">{totalSharesFormatted || '--'}</span>
              </div>
            </div>
            <div
              className="simple-info"
              data-tip={marketCapTip}
              data-for="companyDataTooltip"
              data-place="right"
              data-offset="{'top': -5}"
            >
              <div>
                <span className="label">
                  Market cap (M) <Icons.Info size={13} />
                </span>
                <span className="value">
                  {marketCapFormatted} {marketCap ? `(USD)` : ''}{' '}
                </span>
              </div>
            </div>
            <div className="simple-info">
              <div>
                <span className="label">Cap group</span>
                <span className="value">{capGroup}</span>
              </div>
            </div>
            <div className="simple-info">
              <div>
                <span className="label">{i18n.t('pageKeyStatistics.sector')}</span>
                <span className="value">{sector}</span>
              </div>
            </div>
          </div>
        </div>

        {/* charts */}
        <div className={`charts-wrapper ${this.getChartsClass()}`}>
          <GenericChart
            categories={styleAnalisysLabels}
            enableDataLabels={false}
            idiom={this.state.idiom}
            isLegendEnabled={false}
            mainTitle={i18n.t('pageKeyStatistics.styleAnalysis')}
            series={[{ data: styleAnalisysYAxis }]}
            tooltipFormatter={(tooltipObj, point) => {
              return `<table class="chart-tooltip">
                  <tr>
                    <td class='label'>
                      ${point.category}:
                    </td>
                    <td class="value">
                      ${point.yFormatted} ${i18n.t('pageKeyStatistics.shares').toLowerCase()}
                    </td>
                  </tr>
                </table>`
            }}
            yAxisTitle={i18n.t('pageKeyStatistics.shares')}
          />

          <GenericChart
            chartType="pie"
            categories={turnoverAnalysisLabels}
            enableDataLabels
            idiom={this.state.idiom}
            isLegendEnabled
            mainTitle={i18n.t('pageKeyStatistics.turnoverAnalysis')}
            series={[
              {
                name: i18n.t('pageKeyStatistics.quantity'),
                data: turnoverAnalysisYAxis,
              },
            ]}
            tooltipFormatter={(tooltipObj, point) => {
              return `<table class="chart-tooltip">
                  <tr>
                    <td class='label'>
                      ${point.name}:
                    </td>
                    <td class="value">
                      ${point.yFormatted} ${i18n.t('pageKeyStatistics.shareholders').toLowerCase()}
                    </td>
                  </tr>
                </table>`
            }}
          />
        </div>
      </>
    )
  }

  // TOOLTIP

  // eslint-disable-next-line class-methods-use-this
  renderReactTooltip = () => {
    return <IntelligenceTooltip id="companyDataTooltip" html top={0} effect="solid" place="bottom" />
  }

  // MAIN

  render() {
    if (this.state.isLoading) return <Loading />
    if (this.state.showDeniedIntelligenceAccess) return this.renderDeniedAccess()

    return (
      <div className="main-wrapper">
        <div className="content-wrapper">
          {this.renderTickerSelector()}
          {/* convention for tab contents */}
          {this.renderKeyStatisticsTabContent()}
          {/* Tooltip */}
          {this.renderReactTooltip()}
        </div>
      </div>
    )
  }
}

function CompanyTearsheetWithRouter(props) {
  const navigate = useNavigate()
  const location = useLocation()
  const { navigateTo } = useExternalRedirect()

  // Função que será passada para o componente de classe
  const navigateToIntelligence = (path) => {
    navigateTo(EXTERNAL_PRODUCT.INTELLIGENCE, path)
  }

  return (
    <CompanyTearsheet
      {...props}
      navigate={navigate}
      location={location}
      navigateToIntelligence={navigateToIntelligence}
    />
  )
}

export default CompanyTearsheetWithRouter
