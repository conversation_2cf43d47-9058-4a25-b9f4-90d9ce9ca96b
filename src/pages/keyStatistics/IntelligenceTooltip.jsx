// External components
import React, { Component } from 'react'
import ReactTooltip from 'react-tooltip'

class IntelligenceTooltip extends Component {
  componentDidUpdate() {
    ReactTooltip.rebuild()
  }

  render() {
    return (
      <ReactTooltip
        border
        class="custom-tooltip"
        delayShow={500}
        effect={this.props.effect}
        html={this.props.html}
        id={this.props.id}
        multiline
        offset={{ top: this.props.top || 5, left: this.props.left }}
        place={this.props.place}
        type="info"
      />
    )
  }
}

IntelligenceTooltip.defaultProps = {
  effect: 'float',
  html: false,
  left: 0,
  place: 'top',
  top: 5,
}

export default IntelligenceTooltip
