// External components
import React, { Component } from 'react'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { Loading } from '@mz-codes/design-system'

// Translations
import { i18n } from 'translate'
import { theme } from '@mz-codes/design-system'

const chartMainColor = '#339fff'

class GenericChart extends Component {
  constructor(props) {
    super(props)
    this.state = {
      isLoading: true,
      options: {},
    }
  }

  componentDidMount() {
    this.mountChart()
  }

  componentDidUpdate(prevProps) {
    if (
      JSON.stringify(prevProps.series) !== JSON.stringify(this.props.series) ||
      prevProps.isHidden !== this.props.isHidden ||
      prevProps.isFullWidth !== this.props.isFullWidth ||
      prevProps.chartType !== this.props.chartType ||
      prevProps.mainTitle !== this.props.mainTitle ||
      JSON.stringify(prevProps.categories) !== JSON.stringify(this.props.categories)
    ) {
      this.setState(
        {
          isLoading: true,
          options: {},
        },
        () => this.mountChart()
      )
    }
  }

  // eslint-disable-next-line class-methods-use-this
  hasValue = (v) => {
    if (v === undefined || v === null || (typeof v === 'string' && v.trim() === '')) return false
    return true
  }

  mountChart = () => {
    // onClick
    const onPointClick =
      this.props.onPointClick !== undefined && this.props.onPointClick !== null && this.props.onPointClick !== ''
        ? this.props.onPointClick
        : null

    const tooltipYPositionSubtract = this.getTooltipYPositionSubtract()
    const tooltipMarginRight = this.getTooltipXPositionSubtract()
    const isAnimated = this.getIsAnimated()

    if (this.props.idiom === 'en-US') {
      Highcharts.setOptions({ lang: { decimalPoint: '.', thousandsSep: ',' } })
    } else {
      Highcharts.setOptions({ lang: { decimalPoint: ',', thousandsSep: '.' } })
    }

    const chartOptions = {
      chart: {
        backgroundColor: theme.legacy.colors.neutral.contentBackground,
        height: this.getChartHeight(),
        marginRight: 30,
        spacingBottom: 15,
        style: {
          fontFamily: 'Lato, Arial',
        },
        type: this.props.chartType,
      },
      credits: {
        enabled: false,
      },
      customProps: {
        idiom: this.props.idiom,
        tooltipMarginRight: this.props.tooltipMarginRight,
        tooltipFormatter: this.props.tooltipFormatter,
      },
      legend: {
        enabled: this.props.isLegendEnabled,
        itemStyle: {
          color: '#888',
          fontWeight: 'bold',
        },
        itemHoverStyle: {
          color: '#ccc',
        },
        itemHiddenStyle: {
          color: '#000',
        },
      },
      plotOptions: {
        bar: {
          dataLabels: {
            enabled: this.props.enableDataLabels,
            style: {
              color: '#fff',
              fontFamily: 'arial',
              fontWeight: 'normal',
              textOutline: 'none',
            },
          },
          borderColor: '#fff',
        },
        pie: {
          allowPointSelect: true,
          dataLabels: {
            enabled: this.props.enableDataLabels,
            style: {
              color: '#fff',
              fontFamily: 'arial',
              fontWeight: 'normal',
              textOutline: 'none',
            },
          },
          showInLegend: this.props.isLegendEnabled,
        },
        column: {
          borderColor: '#fff',
          dataLabels: {
            enabled: this.props.enableDataLabels,
            style: {
              color: '#fff',
              fontFamily: 'arial',
              fontWeight: 'normal',
              textOutline: 'none',
            },
          },
        },
        series: {
          animation: isAnimated,
          borderColor: this.props.chartType === 'column' ? '#ffffff' : null,
          cursor: onPointClick ? 'pointer' : 'default',
          events: {
            click: onPointClick,
          },
          negativeColor: '#ff5568',
          stacking: 'normal',
          states: {
            hover: {
              enabled: true,
              borderColor: '#fff',
              color: '#00cfff',
            },
          },
        },
      },
      series: this.props.series,
      title: {
        text: '',
      },
      tooltip: {
        backgroundColor: '#eee',
        borderWidth: 0,
        formatter(tooltipObj) {
          if (
            this.series.chart.userOptions.customProps.tooltipFormatter !== undefined &&
            this.series.chart.userOptions.customProps.tooltipFormatter !== null &&
            this.series.chart.userOptions.customProps.tooltipFormatter !== ''
          ) {
            if (Array.isArray(this.series.chart.userOptions.customProps.tooltipFormatter)) {
              const seriesIndex = this.point.series.index
              return this.series.chart.userOptions.customProps.tooltipFormatter[seriesIndex](tooltipObj, this.point)
            }
            return this.series.chart.userOptions.customProps.tooltipFormatter(tooltipObj, this.point)
          }
          return tooltipObj.defaultFormatter.call(this, tooltipObj)
        },
        shadow: false,
        shape: 'square',
        positioner() {
          const x = this.chart.plotWidth - tooltipMarginRight
          const y = this.chart.plotHeight - tooltipYPositionSubtract
          return { x, y }
        },
        useHTML:
          this.props.tooltipFormatter !== undefined &&
          this.props.tooltipFormatter !== null &&
          this.props.tooltipFormatter !== '',
      },
      xAxis: {
        categories: this.props.categories,
        labels: {
          style: {
            fontWeight: 'bold',
            color: '#fff',
          },
          step: 1,
          rotation: 0,
        },
        lineColor: theme.legacy.colors.primary.primary,
        title: {
          text: null,
        },
      },
      yAxis: [
        {
          gridLineColor: '#665',
          gridLineDashStyle: 'Dash',
          lineColor: theme.legacy.colors.primary.primary,
          lineWidth: 1,
          maxPadding: 0,
          labels: {
            style: {
              color: '#fff',
            },
          },
          title: {
            text: this.props.yAxisTitle,
            margin: 15,
            style: {
              color: '#888',
            },
          },
        },
      ],
    }

    if (this.props.isLegendEnabled === true) {
      chartOptions.chart.spacingBottom = 0
    }

    if (this.props.yAxis !== null) {
      chartOptions.yAxis = this.props.yAxis
    }

    if (this.props.chartType === 'line') delete chartOptions.tooltip.positioner

    if (this.props.chartPadding) {
      delete chartOptions.chart.spacingBottom
      chartOptions.chart.spacing = this.props.chartPadding
    }

    // for bar chart, only blue bars
    if (this.props.chartType === 'bar' || this.props.chartType === 'column') {
      chartOptions.colors = [chartMainColor]
    }

    this.setState({
      isLoading: this.props.series[0].data === null,
      options: chartOptions,
    })
  }

  // EVENTS

  onChartCreated = (chart) => {
    const setChartSize = () => {
      const chartWidth = this.getChartWidth()
      const chartHeight = this.getChartHeight()

      try {
        chart.setSize(chartWidth, chartHeight)
      } catch (e) {
        // do nothing
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', setChartSize)
    }
  }

  // HELPERS

  getChartHeight = () => {
    let chartHeight = 300
    const elementsByClass = [...document.getElementsByClassName(this.props.chartsWrapperCssClass)]
    if (elementsByClass && elementsByClass.length > 0) {
      for (let i = 0; i < elementsByClass.length; i += 1) {
        const element = elementsByClass[i]
        if (!element.classList.contains('hidden')) {
          chartHeight = element.clientHeight - 52
          break
        }
      }
    }
    return chartHeight
  }

  getChartWidth = () => {
    let chartWidth = 400
    const elementsByClass = [...document.getElementsByClassName(this.props.chartsWrapperCssClass)]
    elementsByClass.every((element) => {
      if (!element.classList.contains('hidden')) {
        const chartWrapper = element.children[0] // chart-wrapper child to get the width
        if (chartWrapper) {
          chartWidth = chartWrapper.clientWidth - 10
        }
        return false
      }
      return true
    })

    return chartWidth
  }

  // top margin
  getTooltipYPositionSubtract = () => {
    const defaultMarginTopColumnBar = 30
    const defaultMarginTopColumn = 150

    if (this.props.chartType === 'bar' || this.props.chartType === 'pie') {
      if (!this.hasValue(this.props.tooltipFormatter)) return defaultMarginTopColumnBar
      const count = (this.props.tooltipFormatter.toString().match(/<tr>/g) || []).length
      if (count === 1) return defaultMarginTopColumnBar
      return count * 25
    }

    if (this.props.chartType === 'column') {
      if (!this.hasValue(this.props.tooltipFormatter)) return defaultMarginTopColumn
      const count = (this.props.tooltipFormatter.toString().match(/<tr>/g) || []).length
      if (count === 1) return defaultMarginTopColumn
      return defaultMarginTopColumn - count * 5
    }

    return null
  }

  // right margin
  getTooltipXPositionSubtract = () => {
    const defaultMarginRightBar = 150
    const defaultMarginRightColumn = 200
    const defaultMarginRightSlim = 0

    if (this.props.tooltipMarginRight) return this.props.tooltipMarginRight
    if (this.props.categories === null) {
      return this.props.chartType === 'bar' ? defaultMarginRightBar : defaultMarginRightColumn
    }

    if (this.props.chartType === 'bar' || this.props.chartType === 'pie') {
      if (this.props.isFullWidth) return defaultMarginRightBar

      let maxLength = 0
      this.props.categories.forEach((category) => {
        if (category.length > maxLength) maxLength = category.length
      })

      if (maxLength > 25) return defaultMarginRightSlim
      return defaultMarginRightBar
    }

    if (this.props.chartType === 'column') {
      return this.props.tooltipMarginRight ? this.props.tooltipMarginRight : defaultMarginRightColumn
    }

    return null
  }

  hasSeriesItem = () => {
    return this.props.series[0].data && Array.isArray(this.props.series[0].data) && this.props.series[0].data.length > 0
  }

  getIsAnimated = () => {
    if (this.props.enableAnimation === true) {
      if (!this.props.chartHasLoaded) {
        return { duration: 1500 }
      }
      return false
    }
    return false
  }

  // RENDERS

  renderChartTitle = () => {
    return (
      <span className="chart-title">
        {this.state.isLoading && this.props.hiddenTitleWhileLoading
          ? `${i18n.t('globals.loading')}...`
          : this.props.mainTitle}
      </span>
    )
  }

  renderChartOrientation = () => {
    if (this.state.isLoading) return null

    return this.props.showOrientationDrop === true ? (
      <div className="chart-orientation">
        <select onChange={this.props.onChartOrientationChange} value={this.props.chartType}>
          <option value="bar">Horizontal</option>
          <option value="column">Vertical</option>
        </select>
      </div>
    ) : (
      ''
    )
  }

  renderGenericDropdown = () => {
    const { genericDropdownItems, onGenericDropdownChange, genericDropdownId } = this.props
    if (!genericDropdownItems) return null

    return (
      <div className="generic-dropdown">
        <select onChange={onGenericDropdownChange} value={genericDropdownId}>
          {genericDropdownItems &&
            genericDropdownItems.map((i) => (
              <option key={i} value={i.value}>
                {i.text}
              </option>
            ))}
        </select>
      </div>
    )
  }

  render() {
    if (this.props.isHidden) return null

    return (
      <div className={`chart-wrapper ${this.props.isFullWidth ? 'full-width' : ''}`}>
        {this.renderChartTitle()}
        {this.renderChartOrientation()}
        {this.renderGenericDropdown()}

        {!this.state.isLoading ? (
          !this.hasSeriesItem() ? (
            <p className="no-info-available">{i18n.t('noResultsFound')}</p>
          ) : (
            <HighchartsReact
              highcharts={Highcharts}
              options={this.state.options}
              containerProps={{ style: { width: '100%' } }}
              callback={this.onChartCreated}
            />
          )
        ) : (
          <Loading isSimple customCssClass="chart" />
        )}
      </div>
    )
  }
}

GenericChart.defaultProps = {
  chartsWrapperCssClass: 'charts-wrapper',
  chartType: 'bar',
  enableAnimation: true,
  enableDataLabels: true,
  hiddenTitleWhileLoading: false,
  idiom: i18n.language,
  isFullWidth: false,
  isHidden: false,
  isLegendEnabled: false,
  mainTitle: 'Chart title',
  showOrientationDrop: false,
  yAxisTitle: '',
}

export default GenericChart

/*
RULES

LOADING RULES
when this.props.series[0].data === null: show a loading icon
when this.props.series[0].data !== null and length === 0: show message: no info available
when this.props.series[0].data !== null and length > 0: loads the chart

HEIGHT/WIDTH RULES
The chart will find the 'charts-wrapper' parent div and apply its height/width to the chart
If the chart had to find another parent div, use the prop 'chartsWrapperCssClass' to change de default logic
So, the height/width is controlled by the css and applied in the chart dinamically
*/
