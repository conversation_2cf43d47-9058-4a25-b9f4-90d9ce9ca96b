import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios'
import { http } from '@mziq-platform/toolkit'
import storeInformation from 'utils/storeInformation'

export const api = http(
  axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
  }),
  {
    unauthorizedBypassRoutes: ['/customers/users/logto/authentication', '/customers/users/logout'],
  }
) as AxiosInstance

const HEADERS = {
  Accept: 'application/json',
  'Content-Type': 'application/json',
}

const injectAuthorization = (config: InternalAxiosRequestConfig) => {
  const token = storeInformation.getCore2Token()

  const headerTokens = token
    ? {
        Authorization: `Bearer ${token}`,
      }
    : {}

  return Object.assign(config, {
    headers: {
      ...HEADERS,
      ...config.headers,
      ...headerTokens,
    },
  })
}

api.interceptors.request.use(injectAuthorization)
