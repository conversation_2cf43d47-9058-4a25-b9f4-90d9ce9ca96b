import { DefaultMonitoredError } from 'pages/monitoring/errors'
import { api, MZ_IRM_NEW } from '../../api'
import { ExportDailyPosition, ExportDailyPositionPromise } from './types'

export const onExportDailyPosition = async (props: ExportDailyPosition): Promise<ExportDailyPositionPromise> => {
  const { companyId, tickerId, referenceDateStart, referenceDateEnd, language, classification } = props
  try {
    const response = await api.post(
      `${MZ_IRM_NEW}/companies/${companyId}/tickers/${tickerId}/shareholders/monitored/positions/export`,
      {
        referenceDateStart,
        referenceDateEnd,
        language,
        classification,
      }
    )
    return response.data
  } catch (err) {
    throw DefaultMonitoredError
  }
}
