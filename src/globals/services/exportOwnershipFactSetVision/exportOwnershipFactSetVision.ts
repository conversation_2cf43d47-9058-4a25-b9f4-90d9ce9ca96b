import { MZ_IRM_NEW, api } from 'globals/api'
import { ExportOwnershipFactSetVisionError } from 'pages/ownership/errors'
import { ExportCurrentShareholderFactSetParams } from './types'

export const exportOwnershipFactSetVision = async (params: ExportCurrentShareholderFactSetParams) => {
  const { tickerId, portfolioId, referenceDate, search, order, limit, language } = params
  const uri = `${MZ_IRM_NEW}/tickers/${tickerId}/portfolios/${portfolioId}/albert/positions/export`

  try {
    const response = await api.post(uri, {
      referenceDate,
      search,
      order,
      limit,
      language,
    })
    return response.data
  } catch (err) {
    throw ExportOwnershipFactSetVisionError
  }
}
