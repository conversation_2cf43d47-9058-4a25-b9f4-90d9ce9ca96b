import { Navigate, Route, Routes } from 'react-router-dom'
import { FirmDetails, ContactDetails } from '@mz-codes/mz-page-package'

import { PATH } from 'consts'
import {
  Ownership,
  Summary,
  ExportHistory,
  Shareholders,
  ShareholderReport,
  DailyPosition,
  InterestGroup,
  Charts,
  CompanyKeyStatistics,
  ShareholderBase,
  TickerHistory,
  AlbertOverview,
  GroupedOverview,
  SimpleOverview,
  PositionNinetyDays,
  SmartGrouping,
  Logto,
  LogtoCallback,
  Logout,
} from 'pages'
import { AuthLayout } from './auth-layout'

export function AppRoutes() {
  return (
    <Routes>
      <Route index path="/logout" element={<Logout />} />
      <Route index path="/" element={<Logto />} />
      <Route index path="/logto/callback" element={<LogtoCallback />} />
      <Route path={PATH} element={<AuthLayout />}>
        <Route index element={<Navigate to="summary" replace />} />
        <Route path="summary" element={<Summary />} />
        <Route path="keystatistics" element={<CompanyKeyStatistics />} />
        <Route path="ownership" element={<Ownership />} />
        <Route path="shareholders" element={<Shareholders />} />
        <Route path="reports" element={<ShareholderReport />} />
        <Route path="ownership/:id/simple/overview" element={<SimpleOverview />} />
        <Route path="ownership/:id/grouped/overview" element={<GroupedOverview />} />
        <Route path="ownership/:id/albert/overview" element={<AlbertOverview />} />
        <Route path="monitoring/dailyPosition" element={<DailyPosition />} />
        <Route path="monitoring/interestGroup" element={<InterestGroup />} />
        <Route path="monitoring/ninetyDayPositions" element={<PositionNinetyDays />} />
        <Route path="history/ticker" element={<TickerHistory />} />
        <Route path="history/exports" element={<ExportHistory />} />
        <Route path="history/uploads" element={<ShareholderBase />} />
        <Route path="charts" element={<Charts />} />
        <Route path="summary/smartGrouping" element={<SmartGrouping />} />
        <Route path="group/:groupId" element={<FirmDetails />} />
        <Route path="contact/:id" element={<ContactDetails />} />
      </Route>
    </Routes>
  )
}
