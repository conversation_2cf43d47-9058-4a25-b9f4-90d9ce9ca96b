import { type PropsWithChildren } from 'react'
import { Navigate } from 'react-router-dom'
import storeInformation from 'utils/storeInformation'

type ProtectedRouteProps = PropsWithChildren

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const isUserLogged = !(storeInformation.getCore2Token() == null)

  if (!isUserLogged) {
    return <Navigate to="/" replace />
  }

  return children
}
