import { ReactNode } from 'react'

import { render } from '@testing-library/react'
import { ThemeProvider } from 'styled-components'

import { theme } from '@mz-codes/design-system'

export function customRender(children: ReactNode) {
  const { rerender, ...rest } = render(<ThemeProvider theme={theme}>{children}</ThemeProvider>)

  const customRerender = (customChildren: ReactNode) => {
    return rerender(<ThemeProvider theme={theme}>{customChildren}</ThemeProvider>)
  }

  return {
    rerender: customRerender,
    ...rest,
  }
}
