import { env } from '../../env'

export const mockStoreInformation = {
  getCore2Token: () => 'mock-token',
  getEnvironment: () => (env.IS_DEV ? 'local' : 'production'),
  getStoreId: () => 'mock-store-id',
  getStoreName: () => 'mock-store-name',
  clear: () => {},
  setCore2UserId: () => {},
  setCore2Token: () => {},
  setCore2UserProfiles: () => {},
  setCore2UserCustomizations: () => {},
  setUserEmail: () => {},
  setUserInformation: () => {},
  setUserPasswordExpiration: () => {},
  setCore2UserCustomers: () => {},
  setCore2UserCustomersNumber: () => {},
  setUserId: () => {},
}
