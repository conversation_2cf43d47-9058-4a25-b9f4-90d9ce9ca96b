import { api } from 'globals/api'
import { MZ_IRM_V2, MZ_IRM_DEALOGIC, MZ_IRM_NEW, MZ_LEGACY_CORE } from '../globals/api/prefixes'

export const getTickers = async (companyId) =>
  (await api.get(`${MZ_IRM_NEW}/management/company/${companyId}/ticker`)).data

export const uploadShareholderBase = async (companyId, payload, config = {}) => {
  api.defaults.timeout = 30 * 60 * 1000
  return (await api.post(`${MZ_IRM_NEW}/positionBatch/company/${companyId}/uploadBase`, payload, config)).data
}

export const createTask = async (companyId, body) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/task`
  const res = await api.post(uri, body)
  return res.data
}

export const vinculateContactWithTask = async (taskId, contactId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/task/${taskId}/contact/${contactId}`
  const res = await api.put(uri)
  return res.data
}

export const vinculateTaskWithIntitution = async (companyId, taskId, institutionId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/task/${taskId}/institution/${institutionId}`
  const res = await api.put(uri, null)
  return res.data
}

export const vinculateTaskWithGroup = async (companyId, taskId, shareholderGroupId, groupName) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/task/${taskId}/shareholderGroup/${shareholderGroupId}`
  const res = await api.put(uri, { name: groupName })
  return res.data
}

export const getCurrentShareholderBase = async (
  companyId,
  tickerId,
  referenceDate,
  limit,
  searchTerm,
  shareholderType,
  order,
  viewGrouped,
  fromAlbert,
  groupedType,
  classificationId
) => {
  const { data } = await api.get(`${MZ_IRM_NEW}/position/companies/${companyId}/positions/tickers/${tickerId}`, {
    params: {
      limit,
      order,
      fromAlbert,
      groupedType,
      referenceDate,
      displayName: searchTerm,
      shareholderType,
      viewGrouped,
      classificationId,
    },
  })

  return data
}

export const getCurrentShareholderAlbertBase = async (
  companyId,
  tickerId,
  referenceDate,
  limit,
  searchTerm,
  shareholderType,
  order,
  viewGrouped
) =>
  (
    await api.post(
      `${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/albert/position`,
      { referenceDate, displayName: searchTerm, shareholderType, viewGrouped },
      { params: { limit, order } }
    )
  ).data

export const getCurrentShareholderChildPosition = async (
  companyId,
  tickerId,
  referenceDate,
  groupId,
  limit,
  omitZeroed = true
) =>
  (
    await api.post(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/shareholderGroup/${groupId}/position`, {
      referenceDate,
      limit,
      omitZeroed,
    })
  ).data

export const getCurrentShareholderAlbertChildPosition = async (companyId, tickerId, referenceDate, groupId) =>
  (
    await api.post(
      `${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/albert/shareholderGroup/${groupId}/position`,
      { referenceDate }
    )
  ).data

export const getCurrentShareholderFactSetBase = async (
  companyId,
  tickerId,
  userId,
  portfolioId,
  referenceDate,
  limit,
  searchTerm = ''
) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/user/${userId}/portfolio/${portfolioId}/albert/position?referenceDate=${referenceDate}&limit=${limit}&displayName=${searchTerm}`
  const { data } = await api.get(uri)
  return data
}

export const getCurrentShareholderFactSetChildrenPosition = async (
  companyId,
  tickerId,
  userId,
  portfolioId,
  shareholderGroupId,
  referenceDate
) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/user/${userId}/portfolio/${portfolioId}/albert/shareholderGroup/${shareholderGroupId}/position?referenceDate=${referenceDate}`
  const { data } = await api.get(uri)
  return data
}

export const getCompanyCurrentPortfolio = async (companyId, userId) => {
  const uri = `${MZ_IRM_DEALOGIC}/portfolio/company/${companyId}/user/${userId}`
  const { data } = await api.get(uri)
  return data
}

export const createGroup = async (companyId, name) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholderGroup`
  const res = await api.post(uri, { name })
  return res.data
}

export const createShareholder = async (companyId, name, shareholderType, documentType, document) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholder`
  const res = await api.post(uri, {
    name,
    shareholderType,
    documentType,
    document,
  })
  return res.data
}

export const deleteShareholderGroup = async (companyId, shareholderGroupId) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholderGroup/${shareholderGroupId}`
  const res = await api.delete(uri)
  return res.data
}

export const exportShareholderGroup = async ({
  firmId,
  tickerId,
  mzTickerId,
  startDate,
  endDate,
  interactions,
  language,
  myCompanyAxisType,
  interactionType,
  generalInteractionType,
  portfolioId,
}) => {
  const uri = `${MZ_IRM_V2}/firm/${firmId}/report/export`
  const body = {
    tickerId,
    mzTickerId,
    startDate,
    endDate,
    interactions,
    language,
    myCompanyAxisType,
    interactionType,
    generalInteractionType,
    portfolioId,
  }
  const res = await api.post(uri, body)

  return res.data
}

export const getShareholders = async (companyId, viewType, searchTerm, type, quantity) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/${viewType}/search?top=${quantity}`
  const body = {
    searchTerms: searchTerm,
    type,
  }
  const res = await api.post(uri, body)
  return res.data
}

export const getShareholdersNew = async (companyId, viewType, search, offset, quantity, type, groupedType) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/${viewType}?groupedType=${groupedType}&search=${search}&type=${type}&_offset=${offset}&_limit=${quantity}`
  const res = await api.get(uri)
  return res.data
}

export const searchShareholders = async (companyId, shareholderGroupId, searchTerm) => {
  const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/group/${shareholderGroupId}/members/search`
  const res = await api.post(uri, { searchTerms: searchTerm })
  return res.data
}

export const searchAlbertShareholders = async (companyId, shareholderGroupId, searchTerm) => {
  const uri = `${MZ_IRM_NEW}/albertTearsheet/company/${companyId}/group/${shareholderGroupId}/members/search`
  const res = await api.post(uri, { searchTerms: searchTerm })
  return res.data
}

export const createGroupAndGroupShareholder = async (
  companyId,
  groupName,
  shareholderDocument,
  shareholderDocumentType
) =>
  (
    await api.post(`${MZ_IRM_NEW}/company/${companyId}/createGroupAndGroupShareholder`, {
      groupName,
      shareholderDocument,
      shareholderDocumentType,
    })
  ).data

export const groupShareholder = async (companyId, shareholderGroupId, shareholderDocument, shareholderDocumentType) =>
  (
    await api.post(`${MZ_IRM_NEW}/company/${companyId}/shareholdergroup/${shareholderGroupId}/groupShareholder`, {
      shareholderDocument,
      shareholderDocumentType,
    })
  ).data

export const ungroup = async (companyId, shareholderGroupId, shareholderId) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholderGroup/${shareholderGroupId}/shareholder/${shareholderId}/unlink`
  const res = await api.post(uri)
  return res.data
}

export const getGroups = async (companyId) =>
  (await api.get(`${MZ_IRM_NEW}/company/${companyId}/shareholdergroup`)).data

export const getTopHolders = async ({
  companyId,
  tickerId = null,
  referenceDate,
  limit,
  shareholderType,
  viewType,
  groupedType,
}) => {
  const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-holders`

  const params = {
    tickerId,
    limit,
    shareholderType,
    viewType,
    groupedType,
    referenceDate,
  }

  const { data: response } = await api.get(url, { params })

  return response
}

export const getTopHoldersMovement = async ({
  companyId,
  tickerId = null,
  referenceDateStart,
  referenceDateEnd,
  limit,
  fromAlbert,
  shareholderType,
  viewType,
  groupedType,
}) => {
  const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-holders-movement`

  const params = {
    tickerId,
    limit,
    fromAlbert,
    shareholderType,
    viewType,
    groupedType,
    referenceDateStart,
    referenceDateEnd,
  }

  const { data: response } = await api.get(url, { params })

  return response
}

export const getTopBuyers = async ({
  companyId,
  tickerId = null,
  referenceDateStart,
  referenceDateEnd,
  limit,
  shareholderType,
  viewType,
  groupedType,
}) => {
  const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-buyers`

  const params = {
    tickerId,
    limit,
    shareholderType,
    viewType,
    groupedType,
    referenceDateStart,
    referenceDateEnd,
  }

  const { data: response } = await api.get(url, { params })

  return response
}

export const getTopSellers = async ({
  companyId,
  tickerId = null,
  referenceDateStart,
  referenceDateEnd,
  limit,
  fromAlbert,
  shareholderType,
  viewType,
  groupedType,
}) => {
  const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-sellers`

  const params = {
    tickerId,
    limit,
    fromAlbert,
    shareholderType,
    viewType,
    groupedType,
    referenceDateStart,
    referenceDateEnd,
  }

  const { data: response } = await api.get(url, { params })

  return response
}

export const getZeroedOut = async ({
  companyId,
  tickerId = null,
  referenceDateStart,
  referenceDateEnd,
  limit,
  fromAlbert,
  shareholderType,
  viewType,
  groupedType,
}) => {
  const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-zeroed`

  const params = {
    tickerId,
    limit,
    fromAlbert,
    shareholderType,
    viewType,
    groupedType,
    referenceDateStart,
    referenceDateEnd,
  }

  const { data: response } = await api.get(url, { params })

  return response
}

export const saveClosingPrice = async (companyId, tickerId, closingDate, closingPrice) =>
  (
    await api.post(`${MZ_IRM_NEW}/management/company/${companyId}/ticker/${tickerId}/closingPrice`, {
      closingDate,
      closingPrice,
      currency: 'BRL',
    })
  ).data

export const getShareholdersGroupedByCountry = async (companyId, tickerId, referenceDate) =>
  (await api.post(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/report/byCountry`, { referenceDate })).data

export const getShareholdersGroupedByType = async (companyId, tickerId, referenceDate) =>
  (await api.post(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/report/byShareholderType`, { referenceDate }))
    .data

export const getShareholdersGroupedBySumCountry = async (companyId, tickerId, referenceDate) =>
  (
    await api.post(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/report/bySumStocksPerCountry`, {
      referenceDate,
    })
  ).data

export const getShareholdersGroupedBySumType = async (companyId, tickerId, referenceDate) =>
  (
    await api.post(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/report/bySumStocksPerShareholderType`, {
      referenceDate,
    })
  ).data

export const getTopNewHolders = async ({
  companyId,
  tickerId,
  referenceDateStart,
  referenceDateEnd,
  limit,
  fromAlbert,
  shareholderType,
  viewType,
  groupedType,
}) => {
  const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-new-holders`

  const params = {
    tickerId,
    limit,
    fromAlbert,
    shareholderType,
    viewType,
    groupedType,
    referenceDateStart,
    referenceDateEnd,
  }

  const { data: response } = await api.get(url, { params })

  return response
}

export const getTopVariation = async ({
  companyId,
  tickerId = null,
  referenceDateStart,
  referenceDateEnd,
  limit,
  order,
  fromAlbert,
  shareholderType,
  viewType,
  groupedType,
}) => {
  const url = `${MZ_IRM_NEW}/position/companies/${companyId}/top-reports/top-variation`

  const params = {
    tickerId,
    limit,
    order,
    fromAlbert,
    shareholderType,
    viewType,
    groupedType,
    referenceDateStart,
    referenceDateEnd,
  }

  const response = await api.get(url, { params })

  return response.data
}

export const getComplianceAvailableDays = async (companyId, tickerId, referenceDateStart, referenceDateEnd) =>
  (
    await api.post(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/report/compliance/days`, {
      referenceDateStart,
      referenceDateEnd,
    })
  ).data

export const exportCurrentShareholderBase = async (companyId, tickerId, referenceDate) => {
  const { data } = await api.post(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/position`, {
    referenceDate,
  })

  return data
}

export const exportCurrentShareholderAlbertBase = async (
  companyId,
  tickerId,
  referenceDate,
  limit,
  searchTerm,
  shareholderType,
  order,
  viewGrouped
) =>
  (
    await api.post(
      `${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/albert/position`,
      { referenceDate, name: searchTerm, shareholderType, viewGrouped },
      { params: { limit, order, exportExcel: 'true' } }
    )
  ).data

export const getExportedFile = async (reportId) =>
  api.get(`${MZ_IRM_NEW}/reports/${reportId}/file`, {
    responseType: 'arraybuffer',
  })

export const getNationalityPercentage = async (companyId, tickerId, referenceDate) =>
  (
    await api.get(
      `${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/tickers/${tickerId}/percentage-nationality`,
      {
        params: { referenceDate },
      }
    )
  ).data

export const getTopShareholderByCountry = async (companyId, tickerId, referenceDate, limit) =>
  (
    await api.get(`${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/top-holders-per-country`, {
      params: { limit, referenceDate, tickerId },
    })
  ).data

export const getTopBuyersByCountry = async (companyId, tickerId, referenceDate, limit) => {
  const { data } = await api.get(
    `${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/top-buyers-per-country`,
    {
      params: { limit, referenceDate, tickerId },
    }
  )

  return data
}

export const getTopSellersByCountry = async (companyId, tickerId, referenceDate, limit) =>
  (
    await api.get(`${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/top-sellers-per-country`, {
      params: { limit, referenceDate, tickerId },
    })
  ).data

export const getSummaryTopBuyers = async (companyId, tickerId, referenceDate, limit, viewType) =>
  (
    await api.get(`${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/top-buyers`, {
      params: {
        limit,
        referenceDate,
        viewType,
        tickerId,
      },
    })
  ).data

export const getSummaryPosition = async (companyId, tickerId, shareholderGroupId, referenceDate, fromAlbert, orderBy) =>
  (
    await api.get(
      `${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/shareholder-group/${shareholderGroupId}/position`,
      { params: { referenceDate, fromAlbert, orderBy, tickerId } }
    )
  ).data

export const getSummaryTopSellers = async (companyId, tickerId, referenceDate, limit, viewType) => {
  const { data } = await api.get(`${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/top-sellers`, {
    params: {
      limit,
      referenceDate,
      viewType,
      tickerId,
    },
  })

  return data
}

export const getSummaryTopHolders = async (companyId, tickerId, referenceDate, limit, viewType) => {
  const { data } = await api.get(`${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/top-holders`, {
    params: {
      limit,
      referenceDate,
      viewType,
      tickerId,
    },
  })

  return data
}

export const generateSummaryReport = async ({
  companyId,
  quantity,
  referenceDateEnd,
  referenceDateStart,
  tickerId,
  viewType,
}) => {
  const { status } = await api.post(`${MZ_IRM_NEW}/position/companies/${companyId}/summary-report/export`, {
    quantity,
    referenceDateEnd,
    referenceDateStart,
    tickerId,
    viewType,
  })
  return { success: status === 204 }
}

export const getNoteTags = async (companyId, noteId) => {
  const uri = `${MZ_LEGACY_CORE}/company/${companyId}/note/${noteId}/tags`
  const res = await api.get(uri)
  return res.data
}

export const createNoteTag = async (companyId, noteId, body) => {
  const uri = `${MZ_LEGACY_CORE}/company/${companyId}/note/${noteId}/tag`
  const res = await api.post(uri, body)
  return res.data
}

export const getTags = async (companyId) => {
  const uri = `${MZ_LEGACY_CORE}/company/${companyId}/tags`
  const res = await api.get(uri)
  return res.data
}
export const updateNote = async (
  companyId,
  noteId,
  note,
  dealogicContactId,
  contactId,
  shareholderId,
  shareholderGroupId,
  dealogicFundId,
  dealogicInstitutionId
) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/note`
  const res = await api.post(uri, {
    noteTitle: note.title,
    noteContent: note.note,
    isPrivate: note.isPrivate,
    dealogicContactId,
    dealogicFundId,
    dealogicInstitutionId,
    id: noteId,
    contactId,
    shareholderId,
    shareholderGroupId,
  })
  return res.data
}
export const createNote = async (
  companyId,
  note,
  dealogicContactId,
  contactId,
  shareholderId,
  shareholderGroupId,
  dealogicFundId,
  dealogicInstitutionId
) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/note`

  const res = await api.post(uri, {
    noteTitle: note.title,
    noteContent: note.note,
    isPrivate: note.isPrivate,
    dealogicContactId: contactId ? null : dealogicContactId,
    dealogicFundId,
    dealogicInstitutionId,
    contactId,
    shareholderId,
    shareholderGroupId,
  })
  return res.data
}

export const getInstitutionTearSheet = async (companyId, shareholderId) => {
  const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/shareholder/${shareholderId}`
  const res = await api.get(uri)
  return res.data
}

export const getContactsFromFund = async (companyId, shareholderId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/shareholder/${shareholderId}/contacts`
  const res = await api.get(uri)
  return res.data
}

export const getTasksFromFund = async (companyId, shareholderId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/shareholder/${shareholderId}/tasks`
  const res = await api.get(uri)
  return res.data
}

export const vinculateContactWithFund = async (companyId, contactId, shareholderId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/contact/${contactId}/shareholder`
  const res = await api.post(uri, { shareholderId })
  return res.data
}

export const deleteContactFromFund = async (companyId, contactId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/contact/${contactId}/shareholder`
  const res = await api.delete(uri)
  return res.data
}

export const vinculateTaskWithFund = async (companyId, taskId, shareholderId, name) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/task/${taskId}/shareholder/${shareholderId}`
  const res = await api.put(uri, { name })
  return res.data
}

export const deleteTask = async (companyId, id) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/task/${id}`
  const res = await api.delete(uri)
  return res.data
}

export const updateShareholderDisplayName = async (companyId, shareholderId, displayName) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholder/${shareholderId}/displayName`
  const res = await api.post(uri, { displayName })
  return res.data
}

export const getIntegrationsBindForShareholder = async (shareholderGroupId) => {
  const uri = `${MZ_IRM_V2}/integrations/bind?targetEntity=shareholder_group&targetEntityId=${shareholderGroupId}`
  const res = await api.get(uri)
  return res.data
}

export const removeIntegrationsBind = async (companyId, targetEntity, targetEntityId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/integrationsbind/remove?targetEntity=${targetEntity}&targetEntityId=${targetEntityId}`
  const res = await api.delete(uri)
  return res.data
}

export const getInstitutionInfo = async (companyId, investorId) => {
  const uri = `${MZ_IRM_DEALOGIC}/elastic/company/${companyId}/public/investor/${investorId}`
  const res = await api.get(uri)
  return res.data
}

export const getInstitutionGroupedTearSheetNew = async (
  companyId,
  tickerId,
  shareholderGroupId,
  startDate,
  endDate,
  search,
  offset,
  limit
) => {
  const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/ticker/${tickerId}/group/${shareholderGroupId}/info?startDate=${startDate}&endDate=${endDate}&_offset=${offset}&_limit=${limit}&search=${search}`
  const res = await api.get(uri)
  return res.data
}

export const getContactsFromGroup = async (companyId, shareholderGroupId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/shareholderGroup/${shareholderGroupId}/contacts`
  const res = await api.get(uri)
  return res.data
}

export const getTasksFromGroup = async (companyId, shareholderGroupId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/shareholderGroup/${shareholderGroupId}/tasks`
  const res = await api.get(uri)
  return res.data
}

export const vinculateContactWithGroup = async (companyId, contactId, shareholderGroupId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/contact/${contactId}/shareholderGroup`
  const res = await api.post(uri, {
    shareholderGroupId,
  })
  return res.data
}

export const deleteContactFromGroup = async (companyId, contactId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/contact/${contactId}/shareholderGroup`
  const res = await api.delete(uri)
  return res.data
}

export const searchShareholderByName = async (companyId, name) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholder/search`
  const res = await api.post(uri, { displayName: name })
  return res.data
}

export const vinculateShareholderWithGroup = async (companyId, shareholderGroupId, shareholderId) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholderGroup/${shareholderGroupId}/shareholder/${shareholderId}/associate`
  const res = await api.post(uri)
  return res.data
}

export const unlinkShareholderFromGroup = async (companyId, shareholderGroupId, shareholderId) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholderGroup/${shareholderGroupId}/shareholder/${shareholderId}/unlink`
  const res = await api.post(uri)
  return res.data
}

export const updateShareholderGroupDisplayName = async (companyId, shareholderGroupId, displayName) => {
  const uri = `${MZ_IRM_NEW}/company/${companyId}/shareholderGroup/${shareholderGroupId}/name`
  const res = await api.post(uri, { name: displayName })
  return res.data
}

export const vinculateDealogicInvestorWithGroup = async (companyId, shareholderId, dealogicInstitutionId) => {
  const uri = `${MZ_IRM_NEW}/dealogicMapping/company/${companyId}/shareholderGroup/${shareholderId}/institution/${dealogicInstitutionId}`
  const res = await api.post(uri)
  return res.data
}

export const getInstitutionTopPeers = async (peers, investorId) => {
  const uri = `${MZ_IRM_DEALOGIC}/institution/company/institution/${investorId}/peers`
  const res = await api.post(uri, { peers })
  return res.data
}

export const getInstitutionCountries = async (investorId) => {
  const uri = `${MZ_IRM_DEALOGIC}/institution/${investorId}/investments/country`
  let { data } = await api.get(uri)
  data = data.data

  const res = { data: [] }
  const max = data.length > 5 ? 5 : data.length
  for (let i = 0; i < max; i += 1) {
    res.data.push({
      countryName: data[i].Name,
      marketValueUsd: data[i].Total,
      percentage: data[i].Percentage,
    })
  }

  return res
}

export const getInstitutionHistory = async (listingId, investorId) => {
  const uri = `${MZ_IRM_DEALOGIC}/institution/company/${listingId}/institutions/${investorId}/history`
  const res = await api.get(uri)
  return res.data
}

export const getInstitutionSectors = async (investorId) => {
  const uri = `${MZ_IRM_DEALOGIC}/institution/${investorId}/investments/sector`
  let { data } = await api.get(uri)
  data = data.data

  const res = { data: [] }
  for (let i = 0; i < data.length; i += 1) {
    res.data.push({
      sector: data[i].Name,
      marketValueUsd: data[i].Total,
      percentage: data[i].Percentage,
    })
  }

  return res
}

export const getInstitutionGroupedTearSheetAlbert = async (companyId, shareholderGroupId) => {
  const uri = `${MZ_IRM_NEW}/albertTearsheet/company/${companyId}/groupAlbert/${shareholderGroupId}`
  const res = await api.get(uri)
  return res.data
}

export const getAlbertGroupPositionHistoryOnCompany = async (
  companyId,
  tickerId,
  shareholderGroupId,
  startDate,
  endDate
) => {
  const uri = `${MZ_IRM_NEW}/tearsheet/companies/${companyId}/position-history/tickers/${tickerId}/group/${shareholderGroupId}/albert`
  const { data } = await api.get(uri, {
    params: { startDate, endDate },
  })
  return data
}

export const savePageAccessLog = async (companyId, pageName, pageUrl) => {
  const uri = `${MZ_LEGACY_CORE}/analytics/company/${companyId}/entry`
  try {
    const res = await api.post(uri, {
      pageName,
      pageRoute: pageUrl,
    })
    return res.data
  } catch (e) {
    return null
  }
}

export const getListings = async (companyId) => {
  const uri = `${MZ_IRM_DEALOGIC}/listing/${companyId}`

  const { data } = await api.get(uri)
  return data
}

export const getCompanyInfo = async (companyId, publicCompanyMzId) => {
  const uri = `${MZ_IRM_DEALOGIC}/elastic/company/${companyId}/dealogic/${publicCompanyMzId}`

  const { data } = await api.get(uri)
  return data
}

export const getEstimatesHighlights = async (tickerId) => {
  const uri = `${MZ_IRM_DEALOGIC}/listing/${tickerId}/estimates/highlights`

  const { data } = await api.get(uri)
  return data
}

export const getDistributionByStyle = async (tickerId) => {
  const uri = `${MZ_IRM_DEALOGIC}/institution/${tickerId}/group/investorStyle`

  const { data } = await api.get(uri)
  return data
}

export const getDistributionByTurnover = async (tickerId) => {
  const uri = `${MZ_IRM_DEALOGIC}/institution/${tickerId}/group/investorTurnover`

  const { data } = await api.get(uri)
  return data
}

/**
 * GET Report Group Detaled
 * This function not work for TopHolders
 * for TopHolders use getCurrentShareholderChildPosition
 * @param {*} companyID
 * @param {*} tickerID
 * @param {*} shareholderGroupID
 * @param {*} referenceDateStart
 * @param {*} referenceDateEnd
 * @param {*} reportType
 * @returns
 */
export const getReportGroupDetailed = async (
  companyID,
  tickerID,
  shareholderGroupID,
  referenceDateStart,
  referenceDateEnd,
  reportType,
  fromAlbert,
  limit,
  omitZeroed = false
) => {
  const params = {
    referenceDateStart,
    referenceDateEnd,
    reportType,
    fromAlbert,
    limit,
    omitZeroed,
  }
  const uri = `${MZ_IRM_NEW}/company/${companyID}/ticker/${tickerID}/shareholderGroup/${shareholderGroupID}/report/detailed`
  const res = await api.get(uri, { params })
  return res.data
}

export const getDailyPositionClassifications = async (companyId) => {
  const uri = `${MZ_IRM_NEW}/companies/${companyId}/shareholders/monitored/classifications`
  const res = await api.get(uri)
  return res.data?.data || []
}

// ---------------------------- IRM V2 ----------------------------

// FIRM

export const getFirmByEntity = async (entityId, origin) => {
  const uri = `${MZ_IRM_V2}/firm`
  let payload = null

  switch (origin) {
    case 3:
      payload = {
        institutionId: entityId,
      }
      break
    case 4:
      payload = {
        publicCompanyId: entityId,
      }
      break
    case 5:
      payload = {
        stakeholderId: entityId,
      }
      break
    default:
      payload = {
        institutionId: entityId,
      }
  }

  if (payload === null) {
    return null
  }

  const { data } = await api.get(uri, {
    params: payload,
  })

  return data
}

export const createFirmByEntity = async (entityId, entityName, origin) => {
  const uri = `${MZ_IRM_V2}/firm/candidates`
  const payload = {
    id: entityId,
    name: entityName,
    origin,
  }
  const { data } = await api.post(uri, payload)
  return {
    id: data.data.id,
    name: entityName,
  }
}

export const generateChartsReport = async ({ companyId, tickerId, referenceDate }) => {
  const { data } = await api.post(`${MZ_IRM_NEW}/company/${companyId}/ticker/${tickerId}/report/export`, {
    referenceDate,
  })

  return data
}
