import { api } from 'globals/api'
import { MZ_IRM_NEW } from '../globals/api/prefixes'

const downloadGroupedHistoryPosition = async ({
  companyId,
  tickerId,
  shareholderGroupId,
  referenceDateStart,
  referenceDateEnd,
}) => {
  const uri = `${MZ_IRM_NEW}/tearsheet/company/${companyId}/ticker/${tickerId}/group/${shareholderGroupId}/position`
  const res = await api.post(uri, { referenceDateStart, referenceDateEnd })
  return res.data
}

const downloadAlbertHistoryPosition = async ({
  companyId,
  tickerId,
  shareholderGroupId,
  referenceDateStart,
  referenceDateEnd,
}) => {
  const uri = `${MZ_IRM_NEW}/albertTearsheet/company/${companyId}/ticker/${tickerId}/group/${shareholderGroupId}/position?referenceDateStart=${referenceDateStart}&referenceDateEnd=${referenceDateEnd}&exportExcel=true`
  const res = await api.post(uri, { referenceDateStart, referenceDateEnd })
  return res.data
}

const downloadSimpleHistoryPosition = async ({
  companyId,
  tickerId,
  shareholderId,
  referenceDateStart,
  referenceDateEnd,
}) => {
  const uri = `${MZ_IRM_NEW}/tearsheet/companies/${companyId}/position-history/tickers/${tickerId}/shareholders/${shareholderId}`
  const res = await api.post(uri, {
    startDate: referenceDateStart,
    endDate: referenceDateEnd,
  })
  return res.data
}

export { downloadGroupedHistoryPosition, downloadSimpleHistoryPosition, downloadAlbertHistoryPosition }
