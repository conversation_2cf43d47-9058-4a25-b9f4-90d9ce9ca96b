import { api } from 'globals/api'
import { MZ_LEGACY_CORE, MZ_IRM_DEALOGIC } from '../globals/api/prefixes'

export const getMyContactsCore = async (companyId, searchTerms) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/contact/mine/search`
  const res = await api.post(uri, { searchTerms })
  return res.data
}

export const getTags = async (companyId, tags) => {
  const uri = `${MZ_LEGACY_CORE}/company/${companyId}/tags/search`
  const res = await api.post(uri, { tags })
  return res.data
}

export const postSaveContactAsFavorite = async (companyId, contactId) => {
  const uri = `${MZ_LEGACY_CORE}/platform/company/${companyId}/contact/importFromPublicContact/${contactId}`
  const res = await api.post(uri, null)
  return res.data
}

// INTELLIGENCE / DEALOGIC

export const getContacts = async (companyId, searchTerms, searchMode) => {
  const uri = `${MZ_IRM_DEALOGIC}/elastic/company/${companyId}/public/contacts?size=500`
  const res = await api.post(uri, {
    searchTerms,
    searchMode,
  })
  return res.data
}

export const getInstitutions = async (companyId, searchTerms, searchMode) => {
  const uri = `${MZ_IRM_DEALOGIC}/elastic/company/${companyId}/institutions?pageSize=1000`
  const res = await api.post(uri, {
    searchTerms,
    searchMode,
  })
  return res.data
}

export const getInstitutionsInternal = async (companyId, searchTerms) => {
  const uri = `${MZ_IRM_DEALOGIC}/elastic/company/${companyId}/institutions/search?pageSize=1000`
  const res = await api.post(uri, { searchTerms })
  return res.data
}

export default {
  getContacts,
  getInstitutions,
  getInstitutionsInternal,
  getMyContactsCore,
  getTags,
  postSaveContactAsFavorite,
}
