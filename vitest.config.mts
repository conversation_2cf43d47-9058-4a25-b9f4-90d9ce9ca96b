// <reference types="vitest" />
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import os from 'node:os'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': '/src',
      assets: '/src/assets',
      client: '/src/client',
      consts: '/src/consts',
      components: '/src/components',
      errors: '/src/errors',
      hooks: '/src/hooks',
      contexts: '/src/contexts',
      globals: '/src/globals',
      pages: '/src/pages',
      translate: '/src/translate',
      types: '/src/types',
      utils: '/src/utils',
      routes: '/src/routes',
      'shareholders-types': '/src/types/shareholders',
      test: '/src/__tests__',
      '@mz-mfe-navbar/app': '/src/__tests__/mocks/navbar',
    },
  },
  test: {
    globals: true,
    pool: 'threads',
    poolOptions: {
      threads: {
        maxThreads: os.availableParallelism(),
      },
    },
    include: ['src/**/*.spec.tsx'],
    environment: 'jsdom',
    coverage: {
      provider: 'v8',
      reporter: ['lcov', 'html'],
      reportsDirectory: 'coverage',
      all: true,
      include: ['**/src/**/*.template.tsx', '**/src/**/*.component.tsx'],
    },
    setupFiles: './vitest.setup.ts',
  },
})
