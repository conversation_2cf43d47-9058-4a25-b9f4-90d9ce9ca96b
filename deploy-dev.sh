set -e

# Variables
IMAGE_TAG=latest
IMAGE_NAME=localhost:5000/mz-mf-shareholders
CONFIG_MAP_NAME=mz-mf-shareholders-config

# Build and push
docker build --target=dev --secret id=github-token,env=GITHUB_TOKEN --build-arg ENV_FILE=./.env.mzstack -f ./Dockerfile -t ${IMAGE_NAME}:${IMAGE_TAG} .
docker push ${IMAGE_NAME}:${IMAGE_TAG}

# Deploy
kubectl delete configmap ${CONFIG_MAP_NAME} -n default || true
kubectl create configmap ${CONFIG_MAP_NAME} --from-env-file=.env.mzstack
kubectl apply -f ./deploy-dev.yml
