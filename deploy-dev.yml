apiVersion: v1
kind: Service
metadata:
  name: mz-mf-shareholders
spec:
  selector:
    app: mz-mf-shareholders
  ports:
    - port: 3005
      targetPort: 3005

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mz-mf-shareholders
spec:
  rules:
    - host: mz-mf-shareholders.local
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mz-mf-shareholders
                port:
                  number: 3005
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mz-mf-shareholders
  namespace: default
spec:
  selector:
    matchLabels:
      app: mz-mf-shareholders
  template:
    metadata:
      labels:
        app: mz-mf-shareholders
    spec:
      containers:
        - name: mz-mf-shareholders
          image: localhost:5000/mz-mf-shareholders:latest
          args: ['start']
          envFrom:
            - configMapRef:
                name: mz-mf-shareholders-config
          ports:
            - containerPort: 80
          resources:
            limits:
              cpu: 1
              memory: 3000Mi
            requests:
              cpu: 500m
              memory: 300Mi
          volumeMounts:
            - mountPath: /home/<USER>/application
              name: source
      volumes:
        - name: source
          hostPath:
            path: /projects/mz-mf-shareholders/application
            type: Directory
