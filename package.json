{"name": "shareholders", "version": "2.0.0", "description": "Product Shareholders by MZiQ", "main": "index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "bump:all": "npm run bump:dev && npm run bump:prod", "bump:dev": "ncu --upgrade --dep dev --target minor", "bump:prod": "ncu --upgrade --dep prod --target minor", "eslint:check": "eslint . --cache --cache-location ./.eslint-cache --cache-strategy content", "eslint:fix": "npm run eslint:check -- --fix", "husky:install": "cd .. && npx husky init", "jscpd": "jscpd -t 12 --blame --ignore '**/*.min.js,**/*.map,**/*.svg' ./src", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "start": "vite serve", "serve": "vite preview --port 3005 --strictPort", "pre-commit": "npm run codestyle:check", "codestyle:check": "npm run eslint:check && npm run prettier:check", "codestyle:fix": "npm run eslint:fix && npm run prettier:fix", "translation:check": "i18n-unused display-unused && i18n-unused display-missed", "translation:unused": "i18n-unused display-unused", "translation:fix": "i18n-unused remove-unused", "translation:mark": "i18n-unused mark-unused", "translation:missed": "i18n-unused display-missed", "test": "vitest --coverage --silent=false"}, "author": "", "license": "ISC", "dependencies": {"@datadog/browser-rum": "^6.8.0", "@loadable/component": "^5.16.3", "@logto/react": "^4.0.7", "@microsoft/clarity": "^1.0.0", "@module-federation/vite": "^1.1.5", "@mz-codes/design-system": "^2.1.4", "@mz-codes/mz-auth-kit": "^2.1.1", "@mz-codes/mz-page-package": "^1.0.3", "@mziq-platform/toolkit": "^1.0.13", "@types/styled-components": "^5.1.34", "axios": "^1.7.7", "date-fns": "^2.28.0", "highcharts": "^11.4.8", "highcharts-react-official": "^2.1.3", "html-react-parser": "^3.0.1", "i18next": "^23.10.1", "i18next-browser-languagedetector": "^7.2.0", "moment": "^2.29.4", "prop-types": "^15.7.2", "query-string": "^7.0.0", "react": "^18.3.1", "react-collapse": "^5.1.0", "react-datepicker": "^4.8.0", "react-dom": "^18.3.1", "react-dropdown": "^1.9.2", "react-dropzone": "^11.3.2", "react-error-boundary": "^4.1.2", "react-modal": "^3.13.1", "react-paginate": "^8.2.0", "react-router-dom": "^6.28.0", "react-select": "^5.8.0", "react-tooltip": "^4.2.19", "styled-components": "^6.1.13", "valtio": "^2.1.2", "zod": "^3.23.8"}, "devDependencies": {"@playwright/test": "^1.34.3", "@t3-oss/env-core": "^0.11.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.17.32", "@types/react": "^18.2.67", "@types/react-datepicker": "^4.11.2", "@types/react-dom": "^18.2.22", "@types/react-modal": "^3.16.0", "@types/react-paginate": "^7.1.1", "@types/react-router-dom": "^5.1.20", "@types/react-select": "^5.0.1", "@types/styled-components": "^5.1.34", "@types/testing-library__jest-dom": "^5.14.9", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.1.2", "eslint": "^8.47.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.0.11", "i18n-unused": "^0.14.0", "jest-styled-components": "^7.2.0", "jsdom": "^22.1.0", "npm-check-updates": "^16.10.12", "prettier": "^3.0.2", "sass": "^1.81.0", "typescript": "^5.6.3", "vite": "^6.3.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}