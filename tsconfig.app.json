{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "allowJs": true, "paths": {"@/*": ["./src/*"], "assets": ["./src/assets/index"], "assets/*": ["./src/assets/*"], "client": ["./src/client/index"], "client/*": ["./src/client/*"], "consts": ["./src/consts/index"], "consts/*": ["./src/consts/*"], "components": ["./src/components/index"], "components/*": ["./src/components/*"], "errors": ["./src/errors/index"], "hooks": ["./src/hooks/index"], "hooks/*": ["./src/hooks/*"], "contexts": ["./src/contexts/index"], "globals/*": ["./src/globals/*"], "pages": ["./src/pages/index"], "pages/*": ["./src/pages/*"], "services": ["./src/services/index"], "services/*": ["./src/services/*"], "types": ["./src/types/index"], "types/*": ["./src/types/*"], "translate": ["./src/translate/index"], "utils": ["./src/utils/index"], "utils/*": ["./src/utils/*"], "routes": ["./src/routes/index"], "shareholders-types": ["./src/types/shareholders.ts"], "test": ["./src/__tests__/index"]}, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "noImplicitAny": true, "types": ["node", "vitest", "@testing-library/jest-dom"]}, "include": ["src", "vitest.setup.ts", "src/__tests__"]}