## O que esse PR faz?

Explique seu PR para humanos.

## Recomendações para testar/rodar/validar o código

Como eu posso ver as modificações funcionando.

## Links relevantes (issues/docs/jira)

Se tiver algum.

## Checklist para o Review

- [ ] Sem _códigos mortos_ ou _comentários inúteis_
- [ ] DRY - _Don't Repeat Yourself_
- [ ] Fluxos alternativos e _exceções capturadas_
- [ ] Sem problemas de desempenho
- [ ] Sem problemas de segurança
- [ ] Sem breaking changes
