name: Code quality checks

on:
  pull_request:
    branches:
      - main
      - dev
      - stg
    paths-ignore:
      - README.md
      - .github/dependabot.yml

  workflow_dispatch:

permissions:
  contents: read
  packages: read

jobs:
  run-code-quality-checks:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set YEAR-MONTH cache prefix
        id: year-month
        run: echo "value=$(date +'%Y-%m')" >> $GITHUB_OUTPUT

      - name: Setup NodeJS
        uses: actions/setup-node@v4
        with:
          cache: npm
          cache-dependency-path: 'package-lock.json'
          node-version: '20'
          registry-url: https://npm.pkg.github.com
          always-auth: true
          scope: '@${{ github.repository_owner }}'

      - name: Cache Node modules
        id: node-modules-cache
        uses: actions/cache@v4
        with:
          path: 'node_modules'
          key: node-modules-cache-${{ steps.year-month.outputs.value }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            node-modules-cache-

      - name: <PERSON><PERSON> Webpack
        uses: actions/cache@v4
        with:
          path: '.webpack-cache'
          key: webpack-cache-${{ steps.year-month.outputs.value }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            webpack-cache-

      - name: Cache BabelJS
        uses: actions/cache@v4
        with:
          path: '.babel-cache'
          key: babel-cache-${{ steps.year-month.outputs.value }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            babel-cache-

      - name: Cache ESLint
        uses: actions/cache@v4
        with:
          path: '.eslint-cache'
          key: eslint-cache-${{ steps.year-month.outputs.value }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            eslint-cache-

      - name: Cache Prettier
        uses: actions/cache@v4
        with:
          path: '.prettier-cache'
          key: prettier-cache-${{ steps.year-month.outputs.value }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            prettier-cache-

      - name: Install dependencies
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check code style
        run: npm run codestyle:check

      - name: Check webpack compilation
        run: npm run build
        env:
          IS_DEPLOYMENT: 'true'
