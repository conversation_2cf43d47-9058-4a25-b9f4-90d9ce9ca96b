name: Create staging environment

on:
  pull_request_review:
    types:
      - submitted

jobs:
  create-staging-branch:
    if: github.event.review.state == 'approved' && github.event.pull_request.base.ref == 'dev'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Create staging branch
        run: |
          git checkout -b ${{ github.ref_name }}-stg-${{ github.run_number }}
          git push origin ${{ github.ref_name }}-stg-${{ github.run_number }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  create-staging-pr:
    needs: create-staging-branch
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Create pull request to stg
        run: gh pr create -B stg -H "${{ github.ref_name }}-stg-${{ github.run_number }}" --title "Merge ${{ github.event.pull_request.title }} STG" --body "Created by Github action for PR ${{ github.event.pull_request.html_url }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
