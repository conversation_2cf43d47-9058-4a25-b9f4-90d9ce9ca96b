name: Deploy site to CloudFront

on:
  push:
    branches:
      - main
      - stg

permissions:
  id-token: write
  contents: read
  packages: read

env:
  AWS_S3_BUCKET: ${{ github.ref_name == 'main' && 'mz-prd-pub-mziq-shareholders' || 'mz-stg-pub-mziq-shareholders' }}
  CLOUDFRONT_DIST_ID: ${{ github.ref_name == 'main' && 'E2AZONEW18IKOJ' || 'E22NWH2HAIBWSU' }}

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup NodeJS
        uses: actions/setup-node@v4
        with:
          cache: npm
          cache-dependency-path: './package-lock.json'
          node-version: '20'
          registry-url: https://npm.pkg.github.com
          always-auth: true
          scope: '@${{ github.repository_owner }}'

      - name: Install dependencies
        run: npm install
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Build site
        run: npm run build -- --mode $BUILD_MODE
        env:
          IS_DEPLOYMENT: 'true'
          NODE_ENV: production
          BUILD_MODE: ${{ github.ref_name == 'main' && 'production' || 'staging' }}

      - name: Configure AWS Credentials
        uses: 'aws-actions/configure-aws-credentials@v3.0.1'
        with:
          aws-region: ${{ secrets.AWS_REGION }}
          role-to-assume: ${{ secrets.ROLE_GITHUB_ACTIONS_ARN }}
          role-session-name: rol-ecr-push-images

      - name: Remove old site dist
        run: aws s3 rm --recursive "s3://${AWS_S3_BUCKET}"

      - name: Sync new site dist
        run: aws s3 sync ./dist "s3://${AWS_S3_BUCKET}"

      - name: Invalidate CloudFront cache
        run: aws cloudfront create-invalidation --distribution-id "${CLOUDFRONT_DIST_ID}" --paths '/*'
