# mz-shareholders-mf

O micro-frontend React do produto Shareholders, construído com Vite e Module Federation.

## Arquitetura

O projeto utiliza uma arquitetura moderna baseada em:

- Vite como bundler principal
- Module Federation para integração com outros micro-frontends
- React Router v6 para navegação
- TypeScript para tipagem estática
- Vitest para testes unitários

### Estrutura do Projeto

```
src/
├── components/     # Componentes React reutilizáveis
├── pages/         # Páginas da aplicação
├── routes/        # Configuração de rotas
├── types/         # Definições de tipos TypeScript
├── utils/         # Utilitários e helpers
├── __tests__/     # Testes unitários
└── assets/        # Recursos estáticos
```

## Requisitos

- Node.js v20.18.3 ou superior
- npm 9.x ou superior

## Configuração Inicial

### 1. Instalação de Dependências

```sh
npm install
```

### 2. Desenvolvimento

Para iniciar o servidor de desenvolvimento:

```sh
npm run dev
```

O projeto estará disponível em `http://localhost:3005`.

### 3. Build

Para criar uma build de produção:

```sh
npm run build
```

### 4. Testes

Para executar os testes:

```sh
npm test
```

Para executar os testes em modo watch:

```sh
npm run test:watch
```

### 5. Navbar

Rode a `mz-navbar-sub-mf` localmente ou ajuste na .env.local para apontar para a navbar de stg

Em navbar:

```sh
npm start
```

## Module Federation

O projeto utiliza Module Federation para compartilhar dependências com outros micro-frontends. As dependências compartilhadas incluem:

- React
- React DOM
- React Router DOM

## Testes

O projeto utiliza Vitest para testes unitários. Algumas práticas importantes:

- Testes devem estar no diretório `__tests__` ou ter o sufixo `.spec.tsx`
- Utilize `customRender` para renderizar componentes com providers necessários
- Para testes de navegação, use `MemoryRouter` com `Routes` e `Route`

Exemplo de teste:

```typescript
import { describe, expect, it } from 'vitest'
import { customRender } from 'test'
import { MemoryRouter, Routes, Route } from 'react-router-dom'

describe('Component', () => {
  it('should render correctly', () => {
    const { getByText } = customRender(
      <MemoryRouter>
        <Component />
      </MemoryRouter>
    )
    expect(getByText('Content')).toBeInTheDocument()
  })
})
```

## Autenticação

O projeto utiliza o sistema de autenticação logTo. Para acessar o ambiente local:

1. Acesse o ambiente de desenvolvimento
2. Faça login normalmente com suas credenciais
3. Retorne para o endereço localhost

## Troubleshooting

### Problemas com Aliases

Se encontrar problemas com aliases de importação:

1. Verifique se o `tsconfig.app.json` está configurado corretamente
2. Certifique-se de que o plugin `vite-tsconfig-paths` está funcionando
3. Certifique-se de estar utilizando a versão do workspace do typescript no vscode
4. Reinicie o servidor de desenvolvimento

### Problemas com Module Federation

Se encontrar problemas com Module Federation:

1. Verifique se as URLs dos remotes estão corretas
2. Confirme se as versões das dependências compartilhadas são compatíveis
3. Limpe o cache do navegador e reinicie o servidor
